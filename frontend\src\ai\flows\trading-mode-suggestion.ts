// src/ai/flows/trading-mode-suggestion.ts
'use server';

/**
 * @fileOverview This file implements the Trading Mode Suggestion AI agent.
 *
 * - suggestTradingMode - A function that suggests the most suitable trading mode.
 * - TradingModeSuggestionInput - The input type for the suggestTradingMode function.
 * - TradingModeSuggestionOutput - The return type for the suggestTradingMode function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const TradingModeSuggestionInputSchema = z.object({
  riskTolerance: z
    .string()
    .describe(
      'The user risk tolerance, can be low, medium, or high.'
    ),
  preferredCryptocurrencies: z
    .string()
    .describe('The user preferred cryptocurrencies, comma separated.'),
  investmentGoals: z
    .string()
    .describe('The user investment goals, such as long term investment or short term profit.'),
});
export type TradingModeSuggestionInput = z.infer<typeof TradingModeSuggestionInputSchema>;

const TradingModeSuggestionOutputSchema = z.object({
  suggestedMode: z
    .enum(['Simple Spot', 'Stablecoin Swap'])
    .describe('The suggested trading mode.'),
  reason: z.string().describe('The reason for the suggestion.'),
});
export type TradingModeSuggestionOutput = z.infer<typeof TradingModeSuggestionOutputSchema>;

export async function suggestTradingMode(
  input: TradingModeSuggestionInput
): Promise<TradingModeSuggestionOutput> {
  return suggestTradingModeFlow(input);
}

const prompt = ai.definePrompt({
  name: 'tradingModeSuggestionPrompt',
  input: {schema: TradingModeSuggestionInputSchema},
  output: {schema: TradingModeSuggestionOutputSchema},
  prompt: `You are an expert in trading mode selection. You will suggest the most suitable trading mode (Simple Spot or Stablecoin Swap) based on the user's risk tolerance, preferred cryptocurrencies, and investment goals.

Risk Tolerance: {{{riskTolerance}}}
Preferred Cryptocurrencies: {{{preferredCryptocurrencies}}}
Investment Goals: {{{investmentGoals}}}

Consider the following:

*   Simple Spot Mode is suitable for users who are comfortable with higher risk and are looking for short term profits.
*   Stablecoin Swap Mode is suitable for users who are risk averse and are looking for long term investment.

Based on the information above, suggest a trading mode and explain your reasoning.`,
});

const suggestTradingModeFlow = ai.defineFlow(
  {
    name: 'suggestTradingModeFlow',
    inputSchema: TradingModeSuggestionInputSchema,
    outputSchema: TradingModeSuggestionOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
