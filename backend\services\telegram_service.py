import requests
import logging
import os
from flask import current_app

logger = logging.getLogger(__name__)

class TelegramService:
    """Service for interacting with the Telegram API."""
    
    @staticmethod
    def send_message(chat_id, message):
        """
        Send a message to a specific Telegram chat.
        
        Args:
            chat_id: Telegram chat ID to send the message to
            message: Text message to send
            
        Returns:
            Response from Telegram API
        """
        try:
            token = current_app.config.get('TELEGRAM_BOT_TOKEN')
            if not token:
                logger.warning("Telegram bot token not configured")
                return None
            
            url = f"https://api.telegram.org/bot{token}/sendMessage"
            data = {
                'chat_id': chat_id,
                'text': message,
                'parse_mode': 'HTML'
            }
            
            response = requests.post(url, json=data)
            
            if response.status_code == 200:
                logger.info(f"Message sent to Telegram chat {chat_id}")
                return response.json()
            else:
                logger.error(f"Failed to send Telegram message: {response.text}")
                return None
        except Exception as e:
            logger.error(f"Error sending Telegram message: {str(e)}")
            return None
    
    @staticmethod
    def send_trade_notification(chat_id, trade_data):
        """
        Send a formatted trade notification to a Telegram chat.
        
        Args:
            chat_id: Telegram chat ID to send the notification to
            trade_data: Trade data dictionary with details of the trade
            
        Returns:
            Response from Telegram API
        """
        try:
            emoji = "🟢" if trade_data.get('orderType') == 'BUY' else "🔴"
            crypto1 = trade_data.get('crypto1', 'Crypto')
            crypto2 = trade_data.get('crypto2', 'USDT')
            amount = trade_data.get('amountCrypto1', 0)
            price = trade_data.get('avgPrice', 0)
            value = trade_data.get('valueCrypto2', 0)
            pnl = trade_data.get('realizedProfitLossCrypto2')
            
            # Format the message
            message = f"{emoji} <b>{trade_data.get('orderType', '')} order executed</b>\n\n"
            message += f"Pair: {crypto1}/{crypto2}\n"
            message += f"Amount: {amount:.6f} {crypto1}\n"
            message += f"Price: {price:.6f} {crypto2}\n"
            message += f"Value: {value:.2f} {crypto2}\n"
            
            if pnl is not None:
                profit_emoji = "✅" if pnl > 0 else "❌"
                message += f"PnL: {profit_emoji} {pnl:.2f} {crypto2}\n"
            
            return TelegramService.send_message(chat_id, message)
        except Exception as e:
            logger.error(f"Error sending trade notification: {str(e)}")
            return None
    
    @staticmethod
    def send_bot_status_notification(chat_id, status_data):
        """
        Send a formatted bot status notification to a Telegram chat.
        
        Args:
            chat_id: Telegram chat ID to send the notification to
            status_data: Status data dictionary
            
        Returns:
            Response from Telegram API
        """
        try:
            message = f"🤖 <b>{status_data.get('status', 'Bot Status Update')}</b>\n\n"
            
            if 'config_name' in status_data:
                message += f"Configuration: {status_data.get('config_name')}\n"
                
            if 'message' in status_data:
                message += f"{status_data.get('message')}\n"
                
            if 'details' in status_data and status_data['details']:
                message += f"\nDetails: {status_data.get('details')}\n"
            
            return TelegramService.send_message(chat_id, message)
        except Exception as e:
            logger.error(f"Error sending bot status notification: {str(e)}")
            return None 