#!/usr/bin/env python3
"""
Simple test script to verify backend functionality
"""
import requests
import json
import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

BASE_URL = "http://localhost:5000"

def test_health():
    """Test health endpoint"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        print(f"✅ Health check: {response.status_code} - {response.json()}")
        return True
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_auth():
    """Test authentication"""
    try:
        # Test registration
        register_data = {
            "username": "testuser",
            "password": "testpass123",
            "email": "<EMAIL>"
        }
        
        response = requests.post(f"{BASE_URL}/auth/register", 
                               json=register_data, timeout=5)
        print(f"📝 Registration: {response.status_code}")
        
        # Test login
        login_data = {
            "username": "testuser",
            "password": "testpass123"
        }
        
        response = requests.post(f"{BASE_URL}/auth/login", 
                               json=login_data, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            token = data.get('access_token')
            print(f"✅ Login successful: {response.status_code}")
            return token
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Auth test failed: {e}")
        return None

def test_trading_endpoints(token):
    """Test trading endpoints"""
    if not token:
        print("❌ No token available for trading tests")
        return
        
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        # Test get configs
        response = requests.get(f"{BASE_URL}/trading/config", 
                              headers=headers, timeout=5)
        print(f"📊 Get configs: {response.status_code}")
        
        # Test market data
        response = requests.get(f"{BASE_URL}/trading/market-data/BTC-USDT", 
                              headers=headers, timeout=5)
        print(f"📈 Market data: {response.status_code}")
        
        # Test balances
        response = requests.get(f"{BASE_URL}/trading/balances", 
                              headers=headers, timeout=5)
        print(f"💰 Balances: {response.status_code}")
        
    except Exception as e:
        print(f"❌ Trading endpoints test failed: {e}")

def main():
    print("🚀 Testing Pluto Trading Bot Backend")
    print("=" * 50)
    
    # Test health
    if not test_health():
        print("❌ Backend is not running or not responding")
        return
    
    # Test auth
    token = test_auth()
    
    # Test trading endpoints
    test_trading_endpoints(token)
    
    print("=" * 50)
    print("✅ Backend tests completed")

if __name__ == "__main__":
    main()
