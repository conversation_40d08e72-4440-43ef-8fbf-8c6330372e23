import os
import sys
import unittest
import tempfile
from flask import json

# Add parent directory to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
from app import create_app
from db import db

class BasicTestCase(unittest.TestCase):
    
    def setUp(self):
        self.db_fd, self.db_path = tempfile.mkstemp()
        self.app = create_app(testing=True)
        self.app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{self.db_path}'
        self.app.config['TESTING'] = True
        self.client = self.app.test_client()
        
        with self.app.app_context():
            db.create_all()
    
    def tearDown(self):
        os.close(self.db_fd)
        os.unlink(self.db_path)
    
    def test_app_exists(self):
        """Test that the app exists"""
        self.assertIsNotNone(self.app)
    
    def test_app_is_testing(self):
        """Test that the app is in testing mode"""
        self.assertTrue(self.app.config['TESTING'])
    
    def test_register_and_login(self):
        """Test user registration and login"""
        # Register new user
        register_response = self.client.post('/auth/register', json={
            'username': 'testuser',
            'password': 'testpassword',
            'email': '<EMAIL>'
        })
        register_data = json.loads(register_response.data)
        
        self.assertEqual(register_response.status_code, 201)
        self.assertEqual(register_data['message'], 'User registered successfully')
        self.assertIn('access_token', register_data)
        
        # Login with the new user
        login_response = self.client.post('/auth/login', json={
            'username': 'testuser',
            'password': 'testpassword'
        })
        login_data = json.loads(login_response.data)
        
        self.assertEqual(login_response.status_code, 200)
        self.assertEqual(login_data['message'], 'Login successful')
        self.assertIn('access_token', login_data)
        
        # Get user info
        access_token = login_data['access_token']
        user_response = self.client.get('/auth/me', 
                                        headers={'Authorization': f'Bearer {access_token}'})
        user_data = json.loads(user_response.data)
        
        self.assertEqual(user_response.status_code, 200)
        self.assertEqual(user_data['user']['username'], 'testuser')
        self.assertEqual(user_data['user']['email'], '<EMAIL>')


if __name__ == '__main__':
    unittest.main() 