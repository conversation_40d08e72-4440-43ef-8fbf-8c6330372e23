"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx":
/*!********************************************************!*\
  !*** ./src/components/layout/TradingConfigSidebar.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TradingConfigSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/TargetPriceModal */ \"(app-pages-browser)/./src/components/modals/TargetPriceModal.tsx\");\n/* harmony import */ var _components_modals_AlarmConfigModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/AlarmConfigModal */ \"(app-pages-browser)/./src/components/modals/AlarmConfigModal.tsx\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var _components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/crypto-input */ \"(app-pages-browser)/./src/components/ui/crypto-input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power-off.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n// Force refresh of stablecoins list\nconst STABLECOINS_LIST = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n\n\n\nfunction TradingConfigSidebar() {\n    _s();\n    // Safely get trading context with error handling\n    let tradingContext;\n    try {\n        tradingContext = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    } catch (error) {\n        console.error('Trading context not available:', error);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n            className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"Trading context not available. Please refresh the page.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this);\n    }\n    const { config, dispatch, botSystemStatus, appSettings, setTargetPrices: contextSetTargetPrices } = tradingContext;\n    const isBotActive = botSystemStatus === 'Running';\n    const isBotWarmingUp = botSystemStatus === 'WarmingUp';\n    // AI context removed as requested\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    const [isTargetModalOpen, setIsTargetModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAlarmModalOpen, setIsAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // AI Suggestion form state removed\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        let val;\n        if (type === 'checkbox') {\n            val = checked;\n        } else if (type === 'number') {\n            // Handle empty string or invalid numbers gracefully\n            if (value === '' || value === null || value === undefined) {\n                val = 0;\n            } else {\n                const parsed = parseFloat(value);\n                val = isNaN(parsed) ? 0 : parsed;\n            }\n        } else {\n            val = value;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: val\n            }\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: value\n            }\n        });\n        if (name === 'crypto1') {\n            // Reset crypto2 if new crypto1 doesn't support current crypto2\n            const validQuotes = _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_QUOTES_SIMPLE[value] || DEFAULT_QUOTE_CURRENCIES || [\n                \"USDT\",\n                \"USDC\",\n                \"BTC\"\n            ];\n            if (!config.crypto2 || !Array.isArray(validQuotes) || !validQuotes.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: validQuotes[0] || 'USDT'\n                    }\n                }); // Ensure crypto2 gets a valid default\n            }\n        }\n    };\n    const handleCrypto1Selection = (crypto)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto1: crypto\n            }\n        });\n        // Reset crypto2 based on trading mode when crypto1 changes\n        if (config.tradingMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (_lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO1 || [\n                \"BTC\",\n                \"ETH\",\n                \"BNB\",\n                \"SOL\",\n                \"LINK\",\n                \"AVAX\",\n                \"DOT\",\n                \"UNI\",\n                \"NEAR\",\n                \"AAVE\",\n                \"ATOM\",\n                \"VET\",\n                \"RENDER\",\n                \"POL\",\n                \"ALGO\",\n                \"ARB\",\n                \"FET\",\n                \"PAXG\",\n                \"GALA\",\n                \"CRV\",\n                \"COMP\",\n                \"ENJ\"\n            ]).filter((c)=>c !== crypto);\n            if (!allowedCrypto2.includes(config.crypto2) || config.crypto2 === crypto) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO2 || [\n                \"USDC\",\n                \"DAI\",\n                \"TUSD\",\n                \"FDUSD\",\n                \"USDT\",\n                \"EUR\"\n            ];\n            if (!allowedCrypto2.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        }\n    };\n    const handleCrypto2Selection = (crypto)=>{\n        // In stablecoin swap mode, ensure crypto2 is different from crypto1\n        if (config.tradingMode === \"StablecoinSwap\" && crypto === config.crypto1) {\n            // Don't allow selecting the same crypto as crypto1 in swap mode\n            return;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto2: crypto\n            }\n        });\n    };\n    const handleTradingModeChange = (checked)=>{\n        const newMode = checked ? \"StablecoinSwap\" : \"SimpleSpot\";\n        // Reset crypto2 based on the new trading mode\n        let newCrypto2;\n        if (newMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (_lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO1 || [\n                \"BTC\",\n                \"ETH\",\n                \"BNB\",\n                \"SOL\",\n                \"LINK\",\n                \"AVAX\",\n                \"DOT\",\n                \"UNI\",\n                \"NEAR\",\n                \"AAVE\",\n                \"ATOM\",\n                \"VET\",\n                \"RENDER\",\n                \"POL\",\n                \"ALGO\",\n                \"ARB\",\n                \"FET\",\n                \"PAXG\",\n                \"GALA\",\n                \"CRV\",\n                \"COMP\",\n                \"ENJ\"\n            ]).filter((c)=>c !== config.crypto1);\n            newCrypto2 = allowedCrypto2[0];\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO2 || [\n                \"USDC\",\n                \"DAI\",\n                \"TUSD\",\n                \"FDUSD\",\n                \"USDT\",\n                \"EUR\"\n            ];\n            newCrypto2 = allowedCrypto2[0];\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                tradingMode: newMode,\n                crypto2: newCrypto2\n            }\n        });\n    };\n    const handleIncomeSplitChange = (cryptoKey, value)=>{\n        let percent = parseFloat(value);\n        if (isNaN(percent)) percent = 0;\n        if (percent < 0) percent = 0;\n        if (percent > 100) percent = 100;\n        if (cryptoKey === 'incomeSplitCrypto1Percent') {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto1Percent: percent,\n                    incomeSplitCrypto2Percent: 100 - percent\n                }\n            });\n        } else {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto2Percent: percent,\n                    incomeSplitCrypto1Percent: 100 - percent\n                }\n            });\n        }\n    };\n    // AI suggestion handler removed\n    // AI suggestion effect removed\n    // Use static crypto lists\n    const crypto1Options = _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS || [];\n    const crypto2Options = config.tradingMode === \"SimpleSpot\" ? _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_QUOTES_SIMPLE[config.crypto1] || DEFAULT_QUOTE_CURRENCIES || [\n        \"USDT\",\n        \"USDC\",\n        \"BTC\"\n    ] : (_lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS || []).filter((c)=>c !== config.crypto1); // Ensure Crypto2 is not same as Crypto1 in Swap mode\n    // Debug log to see what's actually loaded\n    console.log('🔍 DEBUG: AVAILABLE_CRYPTOS length:', _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS.length);\n    console.log('🔍 DEBUG: crypto1Options length:', crypto1Options.length);\n    console.log('🔍 DEBUG: First 20 cryptos:', _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS.slice(0, 20));\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO1:', _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO1);\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO2:', _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO2);\n    console.log('🔍 DEBUG: AVAILABLE_STABLECOINS:', _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_STABLECOINS);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-sidebar-primary\",\n                    children: \"Trading Configuration\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                className: \"flex-1 pr-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Mode\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                    id: \"enableStablecoinSwap\",\n                                                    checked: config.tradingMode === \"StablecoinSwap\",\n                                                    onCheckedChange: handleTradingModeChange\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"enableStablecoinSwap\",\n                                                    className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n                                                    children: \"Enable Stablecoin Swap Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"Current mode: \",\n                                                config.tradingMode === \"SimpleSpot\" ? \"Simple Spot\" : \"Stablecoin Swap\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        config.tradingMode === \"StablecoinSwap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"preferredStablecoin\",\n                                                    children: \"Preferred Stablecoin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    name: \"preferredStablecoin\",\n                                                    value: config.preferredStablecoin,\n                                                    onValueChange: (val)=>handleSelectChange(\"preferredStablecoin\", val),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            id: \"preferredStablecoin\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select stablecoin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            className: \"max-h-[300px] overflow-y-auto\",\n                                                            children: STABLECOINS_LIST.map((sc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: sc,\n                                                                    children: sc\n                                                                }, sc, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 18\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Pair\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_14__.CryptoInput, {\n                                            label: \"Crypto 1 (Base)\",\n                                            value: config.crypto1,\n                                            allowedCryptos: _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ],\n                                            onValidCrypto: handleCrypto1Selection,\n                                            placeholder: \"e.g., BTC, ETH, SOL\",\n                                            description: \"Enter the base cryptocurrency symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_14__.CryptoInput, {\n                                            label: config.tradingMode === \"StablecoinSwap\" ? \"Crypto 2\" : \"Crypto 2 (Stablecoin)\",\n                                            value: config.crypto2,\n                                            allowedCryptos: config.tradingMode === \"StablecoinSwap\" ? (_lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ]).filter((c)=>c !== config.crypto1) : _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO2 || [\n                                                \"USDC\",\n                                                \"DAI\",\n                                                \"TUSD\",\n                                                \"FDUSD\",\n                                                \"USDT\",\n                                                \"EUR\"\n                                            ],\n                                            onValidCrypto: handleCrypto2Selection,\n                                            placeholder: config.tradingMode === \"StablecoinSwap\" ? \"e.g., BTC, ETH, SOL\" : \"e.g., USDT, USDC, DAI\",\n                                            description: config.tradingMode === \"StablecoinSwap\" ? \"Enter the second cryptocurrency symbol\" : \"Enter the quote/stablecoin symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Parameters\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        [\n                                            {\n                                                name: \"baseBid\",\n                                                label: \"Base Bid (Crypto 2)\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            },\n                                            {\n                                                name: \"multiplier\",\n                                                label: \"Multiplier\",\n                                                type: \"number\",\n                                                step: \"0.001\"\n                                            },\n                                            {\n                                                name: \"numDigits\",\n                                                label: \"Display Digits\",\n                                                type: \"number\",\n                                                step: \"1\"\n                                            },\n                                            {\n                                                name: \"slippagePercent\",\n                                                label: \"Slippage %\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            }\n                                        ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: field.name,\n                                                        children: field.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: field.name,\n                                                        name: field.name,\n                                                        type: field.type,\n                                                        value: config[field.name],\n                                                        onChange: handleInputChange,\n                                                        step: field.step,\n                                                        min: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    children: \"Couple Income % Split (must sum to 100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto1Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto1 || \"Crypto 1\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"incomeSplitCrypto1Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto1Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto1Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 267,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 265,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto2Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto2 || \"Crypto 2\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"incomeSplitCrypto2Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto2Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto2Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 269,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                        className: \"mb-4 bg-sidebar-border\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_16__.cn)(\"text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border\", isBotActive ? \"bg-green-600 text-primary-foreground\" : \"bg-muted text-muted-foreground\"),\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 129\n                                    }, this),\n                                    \"Bot Status: \",\n                                    isBotActive ? 'Running' : isBotWarmingUp ? 'Warming Up' : 'Stopped'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setIsTargetModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Target Prices\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setIsAlarmModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Alarm\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    if (isBotActive) {\n                                        dispatch({\n                                            type: 'SYSTEM_STOP_BOT'\n                                        });\n                                    } else {\n                                        dispatch({\n                                            type: 'SYSTEM_START_BOT_INITIATE'\n                                        });\n                                    }\n                                },\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_16__.cn)('w-full btn-neo', isBotActive || isBotWarmingUp ? 'bg-destructive hover:bg-destructive/90' : 'bg-green-600 hover:bg-green-600/90'),\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 129\n                                    }, this),\n                                    isBotActive ? 'Stop Bot' : isBotWarmingUp ? 'Warming Up...' : 'Start Bot'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    dispatch({\n                                        type: 'SYSTEM_RESET_BOT'\n                                    });\n                                    toast({\n                                        title: \"Bot Reset\",\n                                        description: \"Trading bot has been reset to fresh state. All positions cleared. Saved sessions are preserved.\",\n                                        duration: 4000\n                                    });\n                                },\n                                variant: \"outline\",\n                                className: \"w-full btn-outline-neo\",\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Reset Bot\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_11__.TargetPriceModal, {\n                isOpen: isTargetModalOpen,\n                onClose: ()=>setIsTargetModalOpen(false),\n                onSetTargetPrices: contextSetTargetPrices\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_AlarmConfigModal__WEBPACK_IMPORTED_MODULE_12__.AlarmConfigModal, {\n                isOpen: isAlarmModalOpen,\n                onClose: ()=>setIsAlarmModalOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, this);\n}\n_s(TradingConfigSidebar, \"1Vqtl1Gof3HGb0UJDhvSoNc/2i4=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast\n    ];\n});\n_c = TradingConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TradingConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx\n"));

/***/ })

});