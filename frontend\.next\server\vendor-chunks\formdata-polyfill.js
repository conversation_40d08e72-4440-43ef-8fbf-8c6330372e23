"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/formdata-polyfill";
exports.ids = ["vendor-chunks/formdata-polyfill"];
exports.modules = {

/***/ "(action-browser)/./node_modules/formdata-polyfill/esm.min.js":
/*!***************************************************!*\
  !*** ./node_modules/formdata-polyfill/esm.min.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   File: () => (/* binding */ File),\n/* harmony export */   FormData: () => (/* binding */ FormData),\n/* harmony export */   formDataToBlob: () => (/* binding */ formDataToBlob)\n/* harmony export */ });\n/* harmony import */ var fetch_blob__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fetch-blob */ \"(action-browser)/./node_modules/fetch-blob/index.js\");\n/* harmony import */ var fetch_blob_file_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fetch-blob/file.js */ \"(action-browser)/./node_modules/fetch-blob/file.js\");\n/*! formdata-polyfill. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */\n\n\n\n\nvar {toStringTag:t,iterator:i,hasInstance:h}=Symbol,\nr=Math.random,\nm='append,set,get,getAll,delete,keys,values,entries,forEach,constructor'.split(','),\nf=(a,b,c)=>(a+='',/^(Blob|File)$/.test(b && b[t])?[(c=c!==void 0?c+'':b[t]=='File'?b.name:'blob',a),b.name!==c||b[t]=='blob'?new fetch_blob_file_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]([b],c,b):b]:[a,b+'']),\ne=(c,f)=>(f?c:c.replace(/\\r?\\n|\\r/g,'\\r\\n')).replace(/\\n/g,'%0A').replace(/\\r/g,'%0D').replace(/\"/g,'%22'),\nx=(n, a, e)=>{if(a.length<e){throw new TypeError(`Failed to execute '${n}' on 'FormData': ${e} arguments required, but only ${a.length} present.`)}}\n\nconst File = fetch_blob_file_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]\n\n/** @type {typeof globalThis.FormData} */\nconst FormData = class FormData {\n#d=[];\nconstructor(...a){if(a.length)throw new TypeError(`Failed to construct 'FormData': parameter 1 is not of type 'HTMLFormElement'.`)}\nget [t]() {return 'FormData'}\n[i](){return this.entries()}\nstatic [h](o) {return o&&typeof o==='object'&&o[t]==='FormData'&&!m.some(m=>typeof o[m]!='function')}\nappend(...a){x('append',arguments,2);this.#d.push(f(...a))}\ndelete(a){x('delete',arguments,1);a+='';this.#d=this.#d.filter(([b])=>b!==a)}\nget(a){x('get',arguments,1);a+='';for(var b=this.#d,l=b.length,c=0;c<l;c++)if(b[c][0]===a)return b[c][1];return null}\ngetAll(a,b){x('getAll',arguments,1);b=[];a+='';this.#d.forEach(c=>c[0]===a&&b.push(c[1]));return b}\nhas(a){x('has',arguments,1);a+='';return this.#d.some(b=>b[0]===a)}\nforEach(a,b){x('forEach',arguments,1);for(var [c,d]of this)a.call(b,d,c,this)}\nset(...a){x('set',arguments,2);var b=[],c=!0;a=f(...a);this.#d.forEach(d=>{d[0]===a[0]?c&&(c=!b.push(a)):b.push(d)});c&&b.push(a);this.#d=b}\n*entries(){yield*this.#d}\n*keys(){for(var[a]of this)yield a}\n*values(){for(var[,a]of this)yield a}}\n\n/** @param {FormData} F */\nfunction formDataToBlob (F,B=fetch_blob__WEBPACK_IMPORTED_MODULE_0__[\"default\"]){\nvar b=`${r()}${r()}`.replace(/\\./g, '').slice(-28).padStart(32, '-'),c=[],p=`--${b}\\r\\nContent-Disposition: form-data; name=\"`\nF.forEach((v,n)=>typeof v=='string'\n?c.push(p+e(n)+`\"\\r\\n\\r\\n${v.replace(/\\r(?!\\n)|(?<!\\r)\\n/g, '\\r\\n')}\\r\\n`)\n:c.push(p+e(n)+`\"; filename=\"${e(v.name, 1)}\"\\r\\nContent-Type: ${v.type||\"application/octet-stream\"}\\r\\n\\r\\n`, v, '\\r\\n'))\nc.push(`--${b}--`)\nreturn new B(c,{type:\"multipart/form-data; boundary=\"+b})}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/formdata-polyfill/esm.min.js\n");

/***/ })

};
;