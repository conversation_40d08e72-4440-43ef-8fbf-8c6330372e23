"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/opentracing";
exports.ids = ["vendor-chunks/opentracing"];
exports.modules = {

/***/ "(action-browser)/./node_modules/opentracing/lib/binary_carrier.js":
/*!********************************************************!*\
  !*** ./node_modules/opentracing/lib/binary_carrier.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Convenience class to use as a binary carrier.\n *\n * Any valid Object with a field named `buffer` may be used as a binary carrier;\n * this class is only one such type of object that can be used.\n */\nvar BinaryCarrier = /** @class */ (function () {\n    function BinaryCarrier(buffer) {\n        this.buffer = buffer;\n    }\n    return BinaryCarrier;\n}());\nexports[\"default\"] = BinaryCarrier;\n//# sourceMappingURL=binary_carrier.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9vcGVudHJhY2luZy9saWIvYmluYXJ5X2NhcnJpZXIuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCxrQkFBZTtBQUNmIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxvcGVudHJhY2luZ1xcbGliXFxiaW5hcnlfY2Fycmllci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8qKlxuICogQ29udmVuaWVuY2UgY2xhc3MgdG8gdXNlIGFzIGEgYmluYXJ5IGNhcnJpZXIuXG4gKlxuICogQW55IHZhbGlkIE9iamVjdCB3aXRoIGEgZmllbGQgbmFtZWQgYGJ1ZmZlcmAgbWF5IGJlIHVzZWQgYXMgYSBiaW5hcnkgY2FycmllcjtcbiAqIHRoaXMgY2xhc3MgaXMgb25seSBvbmUgc3VjaCB0eXBlIG9mIG9iamVjdCB0aGF0IGNhbiBiZSB1c2VkLlxuICovXG52YXIgQmluYXJ5Q2FycmllciA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uICgpIHtcbiAgICBmdW5jdGlvbiBCaW5hcnlDYXJyaWVyKGJ1ZmZlcikge1xuICAgICAgICB0aGlzLmJ1ZmZlciA9IGJ1ZmZlcjtcbiAgICB9XG4gICAgcmV0dXJuIEJpbmFyeUNhcnJpZXI7XG59KCkpO1xuZXhwb3J0cy5kZWZhdWx0ID0gQmluYXJ5Q2Fycmllcjtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWJpbmFyeV9jYXJyaWVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/binary_carrier.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/opentracing/lib/constants.js":
/*!***************************************************!*\
  !*** ./node_modules/opentracing/lib/constants.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * The FORMAT_BINARY format represents SpanContexts in an opaque binary\n * carrier.\n *\n * Tracer.inject() will set the buffer field to an Array-like (Array,\n * ArrayBuffer, or TypedBuffer) object containing the injected binary data.\n * Any valid Object can be used as long as the buffer field of the object\n * can be set.\n *\n * Tracer.extract() will look for `carrier.buffer`, and that field is\n * expected to be an Array-like object (Array, ArrayBuffer, or\n * TypedBuffer).\n */\nexports.FORMAT_BINARY = 'binary';\n/**\n * The FORMAT_TEXT_MAP format represents SpanContexts using a\n * string->string map (backed by a Javascript Object) as a carrier.\n *\n * NOTE: Unlike FORMAT_HTTP_HEADERS, FORMAT_TEXT_MAP places no restrictions\n * on the characters used in either the keys or the values of the map\n * entries.\n *\n * The FORMAT_TEXT_MAP carrier map may contain unrelated data (e.g.,\n * arbitrary gRPC metadata); as such, the Tracer implementation should use\n * a prefix or other convention to distinguish Tracer-specific key:value\n * pairs.\n */\nexports.FORMAT_TEXT_MAP = 'text_map';\n/**\n * The FORMAT_HTTP_HEADERS format represents SpanContexts using a\n * character-restricted string->string map (backed by a Javascript Object)\n * as a carrier.\n *\n * Keys and values in the FORMAT_HTTP_HEADERS carrier must be suitable for\n * use as HTTP headers (without modification or further escaping). That is,\n * the keys have a greatly restricted character set, casing for the keys\n * may not be preserved by various intermediaries, and the values should be\n * URL-escaped.\n *\n * The FORMAT_HTTP_HEADERS carrier map may contain unrelated data (e.g.,\n * arbitrary HTTP headers); as such, the Tracer implementation should use a\n * prefix or other convention to distinguish Tracer-specific key:value\n * pairs.\n */\nexports.FORMAT_HTTP_HEADERS = 'http_headers';\n/**\n * A Span may be the \"child of\" a parent Span. In a “child of” reference,\n * the parent Span depends on the child Span in some capacity.\n *\n * See more about reference types at https://github.com/opentracing/specification\n */\nexports.REFERENCE_CHILD_OF = 'child_of';\n/**\n * Some parent Spans do not depend in any way on the result of their child\n * Spans. In these cases, we say merely that the child Span “follows from”\n * the parent Span in a causal sense.\n *\n * See more about reference types at https://github.com/opentracing/specification\n */\nexports.REFERENCE_FOLLOWS_FROM = 'follows_from';\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/constants.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/opentracing/lib/ext/tags.js":
/*!**************************************************!*\
  !*** ./node_modules/opentracing/lib/ext/tags.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/** SPAN_KIND hints at relationship between spans, e.g. client/server */\nexports.SPAN_KIND = 'span.kind';\n/** Marks a span representing the client-side of an RPC or other remote call */\nexports.SPAN_KIND_RPC_CLIENT = 'client';\n/** Marks a span representing the server-side of an RPC or other remote call */\nexports.SPAN_KIND_RPC_SERVER = 'server';\n/** Marks a span representing the producing-side within a messaging system or other remote call */\nexports.SPAN_KIND_MESSAGING_PRODUCER = 'producer';\n/** Marks a span representing the consuming-side within a messaging system or other remote call */\nexports.SPAN_KIND_MESSAGING_CONSUMER = 'consumer';\n/**\n * ERROR (boolean) true if and only if the application considers the operation\n * represented by the Span to have failed\n */\nexports.ERROR = 'error';\n/**\n * COMPONENT (string) ia s low-cardinality identifier of the module, library,\n * or package that is generating a span.\n */\nexports.COMPONENT = 'component';\n/**\n * SAMPLING_PRIORITY (number) determines the priority of sampling this Span.\n * If greater than 0, a hint to the Tracer to do its best to capture the trace.\n * If 0, a hint to the trace to not-capture the trace. If absent, the Tracer\n * should use its default sampling mechanism.\n */\nexports.SAMPLING_PRIORITY = 'sampling.priority';\n// ---------------------------------------------------------------------------\n// PEER_* tags can be emitted by either client-side of server-side to describe\n// the other side/service in a peer-to-peer communications, like an RPC call.\n// ---------------------------------------------------------------------------\n/**\n * PEER_SERVICE (string) Remote service name (for some unspecified\n * definition of \"service\"). E.g., \"elasticsearch\", \"a_custom_microservice\", \"memcache\"\n */\nexports.PEER_SERVICE = 'peer.service';\n/** PEER_HOSTNAME (string) Remote hostname. E.g., \"opentracing.io\", \"internal.dns.name\" */\nexports.PEER_HOSTNAME = 'peer.hostname';\n/**\n * PEER_ADDRESS (string) Remote \"address\", suitable for use in a\n * networking client library. This may be a \"ip:port\", a bare\n * \"hostname\", a FQDN, or even a JDBC substring like \"mysql://prod-db:3306\"\n */\nexports.PEER_ADDRESS = 'peer.address';\n/**\n * PEER_HOST_IPV4 (number) Remote IPv4 address as a .-separated tuple.\n * E.g., \"127.0.0.1\"\n */\nexports.PEER_HOST_IPV4 = 'peer.ipv4';\n// PEER_HOST_IPV6 (string) Remote IPv6 address as a string of\n// colon-separated 4-char hex tuples. E.g., \"2001:0db8:85a3:0000:0000:8a2e:0370:7334\"\nexports.PEER_HOST_IPV6 = 'peer.ipv6';\n// PEER_PORT (number) Remote port. E.g., 80\nexports.PEER_PORT = 'peer.port';\n// ---------------------------------------------------------------------------\n// HTTP tags\n// ---------------------------------------------------------------------------\n/**\n * HTTP_URL (string) URL of the request being handled in this segment of the\n * trace, in standard URI format. E.g., \"https://domain.net/path/to?resource=here\"\n */\nexports.HTTP_URL = 'http.url';\n/**\n * HTTP_METHOD (string) HTTP method of the request for the associated Span. E.g.,\n * \"GET\", \"POST\"\n */\nexports.HTTP_METHOD = 'http.method';\n/**\n * HTTP_STATUS_CODE (number) HTTP response status code for the associated Span.\n * E.g., 200, 503, 404\n */\nexports.HTTP_STATUS_CODE = 'http.status_code';\n// -------------------------------------------------------------------------\n// Messaging tags\n// -------------------------------------------------------------------------\n/**\n * MESSAGE_BUS_DESTINATION (string) An address at which messages can be exchanged.\n * E.g. A Kafka record has an associated \"topic name\" that can be extracted\n * by the instrumented producer or consumer and stored using this tag.\n */\nexports.MESSAGE_BUS_DESTINATION = 'message_bus.destination';\n// --------------------------------------------------------------------------\n// Database tags\n// --------------------------------------------------------------------------\n/**\n * DB_INSTANCE (string) Database instance name. E.g., In java, if the\n * jdbc.url=\"*************************************\", the instance name is \"customers\".\n */\nexports.DB_INSTANCE = 'db.instance';\n/**\n * DB_STATEMENT (string) A database statement for the given database type.\n * E.g., for db.type=\"SQL\", \"SELECT * FROM wuser_table\";\n * for db.type=\"redis\", \"SET mykey 'WuValue'\".\n */\nexports.DB_STATEMENT = 'db.statement';\n/**\n * DB_TYPE (string) Database type. For any SQL database, \"sql\". For others,\n * the lower-case database category, e.g. \"cassandra\", \"hbase\", or \"redis\".\n */\nexports.DB_TYPE = 'db.type';\n/**\n * DB_USER (string) Username for accessing database. E.g., \"readonly_user\"\n * or \"reporting_user\"\n */\nexports.DB_USER = 'db.user';\n//# sourceMappingURL=tags.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/ext/tags.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/opentracing/lib/functions.js":
/*!***************************************************!*\
  !*** ./node_modules/opentracing/lib/functions.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar Constants = __webpack_require__(/*! ./constants */ \"(action-browser)/./node_modules/opentracing/lib/constants.js\");\nvar reference_1 = __webpack_require__(/*! ./reference */ \"(action-browser)/./node_modules/opentracing/lib/reference.js\");\nvar span_1 = __webpack_require__(/*! ./span */ \"(action-browser)/./node_modules/opentracing/lib/span.js\");\n/**\n * Return a new REFERENCE_CHILD_OF reference.\n *\n * @param {SpanContext} spanContext - the parent SpanContext instance to\n *        reference.\n * @return a REFERENCE_CHILD_OF reference pointing to `spanContext`\n */\nfunction childOf(spanContext) {\n    // Allow the user to pass a Span instead of a SpanContext\n    if (spanContext instanceof span_1.default) {\n        spanContext = spanContext.context();\n    }\n    return new reference_1.default(Constants.REFERENCE_CHILD_OF, spanContext);\n}\nexports.childOf = childOf;\n/**\n * Return a new REFERENCE_FOLLOWS_FROM reference.\n *\n * @param {SpanContext} spanContext - the parent SpanContext instance to\n *        reference.\n * @return a REFERENCE_FOLLOWS_FROM reference pointing to `spanContext`\n */\nfunction followsFrom(spanContext) {\n    // Allow the user to pass a Span instead of a SpanContext\n    if (spanContext instanceof span_1.default) {\n        spanContext = spanContext.context();\n    }\n    return new reference_1.default(Constants.REFERENCE_FOLLOWS_FROM, spanContext);\n}\nexports.followsFrom = followsFrom;\n//# sourceMappingURL=functions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/functions.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/opentracing/lib/global_tracer.js":
/*!*******************************************************!*\
  !*** ./node_modules/opentracing/lib/global_tracer.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar tracer_1 = __webpack_require__(/*! ./tracer */ \"(action-browser)/./node_modules/opentracing/lib/tracer.js\");\nvar noopTracer = new tracer_1.default();\nvar _globalTracer = null;\n// Allows direct importing/requiring of the global tracer:\n//\n// let globalTracer = require('opentracing/global');\n//      OR\n// import globalTracer from 'opentracing/global';\n//\n// Acts a bridge to the global tracer that can be safely called before the\n// global tracer is initialized. The purpose of the delegation is to avoid the\n// sometimes nearly intractible initialization order problems that can arise in\n// applications with a complex set of dependencies, while also avoiding the\n// case where\nvar GlobalTracerDelegate = /** @class */ (function (_super) {\n    __extends(GlobalTracerDelegate, _super);\n    function GlobalTracerDelegate() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    GlobalTracerDelegate.prototype.startSpan = function () {\n        var tracer = _globalTracer || noopTracer;\n        return tracer.startSpan.apply(tracer, arguments);\n    };\n    GlobalTracerDelegate.prototype.inject = function () {\n        var tracer = _globalTracer || noopTracer;\n        return tracer.inject.apply(tracer, arguments);\n    };\n    GlobalTracerDelegate.prototype.extract = function () {\n        var tracer = _globalTracer || noopTracer;\n        return tracer.extract.apply(tracer, arguments);\n    };\n    return GlobalTracerDelegate;\n}(tracer_1.default));\nvar globalTracerDelegate = new GlobalTracerDelegate();\n/**\n * Set the global Tracer.\n *\n * The behavior is undefined if this function is called more than once.\n *\n * @param {Tracer} tracer - the Tracer implementation\n */\nfunction initGlobalTracer(tracer) {\n    _globalTracer = tracer;\n}\nexports.initGlobalTracer = initGlobalTracer;\n/**\n * Returns the global tracer.\n */\nfunction globalTracer() {\n    // Return the delegate.  Since the global tracer is largely a convenience\n    // (the user can always create their own tracers), the delegate is used to\n    // give the added convenience of not needing to worry about initialization\n    // order.\n    return globalTracerDelegate;\n}\nexports.globalTracer = globalTracer;\n//# sourceMappingURL=global_tracer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/global_tracer.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/opentracing/lib/index.js":
/*!***********************************************!*\
  !*** ./node_modules/opentracing/lib/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nfunction __export(m) {\n    for (var p in m) if (!exports.hasOwnProperty(p)) exports[p] = m[p];\n}\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar binary_carrier_1 = __webpack_require__(/*! ./binary_carrier */ \"(action-browser)/./node_modules/opentracing/lib/binary_carrier.js\");\nexports.BinaryCarrier = binary_carrier_1.default;\nvar Tags = __webpack_require__(/*! ./ext/tags */ \"(action-browser)/./node_modules/opentracing/lib/ext/tags.js\");\nexports.Tags = Tags;\nvar Noop = __webpack_require__(/*! ./noop */ \"(action-browser)/./node_modules/opentracing/lib/noop.js\");\nvar reference_1 = __webpack_require__(/*! ./reference */ \"(action-browser)/./node_modules/opentracing/lib/reference.js\");\nexports.Reference = reference_1.default;\nvar span_1 = __webpack_require__(/*! ./span */ \"(action-browser)/./node_modules/opentracing/lib/span.js\");\nexports.Span = span_1.default;\nvar span_context_1 = __webpack_require__(/*! ./span_context */ \"(action-browser)/./node_modules/opentracing/lib/span_context.js\");\nexports.SpanContext = span_context_1.default;\nvar tracer_1 = __webpack_require__(/*! ./tracer */ \"(action-browser)/./node_modules/opentracing/lib/tracer.js\");\nexports.Tracer = tracer_1.Tracer;\nvar mock_tracer_1 = __webpack_require__(/*! ./mock_tracer */ \"(action-browser)/./node_modules/opentracing/lib/mock_tracer/index.js\");\nexports.MockTracer = mock_tracer_1.MockTracer;\n__export(__webpack_require__(/*! ./global_tracer */ \"(action-browser)/./node_modules/opentracing/lib/global_tracer.js\"));\n__export(__webpack_require__(/*! ./constants */ \"(action-browser)/./node_modules/opentracing/lib/constants.js\"));\n__export(__webpack_require__(/*! ./functions */ \"(action-browser)/./node_modules/opentracing/lib/functions.js\"));\n// Initialize the noops last to avoid a dependecy cycle between the classes.\nNoop.initialize();\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/opentracing/lib/mock_tracer/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/opentracing/lib/mock_tracer/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar mock_context_1 = __webpack_require__(/*! ./mock_context */ \"(action-browser)/./node_modules/opentracing/lib/mock_tracer/mock_context.js\");\nexports.MockContext = mock_context_1.default;\nvar mock_span_1 = __webpack_require__(/*! ./mock_span */ \"(action-browser)/./node_modules/opentracing/lib/mock_tracer/mock_span.js\");\nexports.MockSpan = mock_span_1.default;\nvar mock_tracer_1 = __webpack_require__(/*! ./mock_tracer */ \"(action-browser)/./node_modules/opentracing/lib/mock_tracer/mock_tracer.js\");\nexports.MockTracer = mock_tracer_1.default;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9vcGVudHJhY2luZy9saWIvbW9ja190cmFjZXIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QscUJBQXFCLG1CQUFPLENBQUMsbUdBQWdCO0FBQzdDLG1CQUFtQjtBQUNuQixrQkFBa0IsbUJBQU8sQ0FBQyw2RkFBYTtBQUN2QyxnQkFBZ0I7QUFDaEIsb0JBQW9CLG1CQUFPLENBQUMsaUdBQWU7QUFDM0Msa0JBQWtCO0FBQ2xCIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxvcGVudHJhY2luZ1xcbGliXFxtb2NrX3RyYWNlclxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG52YXIgbW9ja19jb250ZXh0XzEgPSByZXF1aXJlKFwiLi9tb2NrX2NvbnRleHRcIik7XG5leHBvcnRzLk1vY2tDb250ZXh0ID0gbW9ja19jb250ZXh0XzEuZGVmYXVsdDtcbnZhciBtb2NrX3NwYW5fMSA9IHJlcXVpcmUoXCIuL21vY2tfc3BhblwiKTtcbmV4cG9ydHMuTW9ja1NwYW4gPSBtb2NrX3NwYW5fMS5kZWZhdWx0O1xudmFyIG1vY2tfdHJhY2VyXzEgPSByZXF1aXJlKFwiLi9tb2NrX3RyYWNlclwiKTtcbmV4cG9ydHMuTW9ja1RyYWNlciA9IG1vY2tfdHJhY2VyXzEuZGVmYXVsdDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/mock_tracer/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/opentracing/lib/mock_tracer/mock_context.js":
/*!******************************************************************!*\
  !*** ./node_modules/opentracing/lib/mock_tracer/mock_context.js ***!
  \******************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar span_context_1 = __webpack_require__(/*! ../span_context */ \"(action-browser)/./node_modules/opentracing/lib/span_context.js\");\n/**\n * OpenTracing Context implementation designed for use in\n * unit tests.\n */\nvar MockContext = /** @class */ (function (_super) {\n    __extends(MockContext, _super);\n    function MockContext(span) {\n        var _this = _super.call(this) || this;\n        // Store a reference to the span itself since this is a mock tracer\n        // intended to make debugging and unit testing easier.\n        _this._span = span;\n        return _this;\n    }\n    MockContext.prototype.span = function () {\n        return this._span;\n    };\n    return MockContext;\n}(span_context_1.SpanContext));\nexports.MockContext = MockContext;\nexports[\"default\"] = MockContext;\n//# sourceMappingURL=mock_context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/mock_tracer/mock_context.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/opentracing/lib/mock_tracer/mock_report.js":
/*!*****************************************************************!*\
  !*** ./node_modules/opentracing/lib/mock_tracer/mock_report.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * Index a collection of reported MockSpans in a way that's easy to run unit\n * test assertions against.\n */\nvar MockReport = /** @class */ (function () {\n    function MockReport(spans) {\n        var _this = this;\n        this.spans = spans;\n        this.spansByUUID = {};\n        this.spansByTag = {};\n        this.debugSpans = [];\n        this.unfinishedSpans = [];\n        spans.forEach(function (span) {\n            if (span._finishMs === 0) {\n                _this.unfinishedSpans.push(span);\n            }\n            _this.spansByUUID[span.uuid()] = span;\n            _this.debugSpans.push(span.debug());\n            var tags = span.tags();\n            Object.keys(tags).forEach(function (key) {\n                var val = tags[key];\n                _this.spansByTag[key] = _this.spansByTag[key] || {};\n                _this.spansByTag[key][val] = _this.spansByTag[key][val] || [];\n                _this.spansByTag[key][val].push(span);\n            });\n        });\n    }\n    MockReport.prototype.firstSpanWithTagValue = function (key, val) {\n        var m = this.spansByTag[key];\n        if (!m) {\n            return null;\n        }\n        var n = m[val];\n        if (!n) {\n            return null;\n        }\n        return n[0];\n    };\n    return MockReport;\n}());\nexports.MockReport = MockReport;\nexports[\"default\"] = MockReport;\n//# sourceMappingURL=mock_report.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/mock_tracer/mock_report.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/opentracing/lib/mock_tracer/mock_span.js":
/*!***************************************************************!*\
  !*** ./node_modules/opentracing/lib/mock_tracer/mock_span.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\n/* eslint-disable import/no-extraneous-dependencies */\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar opentracing = __webpack_require__(/*! ../index */ \"(action-browser)/./node_modules/opentracing/lib/index.js\");\nvar mock_context_1 = __webpack_require__(/*! ./mock_context */ \"(action-browser)/./node_modules/opentracing/lib/mock_tracer/mock_context.js\");\n/**\n * OpenTracing Span implementation designed for use in unit tests.\n */\nvar MockSpan = /** @class */ (function (_super) {\n    __extends(MockSpan, _super);\n    //------------------------------------------------------------------------//\n    // MockSpan-specific\n    //------------------------------------------------------------------------//\n    function MockSpan(tracer) {\n        var _this = _super.call(this) || this;\n        _this._mockTracer = tracer;\n        _this._uuid = _this._generateUUID();\n        _this._startMs = Date.now();\n        _this._finishMs = 0;\n        _this._operationName = '';\n        _this._tags = {};\n        _this._logs = [];\n        return _this;\n    }\n    //------------------------------------------------------------------------//\n    // OpenTracing implementation\n    //------------------------------------------------------------------------//\n    MockSpan.prototype._context = function () {\n        return new mock_context_1.default(this);\n    };\n    MockSpan.prototype._setOperationName = function (name) {\n        this._operationName = name;\n    };\n    MockSpan.prototype._addTags = function (set) {\n        var keys = Object.keys(set);\n        for (var _i = 0, keys_1 = keys; _i < keys_1.length; _i++) {\n            var key = keys_1[_i];\n            this._tags[key] = set[key];\n        }\n    };\n    MockSpan.prototype._log = function (fields, timestamp) {\n        this._logs.push({\n            fields: fields,\n            timestamp: timestamp\n        });\n    };\n    MockSpan.prototype._finish = function (finishTime) {\n        this._finishMs = finishTime || Date.now();\n    };\n    MockSpan.prototype.uuid = function () {\n        return this._uuid;\n    };\n    MockSpan.prototype.operationName = function () {\n        return this._operationName;\n    };\n    MockSpan.prototype.durationMs = function () {\n        return this._finishMs - this._startMs;\n    };\n    MockSpan.prototype.tags = function () {\n        return this._tags;\n    };\n    MockSpan.prototype.tracer = function () {\n        return this._mockTracer;\n    };\n    MockSpan.prototype._generateUUID = function () {\n        var p0 = (\"00000000\" + Math.abs((Math.random() * 0xFFFFFFFF) | 0).toString(16)).substr(-8);\n        var p1 = (\"00000000\" + Math.abs((Math.random() * 0xFFFFFFFF) | 0).toString(16)).substr(-8);\n        return \"\" + p0 + p1;\n    };\n    MockSpan.prototype.addReference = function (ref) {\n    };\n    /**\n     * Returns a simplified object better for console.log()'ing.\n     */\n    MockSpan.prototype.debug = function () {\n        var obj = {\n            uuid: this._uuid,\n            operation: this._operationName,\n            millis: [this._finishMs - this._startMs, this._startMs, this._finishMs]\n        };\n        if (Object.keys(this._tags).length) {\n            obj.tags = this._tags;\n        }\n        return obj;\n    };\n    return MockSpan;\n}(opentracing.Span));\nexports.MockSpan = MockSpan;\nexports[\"default\"] = MockSpan;\n//# sourceMappingURL=mock_span.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/mock_tracer/mock_span.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/opentracing/lib/mock_tracer/mock_tracer.js":
/*!*****************************************************************!*\
  !*** ./node_modules/opentracing/lib/mock_tracer/mock_tracer.js ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n// TODO: Move mock-tracer to its own NPM package once it is complete and tested.\nvar opentracing = __webpack_require__(/*! ../index */ \"(action-browser)/./node_modules/opentracing/lib/index.js\");\nvar mock_report_1 = __webpack_require__(/*! ./mock_report */ \"(action-browser)/./node_modules/opentracing/lib/mock_tracer/mock_report.js\");\nvar mock_span_1 = __webpack_require__(/*! ./mock_span */ \"(action-browser)/./node_modules/opentracing/lib/mock_tracer/mock_span.js\");\n/**\n * OpenTracing Tracer implementation designed for use in unit tests.\n */\nvar MockTracer = /** @class */ (function (_super) {\n    __extends(MockTracer, _super);\n    //------------------------------------------------------------------------//\n    // MockTracer-specific\n    //------------------------------------------------------------------------//\n    function MockTracer() {\n        var _this = _super.call(this) || this;\n        _this._spans = [];\n        return _this;\n    }\n    //------------------------------------------------------------------------//\n    // OpenTracing implementation\n    //------------------------------------------------------------------------//\n    MockTracer.prototype._startSpan = function (name, fields) {\n        // _allocSpan is given it's own method so that derived classes can\n        // allocate any type of object they want, but not have to duplicate\n        // the other common logic in startSpan().\n        var span = this._allocSpan();\n        span.setOperationName(name);\n        this._spans.push(span);\n        if (fields.references) {\n            for (var _i = 0, _a = fields.references; _i < _a.length; _i++) {\n                var ref = _a[_i];\n                span.addReference(ref);\n            }\n        }\n        // Capture the stack at the time the span started\n        span._startStack = new Error().stack;\n        return span;\n    };\n    MockTracer.prototype._inject = function (span, format, carrier) {\n        throw new Error('NOT YET IMPLEMENTED');\n    };\n    MockTracer.prototype._extract = function (format, carrier) {\n        throw new Error('NOT YET IMPLEMENTED');\n    };\n    MockTracer.prototype._allocSpan = function () {\n        return new mock_span_1.default(this);\n    };\n    /**\n     * Discard any buffered data.\n     */\n    MockTracer.prototype.clear = function () {\n        this._spans = [];\n    };\n    /**\n     * Return the buffered data in a format convenient for making unit test\n     * assertions.\n     */\n    MockTracer.prototype.report = function () {\n        return new mock_report_1.default(this._spans);\n    };\n    return MockTracer;\n}(opentracing.Tracer));\nexports.MockTracer = MockTracer;\nexports[\"default\"] = MockTracer;\n//# sourceMappingURL=mock_tracer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/mock_tracer/mock_tracer.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/opentracing/lib/noop.js":
/*!**********************************************!*\
  !*** ./node_modules/opentracing/lib/noop.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar span_1 = __webpack_require__(/*! ./span */ \"(action-browser)/./node_modules/opentracing/lib/span.js\");\nvar span_context_1 = __webpack_require__(/*! ./span_context */ \"(action-browser)/./node_modules/opentracing/lib/span_context.js\");\nvar tracer_1 = __webpack_require__(/*! ./tracer */ \"(action-browser)/./node_modules/opentracing/lib/tracer.js\");\nexports.tracer = null;\nexports.spanContext = null;\nexports.span = null;\n// Deferred initialization to avoid a dependency cycle where Tracer depends on\n// Span which depends on the noop tracer.\nfunction initialize() {\n    exports.tracer = new tracer_1.default();\n    exports.span = new span_1.default();\n    exports.spanContext = new span_context_1.default();\n}\nexports.initialize = initialize;\n//# sourceMappingURL=noop.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9vcGVudHJhY2luZy9saWIvbm9vcC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxhQUFhLG1CQUFPLENBQUMsdUVBQVE7QUFDN0IscUJBQXFCLG1CQUFPLENBQUMsdUZBQWdCO0FBQzdDLGVBQWUsbUJBQU8sQ0FBQywyRUFBVTtBQUNqQyxjQUFjO0FBQ2QsbUJBQW1CO0FBQ25CLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxJQUFJLGNBQWM7QUFDbEIsSUFBSSxZQUFZO0FBQ2hCLElBQUksbUJBQW1CO0FBQ3ZCO0FBQ0Esa0JBQWtCO0FBQ2xCIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxvcGVudHJhY2luZ1xcbGliXFxub29wLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xudmFyIHNwYW5fMSA9IHJlcXVpcmUoXCIuL3NwYW5cIik7XG52YXIgc3Bhbl9jb250ZXh0XzEgPSByZXF1aXJlKFwiLi9zcGFuX2NvbnRleHRcIik7XG52YXIgdHJhY2VyXzEgPSByZXF1aXJlKFwiLi90cmFjZXJcIik7XG5leHBvcnRzLnRyYWNlciA9IG51bGw7XG5leHBvcnRzLnNwYW5Db250ZXh0ID0gbnVsbDtcbmV4cG9ydHMuc3BhbiA9IG51bGw7XG4vLyBEZWZlcnJlZCBpbml0aWFsaXphdGlvbiB0byBhdm9pZCBhIGRlcGVuZGVuY3kgY3ljbGUgd2hlcmUgVHJhY2VyIGRlcGVuZHMgb25cbi8vIFNwYW4gd2hpY2ggZGVwZW5kcyBvbiB0aGUgbm9vcCB0cmFjZXIuXG5mdW5jdGlvbiBpbml0aWFsaXplKCkge1xuICAgIGV4cG9ydHMudHJhY2VyID0gbmV3IHRyYWNlcl8xLmRlZmF1bHQoKTtcbiAgICBleHBvcnRzLnNwYW4gPSBuZXcgc3Bhbl8xLmRlZmF1bHQoKTtcbiAgICBleHBvcnRzLnNwYW5Db250ZXh0ID0gbmV3IHNwYW5fY29udGV4dF8xLmRlZmF1bHQoKTtcbn1cbmV4cG9ydHMuaW5pdGlhbGl6ZSA9IGluaXRpYWxpemU7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1ub29wLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/noop.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/opentracing/lib/reference.js":
/*!***************************************************!*\
  !*** ./node_modules/opentracing/lib/reference.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar span_1 = __webpack_require__(/*! ./span */ \"(action-browser)/./node_modules/opentracing/lib/span.js\");\n/**\n * Reference pairs a reference type constant (e.g., REFERENCE_CHILD_OF or\n * REFERENCE_FOLLOWS_FROM) with the SpanContext it points to.\n *\n * See the exported childOf() and followsFrom() functions at the package level.\n */\nvar Reference = /** @class */ (function () {\n    /**\n     * Initialize a new Reference instance.\n     *\n     * @param {string} type - the Reference type constant (e.g.,\n     *        REFERENCE_CHILD_OF or REFERENCE_FOLLOWS_FROM).\n     * @param {SpanContext} referencedContext - the SpanContext being referred\n     *        to. As a convenience, a Span instance may be passed in instead\n     *        (in which case its .context() is used here).\n     */\n    function Reference(type, referencedContext) {\n        this._type = type;\n        this._referencedContext = (referencedContext instanceof span_1.default ?\n            referencedContext.context() :\n            referencedContext);\n    }\n    /**\n     * @return {string} The Reference type (e.g., REFERENCE_CHILD_OF or\n     *         REFERENCE_FOLLOWS_FROM).\n     */\n    Reference.prototype.type = function () {\n        return this._type;\n    };\n    /**\n     * @return {SpanContext} The SpanContext being referred to (e.g., the\n     *         parent in a REFERENCE_CHILD_OF Reference).\n     */\n    Reference.prototype.referencedContext = function () {\n        return this._referencedContext;\n    };\n    return Reference;\n}());\nexports[\"default\"] = Reference;\n//# sourceMappingURL=reference.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9vcGVudHJhY2luZy9saWIvcmVmZXJlbmNlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGFBQWEsbUJBQU8sQ0FBQyx1RUFBUTtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsUUFBUTtBQUN2QjtBQUNBLGVBQWUsYUFBYTtBQUM1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixRQUFRO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixhQUFhO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRCxrQkFBZTtBQUNmIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxvcGVudHJhY2luZ1xcbGliXFxyZWZlcmVuY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG52YXIgc3Bhbl8xID0gcmVxdWlyZShcIi4vc3BhblwiKTtcbi8qKlxuICogUmVmZXJlbmNlIHBhaXJzIGEgcmVmZXJlbmNlIHR5cGUgY29uc3RhbnQgKGUuZy4sIFJFRkVSRU5DRV9DSElMRF9PRiBvclxuICogUkVGRVJFTkNFX0ZPTExPV1NfRlJPTSkgd2l0aCB0aGUgU3BhbkNvbnRleHQgaXQgcG9pbnRzIHRvLlxuICpcbiAqIFNlZSB0aGUgZXhwb3J0ZWQgY2hpbGRPZigpIGFuZCBmb2xsb3dzRnJvbSgpIGZ1bmN0aW9ucyBhdCB0aGUgcGFja2FnZSBsZXZlbC5cbiAqL1xudmFyIFJlZmVyZW5jZSA9IC8qKiBAY2xhc3MgKi8gKGZ1bmN0aW9uICgpIHtcbiAgICAvKipcbiAgICAgKiBJbml0aWFsaXplIGEgbmV3IFJlZmVyZW5jZSBpbnN0YW5jZS5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSB0eXBlIC0gdGhlIFJlZmVyZW5jZSB0eXBlIGNvbnN0YW50IChlLmcuLFxuICAgICAqICAgICAgICBSRUZFUkVOQ0VfQ0hJTERfT0Ygb3IgUkVGRVJFTkNFX0ZPTExPV1NfRlJPTSkuXG4gICAgICogQHBhcmFtIHtTcGFuQ29udGV4dH0gcmVmZXJlbmNlZENvbnRleHQgLSB0aGUgU3BhbkNvbnRleHQgYmVpbmcgcmVmZXJyZWRcbiAgICAgKiAgICAgICAgdG8uIEFzIGEgY29udmVuaWVuY2UsIGEgU3BhbiBpbnN0YW5jZSBtYXkgYmUgcGFzc2VkIGluIGluc3RlYWRcbiAgICAgKiAgICAgICAgKGluIHdoaWNoIGNhc2UgaXRzIC5jb250ZXh0KCkgaXMgdXNlZCBoZXJlKS5cbiAgICAgKi9cbiAgICBmdW5jdGlvbiBSZWZlcmVuY2UodHlwZSwgcmVmZXJlbmNlZENvbnRleHQpIHtcbiAgICAgICAgdGhpcy5fdHlwZSA9IHR5cGU7XG4gICAgICAgIHRoaXMuX3JlZmVyZW5jZWRDb250ZXh0ID0gKHJlZmVyZW5jZWRDb250ZXh0IGluc3RhbmNlb2Ygc3Bhbl8xLmRlZmF1bHQgP1xuICAgICAgICAgICAgcmVmZXJlbmNlZENvbnRleHQuY29udGV4dCgpIDpcbiAgICAgICAgICAgIHJlZmVyZW5jZWRDb250ZXh0KTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQHJldHVybiB7c3RyaW5nfSBUaGUgUmVmZXJlbmNlIHR5cGUgKGUuZy4sIFJFRkVSRU5DRV9DSElMRF9PRiBvclxuICAgICAqICAgICAgICAgUkVGRVJFTkNFX0ZPTExPV1NfRlJPTSkuXG4gICAgICovXG4gICAgUmVmZXJlbmNlLnByb3RvdHlwZS50eXBlID0gZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5fdHlwZTtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIEByZXR1cm4ge1NwYW5Db250ZXh0fSBUaGUgU3BhbkNvbnRleHQgYmVpbmcgcmVmZXJyZWQgdG8gKGUuZy4sIHRoZVxuICAgICAqICAgICAgICAgcGFyZW50IGluIGEgUkVGRVJFTkNFX0NISUxEX09GIFJlZmVyZW5jZSkuXG4gICAgICovXG4gICAgUmVmZXJlbmNlLnByb3RvdHlwZS5yZWZlcmVuY2VkQ29udGV4dCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX3JlZmVyZW5jZWRDb250ZXh0O1xuICAgIH07XG4gICAgcmV0dXJuIFJlZmVyZW5jZTtcbn0oKSk7XG5leHBvcnRzLmRlZmF1bHQgPSBSZWZlcmVuY2U7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1yZWZlcmVuY2UuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/reference.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/opentracing/lib/span.js":
/*!**********************************************!*\
  !*** ./node_modules/opentracing/lib/span.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar noop = __webpack_require__(/*! ./noop */ \"(action-browser)/./node_modules/opentracing/lib/noop.js\");\n/**\n * Span represents a logical unit of work as part of a broader Trace. Examples\n * of span might include remote procedure calls or a in-process function calls\n * to sub-components. A Trace has a single, top-level \"root\" Span that in turn\n * may have zero or more child Spans, which in turn may have children.\n */\nvar Span = /** @class */ (function () {\n    function Span() {\n    }\n    // ---------------------------------------------------------------------- //\n    // OpenTracing API methods\n    // ---------------------------------------------------------------------- //\n    /**\n     * Returns the SpanContext object associated with this Span.\n     *\n     * @return {SpanContext}\n     */\n    Span.prototype.context = function () {\n        return this._context();\n    };\n    /**\n     * Returns the Tracer object used to create this Span.\n     *\n     * @return {Tracer}\n     */\n    Span.prototype.tracer = function () {\n        return this._tracer();\n    };\n    /**\n     * Sets the string name for the logical operation this span represents.\n     *\n     * @param {string} name\n     */\n    Span.prototype.setOperationName = function (name) {\n        this._setOperationName(name);\n        return this;\n    };\n    /**\n     * Sets a key:value pair on this Span that also propagates to future\n     * children of the associated Span.\n     *\n     * setBaggageItem() enables powerful functionality given a full-stack\n     * opentracing integration (e.g., arbitrary application data from a web\n     * client can make it, transparently, all the way into the depths of a\n     * storage system), and with it some powerful costs: use this feature with\n     * care.\n     *\n     * IMPORTANT NOTE #1: setBaggageItem() will only propagate baggage items to\n     * *future* causal descendants of the associated Span.\n     *\n     * IMPORTANT NOTE #2: Use this thoughtfully and with care. Every key and\n     * value is copied into every local *and remote* child of the associated\n     * Span, and that can add up to a lot of network and cpu overhead.\n     *\n     * @param {string} key\n     * @param {string} value\n     */\n    Span.prototype.setBaggageItem = function (key, value) {\n        this._setBaggageItem(key, value);\n        return this;\n    };\n    /**\n     * Returns the value for a baggage item given its key.\n     *\n     * @param  {string} key\n     *         The key for the given trace attribute.\n     * @return {string}\n     *         String value for the given key, or undefined if the key does not\n     *         correspond to a set trace attribute.\n     */\n    Span.prototype.getBaggageItem = function (key) {\n        return this._getBaggageItem(key);\n    };\n    /**\n     * Adds a single tag to the span.  See `addTags()` for details.\n     *\n     * @param {string} key\n     * @param {any} value\n     */\n    Span.prototype.setTag = function (key, value) {\n        var _a;\n        // NOTE: the call is normalized to a call to _addTags()\n        this._addTags((_a = {}, _a[key] = value, _a));\n        return this;\n    };\n    /**\n     * Adds the given key value pairs to the set of span tags.\n     *\n     * Multiple calls to addTags() results in the tags being the superset of\n     * all calls.\n     *\n     * The behavior of setting the same key multiple times on the same span\n     * is undefined.\n     *\n     * The supported type of the values is implementation-dependent.\n     * Implementations are expected to safely handle all types of values but\n     * may choose to ignore unrecognized / unhandle-able values (e.g. objects\n     * with cyclic references, function objects).\n     *\n     * @return {[type]} [description]\n     */\n    Span.prototype.addTags = function (keyValueMap) {\n        this._addTags(keyValueMap);\n        return this;\n    };\n    /**\n     * Add a log record to this Span, optionally at a user-provided timestamp.\n     *\n     * For example:\n     *\n     *     span.log({\n     *         size: rpc.size(),  // numeric value\n     *         URI: rpc.URI(),  // string value\n     *         payload: rpc.payload(),  // Object value\n     *         \"keys can be arbitrary strings\": rpc.foo(),\n     *     });\n     *\n     *     span.log({\n     *         \"error.description\": someError.description(),\n     *     }, someError.timestampMillis());\n     *\n     * @param {object} keyValuePairs\n     *        An object mapping string keys to arbitrary value types. All\n     *        Tracer implementations should support bool, string, and numeric\n     *        value types, and some may also support Object values.\n     * @param {number} timestamp\n     *        An optional parameter specifying the timestamp in milliseconds\n     *        since the Unix epoch. Fractional values are allowed so that\n     *        timestamps with sub-millisecond accuracy can be represented. If\n     *        not specified, the implementation is expected to use its notion\n     *        of the current time of the call.\n     */\n    Span.prototype.log = function (keyValuePairs, timestamp) {\n        this._log(keyValuePairs, timestamp);\n        return this;\n    };\n    /**\n     * DEPRECATED\n     */\n    Span.prototype.logEvent = function (eventName, payload) {\n        return this._log({ event: eventName, payload: payload });\n    };\n    /**\n     * Sets the end timestamp and finalizes Span state.\n     *\n     * With the exception of calls to Span.context() (which are always allowed),\n     * finish() must be the last call made to any span instance, and to do\n     * otherwise leads to undefined behavior.\n     *\n     * @param  {number} finishTime\n     *         Optional finish time in milliseconds as a Unix timestamp. Decimal\n     *         values are supported for timestamps with sub-millisecond accuracy.\n     *         If not specified, the current time (as defined by the\n     *         implementation) will be used.\n     */\n    Span.prototype.finish = function (finishTime) {\n        this._finish(finishTime);\n        // Do not return `this`. The Span generally should not be used after it\n        // is finished so chaining is not desired in this context.\n    };\n    // ---------------------------------------------------------------------- //\n    // Derived classes can choose to implement the below\n    // ---------------------------------------------------------------------- //\n    // By default returns a no-op SpanContext.\n    Span.prototype._context = function () {\n        return noop.spanContext;\n    };\n    // By default returns a no-op tracer.\n    //\n    // The base class could store the tracer that created it, but it does not\n    // in order to ensure the no-op span implementation has zero members,\n    // which allows V8 to aggressively optimize calls to such objects.\n    Span.prototype._tracer = function () {\n        return noop.tracer;\n    };\n    // By default does nothing\n    Span.prototype._setOperationName = function (name) {\n    };\n    // By default does nothing\n    Span.prototype._setBaggageItem = function (key, value) {\n    };\n    // By default does nothing\n    Span.prototype._getBaggageItem = function (key) {\n        return undefined;\n    };\n    // By default does nothing\n    //\n    // NOTE: both setTag() and addTags() map to this function. keyValuePairs\n    // will always be an associative array.\n    Span.prototype._addTags = function (keyValuePairs) {\n    };\n    // By default does nothing\n    Span.prototype._log = function (keyValuePairs, timestamp) {\n    };\n    // By default does nothing\n    //\n    // finishTime is expected to be either a number or undefined.\n    Span.prototype._finish = function (finishTime) {\n    };\n    return Span;\n}());\nexports.Span = Span;\nexports[\"default\"] = Span;\n//# sourceMappingURL=span.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9vcGVudHJhY2luZy9saWIvc3Bhbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxXQUFXLG1CQUFPLENBQUMsdUVBQVE7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsUUFBUTtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLFFBQVE7QUFDeEI7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsS0FBSztBQUNwQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhCQUE4QjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixRQUFRO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQSxlQUFlLFFBQVE7QUFDdkI7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsb0NBQW9DO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsUUFBUTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNELFlBQVk7QUFDWixrQkFBZTtBQUNmIiwic291cmNlcyI6WyJFOlxcYm90XFx0cmFkaW5nYm90X2ZpbmFsXFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxvcGVudHJhY2luZ1xcbGliXFxzcGFuLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xudmFyIG5vb3AgPSByZXF1aXJlKFwiLi9ub29wXCIpO1xuLyoqXG4gKiBTcGFuIHJlcHJlc2VudHMgYSBsb2dpY2FsIHVuaXQgb2Ygd29yayBhcyBwYXJ0IG9mIGEgYnJvYWRlciBUcmFjZS4gRXhhbXBsZXNcbiAqIG9mIHNwYW4gbWlnaHQgaW5jbHVkZSByZW1vdGUgcHJvY2VkdXJlIGNhbGxzIG9yIGEgaW4tcHJvY2VzcyBmdW5jdGlvbiBjYWxsc1xuICogdG8gc3ViLWNvbXBvbmVudHMuIEEgVHJhY2UgaGFzIGEgc2luZ2xlLCB0b3AtbGV2ZWwgXCJyb290XCIgU3BhbiB0aGF0IGluIHR1cm5cbiAqIG1heSBoYXZlIHplcm8gb3IgbW9yZSBjaGlsZCBTcGFucywgd2hpY2ggaW4gdHVybiBtYXkgaGF2ZSBjaGlsZHJlbi5cbiAqL1xudmFyIFNwYW4gPSAvKiogQGNsYXNzICovIChmdW5jdGlvbiAoKSB7XG4gICAgZnVuY3Rpb24gU3BhbigpIHtcbiAgICB9XG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSAvL1xuICAgIC8vIE9wZW5UcmFjaW5nIEFQSSBtZXRob2RzXG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSAvL1xuICAgIC8qKlxuICAgICAqIFJldHVybnMgdGhlIFNwYW5Db250ZXh0IG9iamVjdCBhc3NvY2lhdGVkIHdpdGggdGhpcyBTcGFuLlxuICAgICAqXG4gICAgICogQHJldHVybiB7U3BhbkNvbnRleHR9XG4gICAgICovXG4gICAgU3Bhbi5wcm90b3R5cGUuY29udGV4dCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX2NvbnRleHQoKTtcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIFJldHVybnMgdGhlIFRyYWNlciBvYmplY3QgdXNlZCB0byBjcmVhdGUgdGhpcyBTcGFuLlxuICAgICAqXG4gICAgICogQHJldHVybiB7VHJhY2VyfVxuICAgICAqL1xuICAgIFNwYW4ucHJvdG90eXBlLnRyYWNlciA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX3RyYWNlcigpO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogU2V0cyB0aGUgc3RyaW5nIG5hbWUgZm9yIHRoZSBsb2dpY2FsIG9wZXJhdGlvbiB0aGlzIHNwYW4gcmVwcmVzZW50cy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lXG4gICAgICovXG4gICAgU3Bhbi5wcm90b3R5cGUuc2V0T3BlcmF0aW9uTmFtZSA9IGZ1bmN0aW9uIChuYW1lKSB7XG4gICAgICAgIHRoaXMuX3NldE9wZXJhdGlvbk5hbWUobmFtZSk7XG4gICAgICAgIHJldHVybiB0aGlzO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogU2V0cyBhIGtleTp2YWx1ZSBwYWlyIG9uIHRoaXMgU3BhbiB0aGF0IGFsc28gcHJvcGFnYXRlcyB0byBmdXR1cmVcbiAgICAgKiBjaGlsZHJlbiBvZiB0aGUgYXNzb2NpYXRlZCBTcGFuLlxuICAgICAqXG4gICAgICogc2V0QmFnZ2FnZUl0ZW0oKSBlbmFibGVzIHBvd2VyZnVsIGZ1bmN0aW9uYWxpdHkgZ2l2ZW4gYSBmdWxsLXN0YWNrXG4gICAgICogb3BlbnRyYWNpbmcgaW50ZWdyYXRpb24gKGUuZy4sIGFyYml0cmFyeSBhcHBsaWNhdGlvbiBkYXRhIGZyb20gYSB3ZWJcbiAgICAgKiBjbGllbnQgY2FuIG1ha2UgaXQsIHRyYW5zcGFyZW50bHksIGFsbCB0aGUgd2F5IGludG8gdGhlIGRlcHRocyBvZiBhXG4gICAgICogc3RvcmFnZSBzeXN0ZW0pLCBhbmQgd2l0aCBpdCBzb21lIHBvd2VyZnVsIGNvc3RzOiB1c2UgdGhpcyBmZWF0dXJlIHdpdGhcbiAgICAgKiBjYXJlLlxuICAgICAqXG4gICAgICogSU1QT1JUQU5UIE5PVEUgIzE6IHNldEJhZ2dhZ2VJdGVtKCkgd2lsbCBvbmx5IHByb3BhZ2F0ZSBiYWdnYWdlIGl0ZW1zIHRvXG4gICAgICogKmZ1dHVyZSogY2F1c2FsIGRlc2NlbmRhbnRzIG9mIHRoZSBhc3NvY2lhdGVkIFNwYW4uXG4gICAgICpcbiAgICAgKiBJTVBPUlRBTlQgTk9URSAjMjogVXNlIHRoaXMgdGhvdWdodGZ1bGx5IGFuZCB3aXRoIGNhcmUuIEV2ZXJ5IGtleSBhbmRcbiAgICAgKiB2YWx1ZSBpcyBjb3BpZWQgaW50byBldmVyeSBsb2NhbCAqYW5kIHJlbW90ZSogY2hpbGQgb2YgdGhlIGFzc29jaWF0ZWRcbiAgICAgKiBTcGFuLCBhbmQgdGhhdCBjYW4gYWRkIHVwIHRvIGEgbG90IG9mIG5ldHdvcmsgYW5kIGNwdSBvdmVyaGVhZC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSB7c3RyaW5nfSBrZXlcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30gdmFsdWVcbiAgICAgKi9cbiAgICBTcGFuLnByb3RvdHlwZS5zZXRCYWdnYWdlSXRlbSA9IGZ1bmN0aW9uIChrZXksIHZhbHVlKSB7XG4gICAgICAgIHRoaXMuX3NldEJhZ2dhZ2VJdGVtKGtleSwgdmFsdWUpO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIFJldHVybnMgdGhlIHZhbHVlIGZvciBhIGJhZ2dhZ2UgaXRlbSBnaXZlbiBpdHMga2V5LlxuICAgICAqXG4gICAgICogQHBhcmFtICB7c3RyaW5nfSBrZXlcbiAgICAgKiAgICAgICAgIFRoZSBrZXkgZm9yIHRoZSBnaXZlbiB0cmFjZSBhdHRyaWJ1dGUuXG4gICAgICogQHJldHVybiB7c3RyaW5nfVxuICAgICAqICAgICAgICAgU3RyaW5nIHZhbHVlIGZvciB0aGUgZ2l2ZW4ga2V5LCBvciB1bmRlZmluZWQgaWYgdGhlIGtleSBkb2VzIG5vdFxuICAgICAqICAgICAgICAgY29ycmVzcG9uZCB0byBhIHNldCB0cmFjZSBhdHRyaWJ1dGUuXG4gICAgICovXG4gICAgU3Bhbi5wcm90b3R5cGUuZ2V0QmFnZ2FnZUl0ZW0gPSBmdW5jdGlvbiAoa2V5KSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9nZXRCYWdnYWdlSXRlbShrZXkpO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogQWRkcyBhIHNpbmdsZSB0YWcgdG8gdGhlIHNwYW4uICBTZWUgYGFkZFRhZ3MoKWAgZm9yIGRldGFpbHMuXG4gICAgICpcbiAgICAgKiBAcGFyYW0ge3N0cmluZ30ga2V5XG4gICAgICogQHBhcmFtIHthbnl9IHZhbHVlXG4gICAgICovXG4gICAgU3Bhbi5wcm90b3R5cGUuc2V0VGFnID0gZnVuY3Rpb24gKGtleSwgdmFsdWUpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICAvLyBOT1RFOiB0aGUgY2FsbCBpcyBub3JtYWxpemVkIHRvIGEgY2FsbCB0byBfYWRkVGFncygpXG4gICAgICAgIHRoaXMuX2FkZFRhZ3MoKF9hID0ge30sIF9hW2tleV0gPSB2YWx1ZSwgX2EpKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBBZGRzIHRoZSBnaXZlbiBrZXkgdmFsdWUgcGFpcnMgdG8gdGhlIHNldCBvZiBzcGFuIHRhZ3MuXG4gICAgICpcbiAgICAgKiBNdWx0aXBsZSBjYWxscyB0byBhZGRUYWdzKCkgcmVzdWx0cyBpbiB0aGUgdGFncyBiZWluZyB0aGUgc3VwZXJzZXQgb2ZcbiAgICAgKiBhbGwgY2FsbHMuXG4gICAgICpcbiAgICAgKiBUaGUgYmVoYXZpb3Igb2Ygc2V0dGluZyB0aGUgc2FtZSBrZXkgbXVsdGlwbGUgdGltZXMgb24gdGhlIHNhbWUgc3BhblxuICAgICAqIGlzIHVuZGVmaW5lZC5cbiAgICAgKlxuICAgICAqIFRoZSBzdXBwb3J0ZWQgdHlwZSBvZiB0aGUgdmFsdWVzIGlzIGltcGxlbWVudGF0aW9uLWRlcGVuZGVudC5cbiAgICAgKiBJbXBsZW1lbnRhdGlvbnMgYXJlIGV4cGVjdGVkIHRvIHNhZmVseSBoYW5kbGUgYWxsIHR5cGVzIG9mIHZhbHVlcyBidXRcbiAgICAgKiBtYXkgY2hvb3NlIHRvIGlnbm9yZSB1bnJlY29nbml6ZWQgLyB1bmhhbmRsZS1hYmxlIHZhbHVlcyAoZS5nLiBvYmplY3RzXG4gICAgICogd2l0aCBjeWNsaWMgcmVmZXJlbmNlcywgZnVuY3Rpb24gb2JqZWN0cykuXG4gICAgICpcbiAgICAgKiBAcmV0dXJuIHtbdHlwZV19IFtkZXNjcmlwdGlvbl1cbiAgICAgKi9cbiAgICBTcGFuLnByb3RvdHlwZS5hZGRUYWdzID0gZnVuY3Rpb24gKGtleVZhbHVlTWFwKSB7XG4gICAgICAgIHRoaXMuX2FkZFRhZ3Moa2V5VmFsdWVNYXApO1xuICAgICAgICByZXR1cm4gdGhpcztcbiAgICB9O1xuICAgIC8qKlxuICAgICAqIEFkZCBhIGxvZyByZWNvcmQgdG8gdGhpcyBTcGFuLCBvcHRpb25hbGx5IGF0IGEgdXNlci1wcm92aWRlZCB0aW1lc3RhbXAuXG4gICAgICpcbiAgICAgKiBGb3IgZXhhbXBsZTpcbiAgICAgKlxuICAgICAqICAgICBzcGFuLmxvZyh7XG4gICAgICogICAgICAgICBzaXplOiBycGMuc2l6ZSgpLCAgLy8gbnVtZXJpYyB2YWx1ZVxuICAgICAqICAgICAgICAgVVJJOiBycGMuVVJJKCksICAvLyBzdHJpbmcgdmFsdWVcbiAgICAgKiAgICAgICAgIHBheWxvYWQ6IHJwYy5wYXlsb2FkKCksICAvLyBPYmplY3QgdmFsdWVcbiAgICAgKiAgICAgICAgIFwia2V5cyBjYW4gYmUgYXJiaXRyYXJ5IHN0cmluZ3NcIjogcnBjLmZvbygpLFxuICAgICAqICAgICB9KTtcbiAgICAgKlxuICAgICAqICAgICBzcGFuLmxvZyh7XG4gICAgICogICAgICAgICBcImVycm9yLmRlc2NyaXB0aW9uXCI6IHNvbWVFcnJvci5kZXNjcmlwdGlvbigpLFxuICAgICAqICAgICB9LCBzb21lRXJyb3IudGltZXN0YW1wTWlsbGlzKCkpO1xuICAgICAqXG4gICAgICogQHBhcmFtIHtvYmplY3R9IGtleVZhbHVlUGFpcnNcbiAgICAgKiAgICAgICAgQW4gb2JqZWN0IG1hcHBpbmcgc3RyaW5nIGtleXMgdG8gYXJiaXRyYXJ5IHZhbHVlIHR5cGVzLiBBbGxcbiAgICAgKiAgICAgICAgVHJhY2VyIGltcGxlbWVudGF0aW9ucyBzaG91bGQgc3VwcG9ydCBib29sLCBzdHJpbmcsIGFuZCBudW1lcmljXG4gICAgICogICAgICAgIHZhbHVlIHR5cGVzLCBhbmQgc29tZSBtYXkgYWxzbyBzdXBwb3J0IE9iamVjdCB2YWx1ZXMuXG4gICAgICogQHBhcmFtIHtudW1iZXJ9IHRpbWVzdGFtcFxuICAgICAqICAgICAgICBBbiBvcHRpb25hbCBwYXJhbWV0ZXIgc3BlY2lmeWluZyB0aGUgdGltZXN0YW1wIGluIG1pbGxpc2Vjb25kc1xuICAgICAqICAgICAgICBzaW5jZSB0aGUgVW5peCBlcG9jaC4gRnJhY3Rpb25hbCB2YWx1ZXMgYXJlIGFsbG93ZWQgc28gdGhhdFxuICAgICAqICAgICAgICB0aW1lc3RhbXBzIHdpdGggc3ViLW1pbGxpc2Vjb25kIGFjY3VyYWN5IGNhbiBiZSByZXByZXNlbnRlZC4gSWZcbiAgICAgKiAgICAgICAgbm90IHNwZWNpZmllZCwgdGhlIGltcGxlbWVudGF0aW9uIGlzIGV4cGVjdGVkIHRvIHVzZSBpdHMgbm90aW9uXG4gICAgICogICAgICAgIG9mIHRoZSBjdXJyZW50IHRpbWUgb2YgdGhlIGNhbGwuXG4gICAgICovXG4gICAgU3Bhbi5wcm90b3R5cGUubG9nID0gZnVuY3Rpb24gKGtleVZhbHVlUGFpcnMsIHRpbWVzdGFtcCkge1xuICAgICAgICB0aGlzLl9sb2coa2V5VmFsdWVQYWlycywgdGltZXN0YW1wKTtcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgfTtcbiAgICAvKipcbiAgICAgKiBERVBSRUNBVEVEXG4gICAgICovXG4gICAgU3Bhbi5wcm90b3R5cGUubG9nRXZlbnQgPSBmdW5jdGlvbiAoZXZlbnROYW1lLCBwYXlsb2FkKSB7XG4gICAgICAgIHJldHVybiB0aGlzLl9sb2coeyBldmVudDogZXZlbnROYW1lLCBwYXlsb2FkOiBwYXlsb2FkIH0pO1xuICAgIH07XG4gICAgLyoqXG4gICAgICogU2V0cyB0aGUgZW5kIHRpbWVzdGFtcCBhbmQgZmluYWxpemVzIFNwYW4gc3RhdGUuXG4gICAgICpcbiAgICAgKiBXaXRoIHRoZSBleGNlcHRpb24gb2YgY2FsbHMgdG8gU3Bhbi5jb250ZXh0KCkgKHdoaWNoIGFyZSBhbHdheXMgYWxsb3dlZCksXG4gICAgICogZmluaXNoKCkgbXVzdCBiZSB0aGUgbGFzdCBjYWxsIG1hZGUgdG8gYW55IHNwYW4gaW5zdGFuY2UsIGFuZCB0byBkb1xuICAgICAqIG90aGVyd2lzZSBsZWFkcyB0byB1bmRlZmluZWQgYmVoYXZpb3IuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gIHtudW1iZXJ9IGZpbmlzaFRpbWVcbiAgICAgKiAgICAgICAgIE9wdGlvbmFsIGZpbmlzaCB0aW1lIGluIG1pbGxpc2Vjb25kcyBhcyBhIFVuaXggdGltZXN0YW1wLiBEZWNpbWFsXG4gICAgICogICAgICAgICB2YWx1ZXMgYXJlIHN1cHBvcnRlZCBmb3IgdGltZXN0YW1wcyB3aXRoIHN1Yi1taWxsaXNlY29uZCBhY2N1cmFjeS5cbiAgICAgKiAgICAgICAgIElmIG5vdCBzcGVjaWZpZWQsIHRoZSBjdXJyZW50IHRpbWUgKGFzIGRlZmluZWQgYnkgdGhlXG4gICAgICogICAgICAgICBpbXBsZW1lbnRhdGlvbikgd2lsbCBiZSB1c2VkLlxuICAgICAqL1xuICAgIFNwYW4ucHJvdG90eXBlLmZpbmlzaCA9IGZ1bmN0aW9uIChmaW5pc2hUaW1lKSB7XG4gICAgICAgIHRoaXMuX2ZpbmlzaChmaW5pc2hUaW1lKTtcbiAgICAgICAgLy8gRG8gbm90IHJldHVybiBgdGhpc2AuIFRoZSBTcGFuIGdlbmVyYWxseSBzaG91bGQgbm90IGJlIHVzZWQgYWZ0ZXIgaXRcbiAgICAgICAgLy8gaXMgZmluaXNoZWQgc28gY2hhaW5pbmcgaXMgbm90IGRlc2lyZWQgaW4gdGhpcyBjb250ZXh0LlxuICAgIH07XG4gICAgLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSAvL1xuICAgIC8vIERlcml2ZWQgY2xhc3NlcyBjYW4gY2hvb3NlIHRvIGltcGxlbWVudCB0aGUgYmVsb3dcbiAgICAvLyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIC8vXG4gICAgLy8gQnkgZGVmYXVsdCByZXR1cm5zIGEgbm8tb3AgU3BhbkNvbnRleHQuXG4gICAgU3Bhbi5wcm90b3R5cGUuX2NvbnRleHQgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBub29wLnNwYW5Db250ZXh0O1xuICAgIH07XG4gICAgLy8gQnkgZGVmYXVsdCByZXR1cm5zIGEgbm8tb3AgdHJhY2VyLlxuICAgIC8vXG4gICAgLy8gVGhlIGJhc2UgY2xhc3MgY291bGQgc3RvcmUgdGhlIHRyYWNlciB0aGF0IGNyZWF0ZWQgaXQsIGJ1dCBpdCBkb2VzIG5vdFxuICAgIC8vIGluIG9yZGVyIHRvIGVuc3VyZSB0aGUgbm8tb3Agc3BhbiBpbXBsZW1lbnRhdGlvbiBoYXMgemVybyBtZW1iZXJzLFxuICAgIC8vIHdoaWNoIGFsbG93cyBWOCB0byBhZ2dyZXNzaXZlbHkgb3B0aW1pemUgY2FsbHMgdG8gc3VjaCBvYmplY3RzLlxuICAgIFNwYW4ucHJvdG90eXBlLl90cmFjZXIgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBub29wLnRyYWNlcjtcbiAgICB9O1xuICAgIC8vIEJ5IGRlZmF1bHQgZG9lcyBub3RoaW5nXG4gICAgU3Bhbi5wcm90b3R5cGUuX3NldE9wZXJhdGlvbk5hbWUgPSBmdW5jdGlvbiAobmFtZSkge1xuICAgIH07XG4gICAgLy8gQnkgZGVmYXVsdCBkb2VzIG5vdGhpbmdcbiAgICBTcGFuLnByb3RvdHlwZS5fc2V0QmFnZ2FnZUl0ZW0gPSBmdW5jdGlvbiAoa2V5LCB2YWx1ZSkge1xuICAgIH07XG4gICAgLy8gQnkgZGVmYXVsdCBkb2VzIG5vdGhpbmdcbiAgICBTcGFuLnByb3RvdHlwZS5fZ2V0QmFnZ2FnZUl0ZW0gPSBmdW5jdGlvbiAoa2V5KSB7XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfTtcbiAgICAvLyBCeSBkZWZhdWx0IGRvZXMgbm90aGluZ1xuICAgIC8vXG4gICAgLy8gTk9URTogYm90aCBzZXRUYWcoKSBhbmQgYWRkVGFncygpIG1hcCB0byB0aGlzIGZ1bmN0aW9uLiBrZXlWYWx1ZVBhaXJzXG4gICAgLy8gd2lsbCBhbHdheXMgYmUgYW4gYXNzb2NpYXRpdmUgYXJyYXkuXG4gICAgU3Bhbi5wcm90b3R5cGUuX2FkZFRhZ3MgPSBmdW5jdGlvbiAoa2V5VmFsdWVQYWlycykge1xuICAgIH07XG4gICAgLy8gQnkgZGVmYXVsdCBkb2VzIG5vdGhpbmdcbiAgICBTcGFuLnByb3RvdHlwZS5fbG9nID0gZnVuY3Rpb24gKGtleVZhbHVlUGFpcnMsIHRpbWVzdGFtcCkge1xuICAgIH07XG4gICAgLy8gQnkgZGVmYXVsdCBkb2VzIG5vdGhpbmdcbiAgICAvL1xuICAgIC8vIGZpbmlzaFRpbWUgaXMgZXhwZWN0ZWQgdG8gYmUgZWl0aGVyIGEgbnVtYmVyIG9yIHVuZGVmaW5lZC5cbiAgICBTcGFuLnByb3RvdHlwZS5fZmluaXNoID0gZnVuY3Rpb24gKGZpbmlzaFRpbWUpIHtcbiAgICB9O1xuICAgIHJldHVybiBTcGFuO1xufSgpKTtcbmV4cG9ydHMuU3BhbiA9IFNwYW47XG5leHBvcnRzLmRlZmF1bHQgPSBTcGFuO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3Bhbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/span.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/opentracing/lib/span_context.js":
/*!******************************************************!*\
  !*** ./node_modules/opentracing/lib/span_context.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n/**\n * SpanContext represents Span state that must propagate to descendant Spans\n * and across process boundaries.\n *\n * SpanContext is logically divided into two pieces: the user-level \"Baggage\"\n * (see setBaggageItem and getBaggageItem) that propagates across Span\n * boundaries and any Tracer-implementation-specific fields that are needed to\n * identify or otherwise contextualize the associated Span instance (e.g., a\n * <trace_id, span_id, sampled> tuple).\n */\nvar SpanContext = /** @class */ (function () {\n    function SpanContext() {\n    }\n    // The SpanContext is entirely implementation dependent\n    /**\n     * Returns a string representation of the implementation internal trace ID.\n     *\n     * @returns {string}\n     */\n    SpanContext.prototype.toTraceId = function () {\n        return '';\n    };\n    /**\n     * Returns a string representation of the implementation internal span ID.\n     *\n     * @returns {string}\n     */\n    SpanContext.prototype.toSpanId = function () {\n        return '';\n    };\n    return SpanContext;\n}());\nexports.SpanContext = SpanContext;\nexports[\"default\"] = SpanContext;\n//# sourceMappingURL=span_context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/span_context.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/opentracing/lib/tracer.js":
/*!************************************************!*\
  !*** ./node_modules/opentracing/lib/tracer.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nvar Functions = __webpack_require__(/*! ./functions */ \"(action-browser)/./node_modules/opentracing/lib/functions.js\");\nvar Noop = __webpack_require__(/*! ./noop */ \"(action-browser)/./node_modules/opentracing/lib/noop.js\");\nvar span_1 = __webpack_require__(/*! ./span */ \"(action-browser)/./node_modules/opentracing/lib/span.js\");\n/**\n * Tracer is the entry-point between the instrumentation API and the tracing\n * implementation.\n *\n * The default object acts as a no-op implementation.\n *\n * Note to implementators: derived classes can choose to directly implement the\n * methods in the \"OpenTracing API methods\" section, or optionally the subset of\n * underscore-prefixed methods to pick up the argument checking and handling\n * automatically from the base class.\n */\nvar Tracer = /** @class */ (function () {\n    function Tracer() {\n    }\n    // ---------------------------------------------------------------------- //\n    // OpenTracing API methods\n    // ---------------------------------------------------------------------- //\n    /**\n     * Starts and returns a new Span representing a logical unit of work.\n     *\n     * For example:\n     *\n     *     // Start a new (parentless) root Span:\n     *     var parent = Tracer.startSpan('DoWork');\n     *\n     *     // Start a new (child) Span:\n     *     var child = Tracer.startSpan('load-from-db', {\n     *         childOf: parent.context(),\n     *     });\n     *\n     *     // Start a new async (FollowsFrom) Span:\n     *     var child = Tracer.startSpan('async-cache-write', {\n     *         references: [\n     *             opentracing.followsFrom(parent.context())\n     *         ],\n     *     });\n     *\n     * @param {string} name - the name of the operation (REQUIRED).\n     * @param {SpanOptions} [options] - options for the newly created span.\n     * @return {Span} - a new Span object.\n     */\n    Tracer.prototype.startSpan = function (name, options) {\n        if (options === void 0) { options = {}; }\n        // Convert options.childOf to fields.references as needed.\n        if (options.childOf) {\n            // Convert from a Span or a SpanContext into a Reference.\n            var childOf = Functions.childOf(options.childOf);\n            if (options.references) {\n                options.references.push(childOf);\n            }\n            else {\n                options.references = [childOf];\n            }\n            delete (options.childOf);\n        }\n        return this._startSpan(name, options);\n    };\n    /**\n     * Injects the given SpanContext instance for cross-process propagation\n     * within `carrier`. The expected type of `carrier` depends on the value of\n     * `format.\n     *\n     * OpenTracing defines a common set of `format` values (see\n     * FORMAT_TEXT_MAP, FORMAT_HTTP_HEADERS, and FORMAT_BINARY), and each has\n     * an expected carrier type.\n     *\n     * Consider this pseudocode example:\n     *\n     *     var clientSpan = ...;\n     *     ...\n     *     // Inject clientSpan into a text carrier.\n     *     var headersCarrier = {};\n     *     Tracer.inject(clientSpan.context(), Tracer.FORMAT_HTTP_HEADERS, headersCarrier);\n     *     // Incorporate the textCarrier into the outbound HTTP request header\n     *     // map.\n     *     Object.assign(outboundHTTPReq.headers, headersCarrier);\n     *     // ... send the httpReq\n     *\n     * @param  {SpanContext} spanContext - the SpanContext to inject into the\n     *         carrier object. As a convenience, a Span instance may be passed\n     *         in instead (in which case its .context() is used for the\n     *         inject()).\n     * @param  {string} format - the format of the carrier.\n     * @param  {any} carrier - see the documentation for the chosen `format`\n     *         for a description of the carrier object.\n     */\n    Tracer.prototype.inject = function (spanContext, format, carrier) {\n        // Allow the user to pass a Span instead of a SpanContext\n        if (spanContext instanceof span_1.default) {\n            spanContext = spanContext.context();\n        }\n        return this._inject(spanContext, format, carrier);\n    };\n    /**\n     * Returns a SpanContext instance extracted from `carrier` in the given\n     * `format`.\n     *\n     * OpenTracing defines a common set of `format` values (see\n     * FORMAT_TEXT_MAP, FORMAT_HTTP_HEADERS, and FORMAT_BINARY), and each has\n     * an expected carrier type.\n     *\n     * Consider this pseudocode example:\n     *\n     *     // Use the inbound HTTP request's headers as a text map carrier.\n     *     var headersCarrier = inboundHTTPReq.headers;\n     *     var wireCtx = Tracer.extract(Tracer.FORMAT_HTTP_HEADERS, headersCarrier);\n     *     var serverSpan = Tracer.startSpan('...', { childOf : wireCtx });\n     *\n     * @param  {string} format - the format of the carrier.\n     * @param  {any} carrier - the type of the carrier object is determined by\n     *         the format.\n     * @return {SpanContext}\n     *         The extracted SpanContext, or null if no such SpanContext could\n     *         be found in `carrier`\n     */\n    Tracer.prototype.extract = function (format, carrier) {\n        return this._extract(format, carrier);\n    };\n    // ---------------------------------------------------------------------- //\n    // Derived classes can choose to implement the below\n    // ---------------------------------------------------------------------- //\n    // NOTE: the input to this method is *always* an associative array. The\n    // public-facing startSpan() method normalizes the arguments so that\n    // all N implementations do not need to worry about variations in the call\n    // signature.\n    //\n    // The default behavior returns a no-op span.\n    Tracer.prototype._startSpan = function (name, fields) {\n        return Noop.span;\n    };\n    // The default behavior is a no-op.\n    Tracer.prototype._inject = function (spanContext, format, carrier) {\n    };\n    // The default behavior is to return a no-op SpanContext.\n    Tracer.prototype._extract = function (format, carrier) {\n        return Noop.spanContext;\n    };\n    return Tracer;\n}());\nexports.Tracer = Tracer;\nexports[\"default\"] = Tracer;\n//# sourceMappingURL=tracer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/opentracing/lib/tracer.js\n");

/***/ })

};
;