import os
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PB<PERSON>DF2HMAC
from flask import current_app
import logging

logger = logging.getLogger(__name__)

class EncryptionError(Exception):
    """Custom exception for encryption failures."""
    pass

class DecryptionError(Exception):
    """Custom exception for decryption failures."""
    pass

def get_derived_key():
    """
    Derives a Fernet-compatible key from the master ENCRYPTION_KEY and ENCRYPTION_SALT
    loaded from the application configuration.
    """
    master_key_str = current_app.config.get('ENCRYPTION_KEY')
    salt_str = current_app.config.get('ENCRYPTION_SALT')

    if not master_key_str:
        logger.critical("ENCRYPTION_KEY not found in app configuration.")
        raise EncryptionError("ENCRYPTION_KEY is not configured.")
    if not salt_str:
        logger.critical("ENCRYPTION_SALT not found in app configuration.")
        raise EncryptionError("ENCRYPTION_SALT is not configured.")

    try:
        # Assuming salt_str is stored as a hex string or base64 string that needs decoding
        # If it's raw bytes in config, adjust accordingly
        # For this example, let's assume it's a hex string from os.urandom(16).hex()
        salt_bytes = bytes.fromhex(salt_str)
    except ValueError as e:
        logger.critical(f"ENCRYPTION_SALT is not a valid hex string: {e}")
        raise EncryptionError("ENCRYPTION_SALT is improperly configured.")

    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32, # Fernet key length
        salt=salt_bytes,
        iterations=390000, # Increased iterations for PBKDF2
    )
    
    master_key_bytes = master_key_str.encode('utf-8')
    derived_key = base64.urlsafe_b64encode(kdf.derive(master_key_bytes))
    return derived_key

def encrypt_data(data: str) -> str:
    """Encrypt sensitive data. Raises EncryptionError on failure."""
    if not data:
        # Or raise ValueError("Cannot encrypt empty data")
        return None
    
    try:
        derived_key = get_derived_key()
        f = Fernet(derived_key)
        encrypted_data_bytes = f.encrypt(data.encode('utf-8'))
        return encrypted_data_bytes.decode('utf-8')
    except EncryptionError: # Propagate if from get_derived_key
        raise
    except Exception as e:
        logger.error(f"Encryption failed: {str(e)}", exc_info=True)
        raise EncryptionError(f"Encryption failed: {str(e)}")

def decrypt_data(encrypted_data: str) -> str:
    """Decrypt encrypted data. Raises DecryptionError on failure."""
    if not encrypted_data:
        return None
    
    try:
        derived_key = get_derived_key()
        f = Fernet(derived_key)
        decrypted_data_bytes = f.decrypt(encrypted_data.encode('utf-8'))
        return decrypted_data_bytes.decode('utf-8')
    except EncryptionError: # If get_derived_key failed during decryption context
        logger.error("Decryption failed because key derivation failed.")
        raise DecryptionError("Key derivation failed during decryption.")
    except (Fernet.InvalidToken, TypeError, ValueError) as e: # Specific Fernet decrypt errors
        logger.error(f"Decryption failed (InvalidToken/TypeError/ValueError): {str(e)}", exc_info=False) # exc_info=False as token might be sensitive
        raise DecryptionError(f"Decryption failed, data may be tampered or key incorrect: {str(e)}")
    except Exception as e:
        logger.error(f"Decryption failed: {str(e)}", exc_info=True)
        raise DecryptionError(f"Decryption failed: {str(e)}") 