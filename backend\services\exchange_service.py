import logging
import ccxt
import time
import os
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

# Constants for retries
MAX_RETRIES = 3
RETRY_DELAY_SECONDS = 1

class ExchangeService:
    @staticmethod
    def get_exchange_instance(api_key=None, use_app_keys=False, exchange_name='binance'):
        """
        Create and return an exchange instance with appropriate configuration.
        """
        # Configure exchange with sensible defaults
        exchange_config = {
            'enableRateLimit': True,
            'timeout': 30000, # 30 seconds
        }

        # Use API key if provided
        if api_key:
            exchange_config['apiKey'] = api_key.api_key
            exchange_config['secret'] = api_key.secret_key
        elif use_app_keys:
            # Use application-level API keys from environment
            # This would typically be read from settings/environment vars
            # For now, we'll leave these fields empty and rely on public endpoints
            pass

        # Configure testnet/sandbox mode based on environment
        if exchange_name.lower() == 'binance':
            # Check if we should use testnet (default True for safety)
            use_testnet = os.getenv('BINANCE_TESTNET', 'True').lower() == 'true'

            if use_testnet:
                exchange_config['sandbox'] = True
                logger.info("🧪 BINANCE TESTNET MODE ENABLED - No real trades will be executed")
            else:
                exchange_config['sandbox'] = False
                logger.warning("🚨 BINANCE LIVE MODE ENABLED - REAL TRADES WILL BE EXECUTED!")

        # Create the exchange instance
        try:
            exchange = getattr(ccxt, exchange_name.lower())(exchange_config)
            return exchange
        except AttributeError:
            logger.error(f"Exchange '{exchange_name}' not supported by ccxt")
            raise ValueError(f"Exchange '{exchange_name}' not supported")
        except Exception as e:
            logger.error(f"Error creating exchange instance: {str(e)}")
            raise

    @staticmethod
    def get_ticker_price(symbol, exchange_instance=None):
        """
        Get the current ticker price for a symbol with retries for transient errors.
        """
        retries = 0
        while retries < MAX_RETRIES:
            try:
                if not exchange_instance:
                    # Assuming app keys for general price feeds if no specific user context
                    exchange_instance = ExchangeService.get_exchange_instance(use_app_keys=True)

                ticker = exchange_instance.fetch_ticker(symbol)
                if ticker and 'last' in ticker and ticker['last'] is not None:
                    return ticker['last']
                else:
                    logger.warning(f"Price for {symbol} not available in ticker: {ticker}")
                    # Depending on strictness, could raise error or return None/previous
                    raise ccxt.ExchangeError(f"Price for {symbol} not available in ticker response.")

            except (ccxt.RequestTimeout, ccxt.DDoSProtection, ccxt.ExchangeNotAvailable, ccxt.OnMaintenance, ccxt.NetworkError) as e_transient:
                retries += 1
                logger.warning(f"Error fetching price for {symbol} (Attempt {retries}/{MAX_RETRIES}): {type(e_transient).__name__} - {str(e_transient)}. Retrying in {RETRY_DELAY_SECONDS}s...")
                if retries < MAX_RETRIES:
                    time.sleep(RETRY_DELAY_SECONDS)
                else:
                    logger.error(f"Failed to fetch price for {symbol} after {MAX_RETRIES} attempts: {str(e_transient)}")
                    raise  # Re-raise the last transient error
            except ccxt.BadSymbol as e_bad_symbol:
                logger.error(f"Error fetching price for {symbol}: Invalid symbol. {str(e_bad_symbol)}")
                raise # Non-recoverable for this call
            except ccxt.ExchangeError as e_exchange:
                logger.error(f"Exchange error fetching price for {symbol}: {type(e_exchange).__name__} - {str(e_exchange)}")
                raise # Other specific exchange errors
            except Exception as e_generic:
                logger.error(f"Unexpected error fetching price for {symbol}: {type(e_generic).__name__} - {str(e_generic)}")
                raise # Non-CCXT errors
        return None # Should not be reached if MAX_RETRIES > 0 and an exception is always raised on failure

    @staticmethod
    def get_account_balance(api_key):
        """
        Get account balance from exchange with retries for transient errors.
        """
        retries = 0
        while retries < MAX_RETRIES:
            try:
                exchange = ExchangeService.get_exchange_instance(api_key=api_key)
                balance = exchange.fetch_balance()
                result = {currency: data['free'] for currency, data in balance['total'].items() if data['free'] > 0} # fetch_balance structure
                return result
            except (ccxt.RequestTimeout, ccxt.DDoSProtection, ccxt.ExchangeNotAvailable, ccxt.OnMaintenance, ccxt.NetworkError) as e_transient:
                retries += 1
                logger.warning(f"Error fetching balance (Attempt {retries}/{MAX_RETRIES}): {type(e_transient).__name__} - {str(e_transient)}. Retrying in {RETRY_DELAY_SECONDS}s...")
                if retries < MAX_RETRIES:
                    time.sleep(RETRY_DELAY_SECONDS)
                else:
                    logger.error(f"Failed to fetch balance after {MAX_RETRIES} attempts: {str(e_transient)}")
                    raise
            except ccxt.AuthenticationError as e_auth:
                logger.error(f"Authentication error fetching balance: {str(e_auth)}")
                raise
            except ccxt.ExchangeError as e_exchange:
                logger.error(f"Exchange error fetching balance: {type(e_exchange).__name__} - {str(e_exchange)}")
                raise
            except Exception as e_generic:
                logger.error(f"Unexpected error fetching balance: {type(e_generic).__name__} - {str(e_generic)}")
                raise
        return None # Should not be reached

    @staticmethod
    def place_order(api_key, symbol, order_type, side, amount, price=None):
        """
        Place an order on the exchange with retries for certain transient errors.
        """
        retries = 0
        while retries < MAX_RETRIES:
            try:
                exchange = ExchangeService.get_exchange_instance(api_key=api_key)

                if order_type.lower() == 'limit' and price is None:
                    logger.error(f"Price must be provided for limit orders. Symbol: {symbol}")
                    raise ValueError("Price must be provided for limit orders")

                params = {} # No specific extra params for now, but placeholder

                logger.info(f"Attempting to place order: {side} {amount} {symbol} {order_type} @ {price if price else 'market'}")

                # Before placing, you might want to fetch market data for precision/min_amount checks if not done by caller
                # markets = exchange.load_markets()
                # market = markets[symbol]
                # amount = exchange.amount_to_precision(symbol, amount)
                # price = exchange.price_to_precision(symbol, price) # if limit order
                # Check against market['limits'] for amount and cost

                if order_type.lower() == 'market':
                    order = exchange.create_market_order(symbol, side, amount, params=params)
                else: # limit
                    order = exchange.create_limit_order(symbol, side, amount, price, params=params)

                logger.info(f"Order placed successfully: ID {order.get('id', 'N/A')}, {side} {amount} {symbol} at {price if order_type.lower() == 'limit' else 'market price'}. Full response: {order}")
                return order

            except (ccxt.RequestTimeout, ccxt.DDoSProtection, ccxt.ExchangeNotAvailable, ccxt.OnMaintenance) as e_transient: # NetworkError is base for some of these
                # Do not retry InsufficientFunds or InvalidOrder, as they are unlikely to be resolved by a simple retry.
                retries += 1
                logger.warning(f"Transient error placing order for {symbol} (Attempt {retries}/{MAX_RETRIES}): {type(e_transient).__name__} - {str(e_transient)}. Retrying in {RETRY_DELAY_SECONDS}s...")
                if retries < MAX_RETRIES:
                    time.sleep(RETRY_DELAY_SECONDS)
                else:
                    logger.error(f"Failed to place order for {symbol} after {MAX_RETRIES} attempts due to transient error: {str(e_transient)}")
                    raise
            except ccxt.InsufficientFunds as e_funds:
                logger.error(f"Insufficient funds to place order for {symbol}: {str(e_funds)}")
                raise
            except ccxt.InvalidOrder as e_invalid_order:
                logger.error(f"Invalid order parameters for {symbol}: {str(e_invalid_order)}")
                # This could be due to min notional, min amount, step size, price precision issues.
                # Consider logging market_info.get('limits') if available here.
                raise
            except ccxt.BadSymbol as e_bad_symbol:
                logger.error(f"Cannot place order, invalid symbol: {symbol}. {str(e_bad_symbol)}")
                raise
            except ccxt.AuthenticationError as e_auth:
                logger.error(f"Authentication error placing order for {symbol}: {str(e_auth)}")
                raise
            except ccxt.RateLimitExceeded as e_rate_limit:
                # RateLimitExceeded is tricky. A simple immediate retry might not be best.
                # CCXT's built-in rate limiter should handle most cases if 'enableRateLimit': True.
                # If this is still hit, it might be a more aggressive limit from the exchange.
                # For now, treat as a non-retryable error for this specific attempt, caller might retry later.
                logger.error(f"Rate limit exceeded placing order for {symbol}: {str(e_rate_limit)}")
                raise
            except ccxt.ExchangeError as e_exchange: # Catch-all for other CCXT exchange errors
                logger.error(f"Exchange error placing order for {symbol}: {type(e_exchange).__name__} - {str(e_exchange)}")
                raise
            except Exception as e_generic:
                logger.error(f"Unexpected error placing order for {symbol}: {type(e_generic).__name__} - {str(e_generic)}")
                raise
        return None # Should not be reached

    @staticmethod
    def get_order_status(api_key, symbol, order_id):
        """
        Get the status of an order with retries for transient errors.
        """
        retries = 0
        while retries < MAX_RETRIES:
            try:
                exchange = ExchangeService.get_exchange_instance(api_key=api_key)
                order = exchange.fetch_order(order_id, symbol)
                return order
            except ccxt.OrderNotFound as e_not_found:
                logger.warning(f"Order {order_id} for {symbol} not found on exchange: {str(e_not_found)}")
                raise # This is a specific state, not necessarily an error to retry indefinitely.
            except (ccxt.RequestTimeout, ccxt.DDoSProtection, ccxt.ExchangeNotAvailable, ccxt.OnMaintenance, ccxt.NetworkError) as e_transient:
                retries += 1
                logger.warning(f"Error getting status for order {order_id} (Attempt {retries}/{MAX_RETRIES}): {type(e_transient).__name__} - {str(e_transient)}. Retrying in {RETRY_DELAY_SECONDS}s...")
                if retries < MAX_RETRIES:
                    time.sleep(RETRY_DELAY_SECONDS)
                else:
                    logger.error(f"Failed to get status for order {order_id} after {MAX_RETRIES} attempts: {str(e_transient)}")
                    raise
            except ccxt.AuthenticationError as e_auth:
                logger.error(f"Authentication error getting status for order {order_id}: {str(e_auth)}")
                raise
            except ccxt.ExchangeError as e_exchange:
                logger.error(f"Exchange error getting status for order {order_id}: {type(e_exchange).__name__} - {str(e_exchange)}")
                raise
            except Exception as e_generic:
                logger.error(f"Unexpected error getting status for order {order_id}: {type(e_generic).__name__} - {str(e_generic)}")
                raise
        return None # Should not be reached

    @staticmethod
    def cancel_order(api_key, symbol, order_id):
        """
        Cancel an existing order with retries for transient errors.
        """
        retries = 0
        while retries < MAX_RETRIES:
            try:
                exchange = ExchangeService.get_exchange_instance(api_key=api_key)
                logger.info(f"Attempting to cancel order {order_id} for {symbol}")
                result = exchange.cancel_order(order_id, symbol)
                logger.info(f"Order {order_id} for {symbol} cancel request sent. Response: {result}")
                return result
            except ccxt.OrderNotFound as e_not_found:
                # If the order is already filled or canceled, it might not be found.
                # This is often not an error in the sense that the order is no longer active.
                logger.warning(f"Attempted to cancel order {order_id} for {symbol}, but it was not found (might be already filled/canceled): {str(e_not_found)}")
                # Depending on desired behavior, you might return a specific status or the exception info.
                # For now, let's re-raise as it indicates the order isn't open to be cancelled via this call.
                raise
            except (ccxt.RequestTimeout, ccxt.DDoSProtection, ccxt.ExchangeNotAvailable, ccxt.OnMaintenance, ccxt.NetworkError) as e_transient:
                retries += 1
                logger.warning(f"Error canceling order {order_id} (Attempt {retries}/{MAX_RETRIES}): {type(e_transient).__name__} - {str(e_transient)}. Retrying in {RETRY_DELAY_SECONDS}s...")
                if retries < MAX_RETRIES:
                    time.sleep(RETRY_DELAY_SECONDS)
                else:
                    logger.error(f"Failed to cancel order {order_id} after {MAX_RETRIES} attempts: {str(e_transient)}")
                    raise
            except ccxt.AuthenticationError as e_auth:
                logger.error(f"Authentication error canceling order {order_id}: {str(e_auth)}")
                raise
            except ccxt.ExchangeError as e_exchange:
                logger.error(f"Exchange error canceling order {order_id}: {type(e_exchange).__name__} - {str(e_exchange)}")
                raise
            except Exception as e_generic:
                logger.error(f"Unexpected error canceling order {order_id}: {type(e_generic).__name__} - {str(e_generic)}")
                raise
        return None # Should not be reached

    @staticmethod
    def get_available_trading_pairs(exchange_name='binance'):
        """
        Get all available trading pairs from the exchange.
        Returns a dictionary with base currencies as keys and lists of quote currencies as values.
        """
        try:
            exchange = ExchangeService.get_exchange_instance(use_app_keys=True, exchange_name=exchange_name)
            markets = exchange.load_markets()

            # Group by base currency
            trading_pairs = {}
            for symbol, market in markets.items():
                if market['active'] and market['spot']:  # Only active spot markets
                    base = market['base']
                    quote = market['quote']

                    if base not in trading_pairs:
                        trading_pairs[base] = []

                    if quote not in trading_pairs[base]:
                        trading_pairs[base].append(quote)

            # Sort quote currencies for each base
            for base in trading_pairs:
                trading_pairs[base].sort()

            logger.info(f"Fetched {len(trading_pairs)} base currencies with trading pairs from {exchange_name}")
            return trading_pairs

        except Exception as e:
            logger.error(f"Error fetching trading pairs from {exchange_name}: {str(e)}")
            # Return empty dict on error, frontend can fall back to static list
            return {}

    @staticmethod
    def get_available_cryptocurrencies(exchange_name='binance'):
        """
        Get all available cryptocurrencies (base currencies) from the exchange.
        """
        try:
            trading_pairs = ExchangeService.get_available_trading_pairs(exchange_name)
            cryptocurrencies = list(trading_pairs.keys())
            cryptocurrencies.sort()

            logger.info(f"Fetched {len(cryptocurrencies)} cryptocurrencies from {exchange_name}")
            return cryptocurrencies

        except Exception as e:
            logger.error(f"Error fetching cryptocurrencies from {exchange_name}: {str(e)}")
            return []