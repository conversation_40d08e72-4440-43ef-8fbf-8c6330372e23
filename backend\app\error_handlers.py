from flask import jsonify
from werkzeug.exceptions import NotFound, BadRequest, Unauthorized, Forbidden
import logging

logger = logging.getLogger(__name__)

def register_error_handlers(app):
    """Register error handlers for the application."""
    
    @app.errorhandler(NotFound)
    def handle_not_found_error(e):
        logger.error(f"Not Found Error: {str(e)}")
        return jsonify({"error": "Resource not found", "message": str(e)}), 404
        
    @app.errorhandler(BadRequest)
    def handle_bad_request_error(e):
        logger.error(f"Bad Request Error: {str(e)}")
        return jsonify({"error": "Bad request", "message": str(e)}), 400
        
    @app.errorhandler(Unauthorized)
    def handle_unauthorized_error(e):
        logger.error(f"Unauthorized Error: {str(e)}")
        return jsonify({"error": "Unauthorized", "message": "Authentication required"}), 401
        
    @app.errorhandler(Forbidden)
    def handle_forbidden_error(e):
        logger.error(f"Forbidden Error: {str(e)}")
        return jsonify({"error": "Forbidden", "message": "You don't have permission to access this resource"}), 403
        
    @app.errorhandler(Exception)
    def handle_generic_exception(e):
        logger.error(f"Unhandled Exception: {str(e)}", exc_info=True)
        return jsonify({
            "error": "Internal server error", 
            "message": "An unexpected error occurred. Please try again later."
        }), 500 