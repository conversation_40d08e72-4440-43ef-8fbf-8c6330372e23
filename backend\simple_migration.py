#!/usr/bin/env python3
"""
Simple database migration to add cooldown tracking fields.
"""

import sys
import os

# Add the backend directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from db import db

def run_migration():
    """Run the database migration."""
    
    app = create_app()
    
    with app.app_context():
        try:
            print("🔧 Running simple database migration...")
            
            # Create all tables (this will add new columns if they don't exist)
            db.create_all()
            
            print("✅ Database migration completed successfully!")
            print("🎯 Crystal clear cooldown tracking is now properly implemented!")
            
            # Verify the columns exist
            inspector = db.inspect(db.engine)
            columns = [col['name'] for col in inspector.get_columns('bot_states')]
            print(f"📊 Current bot_states columns: {columns}")
            
            if 'last_global_buy_timestamp' in columns and 'last_sell_timestamp_per_counter' in columns:
                print("✅ All required cooldown tracking columns are present!")
            else:
                print("❌ Some columns are missing. Manual intervention may be required.")
            
        except Exception as e:
            print(f"❌ Migration failed: {e}")
            raise

if __name__ == "__main__":
    run_migration()
