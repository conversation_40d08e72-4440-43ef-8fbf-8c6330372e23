.PHONY: setup run dev test clean

setup:
	@echo "Setting up virtual environment..."
	python -m venv venv
	@echo "Installing requirements..."
	venv/bin/pip install -r requirements.txt
	@echo "Setup complete. Activate the virtual environment with: source venv/bin/activate (Linux/Mac) or venv\\Scripts\\activate (Windows)"

run:
	@echo "Starting Pluto Trading Bot..."
	venv/bin/python run.py

dev:
	@echo "Starting Pluto Trading Bot in development mode..."
	FLASK_DEBUG=true venv/bin/python run.py

test:
	@echo "Running tests..."
	venv/bin/pytest

clean:
	@echo "Cleaning up..."
	rm -rf __pycache__ .pytest_cache
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete
	@echo "Cleanup complete"

help:
	@echo "Available commands:"
	@echo "  make setup  - Set up virtual environment and install dependencies"
	@echo "  make run    - Run the Pluto Trading Bot"
	@echo "  make dev    - Run in development mode with debug enabled"
	@echo "  make test   - Run the test suite"
	@echo "  make clean  - Clean up Python cache files" 