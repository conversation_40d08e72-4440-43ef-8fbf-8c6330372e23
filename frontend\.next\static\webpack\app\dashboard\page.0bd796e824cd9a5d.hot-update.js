"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/OrdersTable.tsx":
/*!**************************************************!*\
  !*** ./src/components/dashboard/OrdersTable.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ OrdersTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction OrdersTable() {\n    _s();\n    const { getDisplayOrders, config, currentMarketPrice } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    const displayOrders = getDisplayOrders();\n    const formatNumber = function(num) {\n        let forceSign = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (num === undefined || num === null || isNaN(num)) return \"-\";\n        const fixedNum = num.toFixed(config.numDigits);\n        if (forceSign && num > 0) return \"+\".concat(fixedNum);\n        return fixedNum;\n    };\n    const formatPercent = (num)=>{\n        if (num === undefined || num === null || isNaN(num)) return \"-\";\n        return \"\".concat(num.toFixed(2), \"%\");\n    };\n    const columns = [\n        {\n            key: \"#\",\n            label: \"#\"\n        },\n        {\n            key: \"status\",\n            label: \"Status\"\n        },\n        {\n            key: \"orderLevel\",\n            label: \"Level\"\n        },\n        {\n            key: \"valueLevel\",\n            label: \"Value\"\n        },\n        {\n            key: \"crypto2Var\",\n            label: \"\".concat(config.crypto2 || \"Crypto 2\", \" Var.\")\n        },\n        {\n            key: \"crypto1Var\",\n            label: \"\".concat(config.crypto1 || \"Crypto 1\", \" Var.\")\n        },\n        {\n            key: \"targetPrice\",\n            label: \"Target Price\"\n        },\n        {\n            key: \"percentFromActualPrice\",\n            label: \"% from Actual\"\n        },\n        {\n            key: \"originalCostCrypto2\",\n            label: \"Original Cost \".concat(config.crypto2 || \"Crypto 2\")\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-2 border-border rounded-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollArea, {\n            className: \"w-full whitespace-nowrap\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.Table, {\n                    className: \"min-w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                className: \"bg-card hover:bg-card\",\n                                children: columns.map((col)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                        className: \"font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm\",\n                                        children: col.label\n                                    }, col.key, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableBody, {\n                            children: displayOrders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    colSpan: columns.length,\n                                    className: \"h-24 text-center text-muted-foreground\",\n                                    children: 'No target prices set. Use \"Set Target Prices\" in the sidebar.'\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 15\n                            }, this) : displayOrders.map((row)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                    className: \"hover:bg-card/80\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs\",\n                                            children: row.counter\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                variant: row.status === \"Full\" ? \"default\" : \"secondary\",\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(row.status === \"Full\" ? \"bg-green-600 text-white\" : \"bg-yellow-500 text-black\", \"font-bold\"),\n                                                children: row.status\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 70,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs\",\n                                            children: row.orderLevel\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs\",\n                                            children: formatNumber(row.valueLevel)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-3 py-2 text-xs\", row.crypto2Var && row.crypto2Var < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                            children: formatNumber(row.crypto2Var, true)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-3 py-2 text-xs\", row.crypto1Var && row.crypto1Var < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                            children: formatNumber(row.crypto1Var, true)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs font-semibold text-primary\",\n                                            children: formatNumber(row.targetPrice)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.cn)(\"px-3 py-2 text-xs\", row.percentFromActualPrice < 0 ? \"text-destructive\" : \"text-green-400\"),\n                                            children: formatPercent(row.percentFromActualPrice)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                            className: \"px-3 py-2 text-xs\",\n                                            children: formatNumber(row.originalCostCrypto2)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, row.id, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_4__.ScrollBar, {\n                    orientation: \"horizontal\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\OrdersTable.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_s(OrdersTable, \"UL7ACZxyGIzSlHTRji6CgpbrwIo=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext\n    ];\n});\n_c = OrdersTable;\nvar _c;\n$RefreshReg$(_c, \"OrdersTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/OrdersTable.tsx\n"));

/***/ })

});