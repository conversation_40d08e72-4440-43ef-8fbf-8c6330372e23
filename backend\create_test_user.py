import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from db import db, register_models
from models.user_model import User

def create_test_user():
    """Create a test user for development purposes."""
    app = create_app()
    
    with app.app_context():
        # Check if test user already exists
        test_user = User.query.filter_by(username='testuser').first()
        
        if test_user:
            print(f"Test user 'testuser' already exists with ID: {test_user.id}")
        else:
            # Create and add test user
            new_user = User(
                username='testuser',
                password='password123',
                email='<EMAIL>'
            )
            
            db.session.add(new_user)
            db.session.commit()
            print(f"Created test user 'testuser' with ID: {new_user.id}")
            print("Username: testuser")
            print("Password: password123")

if __name__ == "__main__":
    create_test_user() 