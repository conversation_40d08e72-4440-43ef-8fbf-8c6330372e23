"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_modals_SessionAlarmModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/SessionAlarmModal */ \"(app-pages-browser)/./src/components/modals/SessionAlarmModal.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    _s();\n    const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSessionId, setEditingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingName, setEditingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentRuntime, setCurrentRuntime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [alarmModalOpen, setAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAlarmSession, setSelectedAlarmSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            loadSessions();\n            const sessionId = sessionManager.getCurrentSessionId();\n            setCurrentSessionId(sessionId);\n            // Initialize current runtime\n            if (sessionId) {\n                const runtime = sessionManager.getCurrentRuntime(sessionId);\n                setCurrentRuntime(runtime);\n            }\n            // Listen for storage changes to sync sessions across windows\n            const handleStorageChange = {\n                \"SessionManager.useEffect.handleStorageChange\": (event)=>{\n                    if (event.key === 'trading_sessions' && event.newValue) {\n                        loadSessions();\n                        console.log('🔄 Sessions synced from another window');\n                    }\n                }\n            }[\"SessionManager.useEffect.handleStorageChange\"];\n            window.addEventListener('storage', handleStorageChange);\n            return ({\n                \"SessionManager.useEffect\": ()=>{\n                    window.removeEventListener('storage', handleStorageChange);\n                }\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], []);\n    // Update runtime display every 10 seconds for active sessions (reduced to prevent flickering)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            const interval = setInterval({\n                \"SessionManager.useEffect.interval\": ()=>{\n                    if (currentSessionId && currentSessionId !== 'undefined') {\n                        const runtime = sessionManager.getCurrentRuntime(currentSessionId);\n                        setCurrentRuntime(runtime);\n                    // Removed console log to reduce noise\n                    }\n                }\n            }[\"SessionManager.useEffect.interval\"], 10000); // Update every 10 seconds to reduce flickering\n            return ({\n                \"SessionManager.useEffect\": ()=>clearInterval(interval)\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], [\n        currentSessionId,\n        sessionManager\n    ]);\n    const loadSessions = ()=>{\n        console.log('🔄 Loading sessions...');\n        // Run integrity check before loading\n        sessionManager.runIntegrityCheck();\n        // Clean up any corrupted sessions\n        const removedCount = sessionManager.cleanupCorruptedSessions();\n        if (removedCount > 0) {\n            toast({\n                title: \"Sessions Cleaned\",\n                description: \"Removed \".concat(removedCount, \" corrupted session(s)\")\n            });\n        }\n        const allSessions = sessionManager.getAllSessions();\n        console.log(\"\\uD83D\\uDD04 Loaded \".concat(allSessions.length, \" valid sessions\"));\n        setSessions(allSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const handleSaveCurrentSession = async ()=>{\n        console.log('💾 Save button clicked - currentSessionId:', currentSessionId);\n        if (!currentSessionId || currentSessionId === 'undefined') {\n            console.error('❌ Invalid session ID:', currentSessionId);\n            toast({\n                title: \"Error\",\n                description: \"No valid session to save. Please start trading first.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            // Check if there's already a saved version of this session\n            const currentSession = sessionManager.loadSession(currentSessionId);\n            if (!currentSession) {\n                toast({\n                    title: \"Error\",\n                    description: \"Current session not found\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Look for existing saved session with same base name (only manually saved ones)\n            const allSessions = sessionManager.getAllSessions();\n            const baseName = currentSession.name.replace(/ \\((Saved|AutoSaved).*\\)$/, ''); // Remove existing timestamp\n            const existingSavedSession = allSessions.find((s)=>s.id !== currentSessionId && s.name.startsWith(baseName) && s.name.includes('(Saved') && // Only look for manually saved sessions\n                !s.isActive // Only look in inactive sessions\n            );\n            let targetSessionId;\n            let savedName;\n            if (existingSavedSession) {\n                // Update existing saved session - update the timestamp to show latest save\n                targetSessionId = existingSavedSession.id;\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                console.log(\"\\uD83D\\uDCDD Updating existing saved session: \".concat(savedName));\n            } else {\n                // Create new saved session with timestamp\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                targetSessionId = await sessionManager.createNewSession(savedName, config);\n                console.log(\"\\uD83D\\uDCBE Creating new saved session: \".concat(savedName));\n            }\n            // Get current runtime from the active session\n            const currentRuntime = sessionManager.getCurrentRuntime(currentSessionId);\n            // Update the session name if it's an existing session\n            if (existingSavedSession) {\n                sessionManager.renameSession(targetSessionId, savedName);\n            }\n            // Save/update the session with current data and runtime\n            const success = sessionManager.saveSession(targetSessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, false, currentRuntime // Pass the current runtime to the saved session\n            );\n            // DO NOT deactivate the current session - keep it running!\n            // The current session should remain active for continued trading\n            if (success) {\n                loadSessions();\n                toast({\n                    title: \"Session Saved\",\n                    description: existingSavedSession ? \"Save checkpoint updated (Runtime: \".concat(formatRuntime(currentRuntime), \")\") : \"Session saved as checkpoint (Runtime: \".concat(formatRuntime(currentRuntime), \")\")\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to save session\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error saving session:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save session\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleLoadSession = (sessionId)=>{\n        console.log('📂 Loading session:', sessionId);\n        if (!sessionId || sessionId === 'undefined' || sessionId === 'null') {\n            console.error('❌ Invalid session ID for loading:', sessionId);\n            toast({\n                title: \"Error\",\n                description: \"Invalid session ID - cannot load session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const session = sessionManager.loadSession(sessionId);\n            console.log('📂 Session loaded from manager:', session ? 'Success' : 'Failed');\n            if (!session) {\n                console.error('❌ Session not found in manager:', sessionId);\n                toast({\n                    title: \"Error\",\n                    description: \"Session not found - it may have been deleted or corrupted\",\n                    variant: \"destructive\"\n                });\n                // Refresh sessions list to remove stale entries\n                loadSessions();\n                return;\n            }\n            // Validate session has required properties\n            if (!session.config || !session.name) {\n                console.error('❌ Session is corrupted:', session);\n                toast({\n                    title: \"Error\",\n                    description: \"Session data is corrupted and cannot be loaded\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            console.log('📂 Loading session data into context...');\n            // Load session data into context\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: session.config\n            });\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: session.targetPriceRows || []\n            });\n            dispatch({\n                type: 'CLEAR_ORDER_HISTORY'\n            });\n            if (session.orderHistory && Array.isArray(session.orderHistory)) {\n                session.orderHistory.forEach((entry)=>{\n                    dispatch({\n                        type: 'ADD_ORDER_HISTORY_ENTRY',\n                        payload: entry\n                    });\n                });\n            }\n            dispatch({\n                type: 'SET_MARKET_PRICE',\n                payload: session.currentMarketPrice || 0\n            });\n            dispatch({\n                type: 'SET_BALANCES',\n                payload: {\n                    crypto1: session.crypto1Balance || 10,\n                    crypto2: session.crypto2Balance || 100000\n                }\n            });\n            sessionManager.setCurrentSession(sessionId);\n            setCurrentSessionId(sessionId);\n            loadSessions();\n            console.log('✅ Session loaded successfully:', session.name);\n            toast({\n                title: \"Session Loaded\",\n                description: 'Session \"'.concat(session.name, '\" has been loaded. Redirecting to dashboard...')\n            });\n            // Navigate to dashboard after loading session (don't start bot automatically)\n            setTimeout(()=>{\n                router.push('/dashboard');\n            }, 1000);\n        } catch (error) {\n            console.error('❌ Error loading session:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to load session due to an unexpected error\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteSession = (sessionId)=>{\n        console.log('🗑️ Deleting session:', sessionId);\n        if (!sessionId || sessionId === 'undefined' || sessionId === 'null') {\n            console.error('❌ Invalid session ID for deletion:', sessionId);\n            toast({\n                title: \"Error\",\n                description: \"Invalid session ID - cannot delete session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Check if trying to delete the current active session\n        if (currentSessionId === sessionId && botSystemStatus === 'Running') {\n            toast({\n                title: \"Error\",\n                description: \"Cannot delete the currently running session. Stop the bot first.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            const success = sessionManager.deleteSession(sessionId);\n            console.log('🗑️ Session deletion result:', success ? 'Success' : 'Failed');\n            if (success) {\n                if (currentSessionId === sessionId) {\n                    setCurrentSessionId(null);\n                }\n                loadSessions();\n                toast({\n                    title: \"Session Deleted\",\n                    description: \"Session has been deleted successfully\"\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to delete session - session may not exist\",\n                    variant: \"destructive\"\n                });\n                // Refresh sessions list to remove stale entries\n                loadSessions();\n            }\n        } catch (error) {\n            console.error('❌ Error deleting session:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete session due to an unexpected error\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleRenameSession = (sessionId)=>{\n        if (!editingName.trim()) return;\n        const success = sessionManager.renameSession(sessionId, editingName.trim());\n        if (success) {\n            setEditingSessionId(null);\n            setEditingName('');\n            loadSessions();\n            toast({\n                title: \"Session Renamed\",\n                description: \"Session has been renamed successfully\"\n            });\n        }\n    };\n    const handleExportSession = (sessionId)=>{\n        const csvContent = sessionManager.exportSessionToCSV(sessionId);\n        if (!csvContent) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to export session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const session = sessionManager.loadSession(sessionId);\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat((session === null || session === void 0 ? void 0 : session.name) || 'session', \"_\").concat(new Date().toISOString().split('T')[0], \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        toast({\n            title: \"Export Complete\",\n            description: \"Session data has been exported to CSV\"\n        });\n    };\n    const handleOpenAlarmModal = (sessionId, sessionName)=>{\n        setSelectedAlarmSession({\n            id: sessionId,\n            name: sessionName\n        });\n        setAlarmModalOpen(true);\n    };\n    const handleCloseAlarmModal = ()=>{\n        setAlarmModalOpen(false);\n        setSelectedAlarmSession(null);\n    };\n    const formatRuntime = (runtime)=>{\n        if (!runtime || runtime < 0) return '0s';\n        const totalSeconds = Math.floor(runtime / 1000);\n        const hours = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        if (hours > 0) {\n            return \"\".concat(hours, \"h \").concat(minutes, \"m \").concat(seconds, \"s\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"m \").concat(seconds, \"s\");\n        } else {\n            return \"\".concat(seconds, \"s\");\n        }\n    };\n    const getCurrentSession = ()=>{\n        return sessions.find((s)=>s.id === currentSessionId);\n    };\n    const getActiveSessions = ()=>{\n        // Only show sessions that are actually running (bot is active and it's the current session)\n        if (!currentSessionId || botSystemStatus !== 'Running') {\n            console.log('🔍 No active sessions - currentSessionId:', currentSessionId, 'botSystemStatus:', botSystemStatus);\n            return [];\n        }\n        const activeSessions = sessions.filter((s)=>{\n            const isCurrentAndActive = s.id === currentSessionId && s.isActive;\n            console.log(\"\\uD83D\\uDD0D Session \".concat(s.name, \": isActive=\").concat(s.isActive, \", isCurrent=\").concat(s.id === currentSessionId, \", result=\").concat(isCurrentAndActive));\n            return isCurrentAndActive;\n        });\n        console.log('🔍 Active sessions found:', activeSessions.length);\n        return activeSessions;\n    };\n    const getPastSessions = ()=>{\n        // Show all sessions that are NOT currently active/running\n        const pastSessions = sessions.filter((s)=>{\n            const isCurrentlyActive = s.id === currentSessionId && s.isActive && botSystemStatus === 'Running';\n            return !isCurrentlyActive;\n        });\n        console.log('🔍 Past sessions found:', pastSessions.length);\n        return pastSessions;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this),\n                                \"Active Sessions\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: getActiveSessions().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 476,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Active Status\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Runtime\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: getActiveSessions().map((session, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: editingName,\n                                                                onChange: (e)=>setEditingName(e.target.value),\n                                                                onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                className: \"text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleRenameSession(session.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 496,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: session.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"ghost\",\n                                                                onClick: ()=>{\n                                                                    setEditingSessionId(session.id);\n                                                                    setEditingName(session.name);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 510,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"default\",\n                                                        children: \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: session.id === currentSessionId ? formatRuntime(currentRuntime) : formatRuntime(session.runtime)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: session.id === currentSessionId ? // Current session - show Save button (session is active/loaded)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: handleSaveCurrentSession,\n                                                                size: \"sm\",\n                                                                className: \"btn-neo\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"mr-2 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 531,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Save\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 530,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenAlarmModal(session.id, session.name),\n                                                                title: \"Configure Alarms\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 537,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 25\n                                                    }, this) : // Other sessions - show Load button (session is saved)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 550,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 549,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 552,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenAlarmModal(session.id, session.name),\n                                                                title: \"Configure Alarms\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 561,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 555,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, \"active-\".concat(session.id, \"-\").concat(index), true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No active session\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs\",\n                                    children: \"Start trading to create a session automatically\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 574,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 571,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 464,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Past Sessions (\",\n                                    getPastSessions().length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 585,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: [\n                                    \"Auto-saved: \",\n                                    getPastSessions().length,\n                                    \" | Manual: 0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 584,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                            className: \"h-[400px]\",\n                            children: getPastSessions().length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-muted-foreground py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No saved sessions yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 598,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: \"Save your current session to get started.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 596,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Session Name\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Active Status\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Total Runtime\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 608,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 604,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: getPastSessions().map((session, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    value: editingName,\n                                                                    onChange: (e)=>setEditingName(e.target.value),\n                                                                    onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                    className: \"text-sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 618,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleRenameSession(session.id),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 625,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 624,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: session.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 630,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>{\n                                                                        setEditingSessionId(session.id);\n                                                                        setEditingName(session.name);\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 639,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 631,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 615,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            children: \"Inactive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 646,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 645,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: formatRuntime(sessionManager.getCurrentRuntime(session.id))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 651,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 657,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 656,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 660,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 659,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenAlarmModal(session.id, session.name),\n                                                                title: \"Configure Alarms\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 668,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 662,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteSession(session.id),\n                                                                title: \"Delete Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 671,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 670,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 655,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, \"inactive-\".concat(session.id, \"-\").concat(index), true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 614,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 602,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 593,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 583,\n                columnNumber: 7\n            }, this),\n            selectedAlarmSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_SessionAlarmModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: alarmModalOpen,\n                onClose: handleCloseAlarmModal,\n                sessionId: selectedAlarmSession.id,\n                sessionName: selectedAlarmSession.name\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 685,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n        lineNumber: 462,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"lTtemk6bI4J/UVkffUm/7GBSG0Q=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SessionManager.tsx\n"));

/***/ })

});