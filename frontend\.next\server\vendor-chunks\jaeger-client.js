"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jaeger-client";
exports.ids = ["vendor-chunks/jaeger-client"];
exports.modules = {

/***/ "(action-browser)/./node_modules/jaeger-client/dist/src/logger.js":
/*!*******************************************************!*\
  !*** ./node_modules/jaeger-client/dist/src/logger.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nvar NullLogger = function () {\n  function NullLogger() {\n    _classCallCheck(this, NullLogger);\n  }\n\n  _createClass(NullLogger, [{\n    key: \"info\",\n    value: function info(msg) {}\n  }, {\n    key: \"error\",\n    value: function error(msg) {}\n  }]);\n\n  return NullLogger;\n}();\n\nexports[\"default\"] = NullLogger;\n//# sourceMappingURL=logger.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/jaeger-client/dist/src/logger.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/jaeger-client/dist/src/reporters/http_sender.js":
/*!**********************************************************************!*\
  !*** ./node_modules/jaeger-client/dist/src/reporters/http_sender.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n// Copyright (c) 2018 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nvar _fs = __webpack_require__(/*! fs */ \"fs\");\n\nvar _fs2 = _interopRequireDefault(_fs);\n\nvar _http = __webpack_require__(/*! http */ \"http\");\n\nvar _http2 = _interopRequireDefault(_http);\n\nvar _https = __webpack_require__(/*! https */ \"https\");\n\nvar _https2 = _interopRequireDefault(_https);\n\nvar _path = __webpack_require__(/*! path */ \"path\");\n\nvar _path2 = _interopRequireDefault(_path);\n\nvar _url = __webpack_require__(/*! url */ \"url\");\n\nvar URL = _interopRequireWildcard(_url);\n\nvar _thriftrw = __webpack_require__(/*! thriftrw */ \"(action-browser)/./node_modules/thriftrw/index.js\");\n\nvar _logger = __webpack_require__(/*! ../logger.js */ \"(action-browser)/./node_modules/jaeger-client/dist/src/logger.js\");\n\nvar _logger2 = _interopRequireDefault(_logger);\n\nvar _sender_utils = __webpack_require__(/*! ./sender_utils.js */ \"(action-browser)/./node_modules/jaeger-client/dist/src/reporters/sender_utils.js\");\n\nvar _sender_utils2 = _interopRequireDefault(_sender_utils);\n\nvar _thrift = __webpack_require__(/*! ../thrift.js */ \"(action-browser)/./node_modules/jaeger-client/dist/src/thrift.js\");\n\nvar _thrift2 = _interopRequireDefault(_thrift);\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) newObj[key] = obj[key]; } } newObj.default = obj; return newObj; } }\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar DEFAULT_PATH = '/api/traces';\nvar DEFAULT_PORT = 14268;\nvar DEFAULT_TIMEOUT_MS = 5000;\nvar DEFAULT_MAX_SPAN_BATCH_SIZE = 100;\n\nvar HTTPSender = function () {\n  function HTTPSender() {\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n    _classCallCheck(this, HTTPSender);\n\n    this._url = URL.parse(options.endpoint);\n    this._username = options.username;\n    this._password = options.password;\n    this._timeoutMs = options.timeoutMs || options.timeoutMS || DEFAULT_TIMEOUT_MS;\n    this._httpAgent = this._url.protocol === 'https:' ? new _https2.default.Agent({ keepAlive: true }) : new _http2.default.Agent({ keepAlive: true });\n\n    this._maxSpanBatchSize = options.maxSpanBatchSize || DEFAULT_MAX_SPAN_BATCH_SIZE;\n\n    this._logger = options.logger || new _logger2.default();\n    this._jaegerThrift = new _thriftrw.Thrift({\n      source: _thrift2.default.loadJaegerThriftDefinition(),\n      allowOptionalArguments: true\n    });\n\n    this._httpOptions = {\n      protocol: this._url.protocol,\n      hostname: this._url.hostname,\n      port: this._url.port,\n      path: this._url.pathname,\n      method: 'POST',\n      auth: this._username && this._password ? this._username + ':' + this._password : undefined,\n      headers: {\n        'Content-Type': 'application/x-thrift',\n        Connection: 'keep-alive'\n      },\n      agent: this._httpAgent,\n      timeout: this._timeoutMs\n    };\n  }\n\n  _createClass(HTTPSender, [{\n    key: 'setProcess',\n    value: function setProcess(process) {\n      // Go ahead and initialize the Thrift batch that we will reuse for each\n      // flush.\n      this._batch = new this._jaegerThrift.Batch({\n        process: _sender_utils2.default.convertProcessToThrift(this._jaegerThrift, process),\n        spans: []\n      });\n    }\n  }, {\n    key: 'append',\n    value: function append(span, callback) {\n      this._batch.spans.push(new this._jaegerThrift.Span(span));\n\n      if (this._batch.spans.length >= this._maxSpanBatchSize) {\n        this.flush(callback);\n        return;\n      }\n      _sender_utils2.default.invokeCallback(callback, 0);\n    }\n  }, {\n    key: 'flush',\n    value: function flush(callback) {\n      var _this = this;\n\n      var numSpans = this._batch.spans.length;\n      if (!numSpans) {\n        _sender_utils2.default.invokeCallback(callback, 0);\n        return;\n      }\n\n      var result = this._jaegerThrift.Batch.rw.toBuffer(this._batch);\n      this._reset(); // clear buffer for new spans, even if Thrift conversion fails\n\n      if (result.err) {\n        _sender_utils2.default.invokeCallback(callback, numSpans, 'Error encoding Thrift batch: ' + result.err);\n        return;\n      }\n\n      var requester = this._url.protocol === 'https:' ? _https2.default.request : _http2.default.request;\n\n      var req = requester(this._httpOptions, function (resp) {\n        // consume response data to free up memory\n        resp.resume();\n\n        var error = void 0;\n        if (resp.statusCode >= 400) {\n          error = 'error sending spans over HTTP: server responded with HTTP ' + resp.statusCode;\n          _this._logger.error(error);\n        }\n\n        _sender_utils2.default.invokeCallback(callback, numSpans, error);\n      });\n\n      req.on('error', function (err) {\n        var error = 'error sending spans over HTTP: ' + err;\n        _this._logger.error(error);\n        _sender_utils2.default.invokeCallback(callback, numSpans, error);\n      });\n      req.write(result.value);\n      req.end();\n    }\n  }, {\n    key: '_reset',\n    value: function _reset() {\n      this._batch.spans = [];\n    }\n  }, {\n    key: 'close',\n    value: function close() {\n      // Older node versions don't have this.\n      if (this._httpAgent.destroy) {\n        this._httpAgent.destroy();\n      }\n    }\n  }]);\n\n  return HTTPSender;\n}();\n\nexports[\"default\"] = HTTPSender;\n//# sourceMappingURL=http_sender.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/jaeger-client/dist/src/reporters/http_sender.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/jaeger-client/dist/src/reporters/sender_utils.js":
/*!***********************************************************************!*\
  !*** ./node_modules/jaeger-client/dist/src/reporters/sender_utils.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n// Copyright (c) 2018 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nvar _thriftrw = __webpack_require__(/*! thriftrw */ \"(action-browser)/./node_modules/thriftrw/index.js\");\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar SenderUtils = function () {\n  function SenderUtils() {\n    _classCallCheck(this, SenderUtils);\n  }\n\n  _createClass(SenderUtils, null, [{\n    key: 'invokeCallback',\n    value: function invokeCallback(callback, numSpans, error) {\n      if (callback) {\n        callback(numSpans, error);\n      }\n    }\n  }, {\n    key: 'convertProcessToThrift',\n    value: function convertProcessToThrift(t, process) {\n      var tagMessages = [];\n      for (var j = 0; j < process.tags.length; j++) {\n        var tag = process.tags[j];\n        tagMessages.push(new t.Tag(tag));\n      }\n\n      return new t.Process({\n        serviceName: process.serviceName,\n        tags: tagMessages\n      });\n    }\n  }]);\n\n  return SenderUtils;\n}();\n\nexports[\"default\"] = SenderUtils;\n//# sourceMappingURL=sender_utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/jaeger-client/dist/src/reporters/sender_utils.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/jaeger-client/dist/src/reporters/udp_sender.js":
/*!*********************************************************************!*\
  !*** ./node_modules/jaeger-client/dist/src/reporters/udp_sender.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nvar _dgram = __webpack_require__(/*! dgram */ \"dgram\");\n\nvar _dgram2 = _interopRequireDefault(_dgram);\n\nvar _fs = __webpack_require__(/*! fs */ \"fs\");\n\nvar _fs2 = _interopRequireDefault(_fs);\n\nvar _path = __webpack_require__(/*! path */ \"path\");\n\nvar _path2 = _interopRequireDefault(_path);\n\nvar _thriftrw = __webpack_require__(/*! thriftrw */ \"(action-browser)/./node_modules/thriftrw/index.js\");\n\nvar _logger = __webpack_require__(/*! ../logger */ \"(action-browser)/./node_modules/jaeger-client/dist/src/logger.js\");\n\nvar _logger2 = _interopRequireDefault(_logger);\n\nvar _sender_utils = __webpack_require__(/*! ./sender_utils */ \"(action-browser)/./node_modules/jaeger-client/dist/src/reporters/sender_utils.js\");\n\nvar _sender_utils2 = _interopRequireDefault(_sender_utils);\n\nvar _thrift = __webpack_require__(/*! ../thrift */ \"(action-browser)/./node_modules/jaeger-client/dist/src/thrift.js\");\n\nvar _thrift2 = _interopRequireDefault(_thrift);\n\nvar _util = __webpack_require__(/*! ../util */ \"(action-browser)/./node_modules/jaeger-client/dist/src/util.js\");\n\nvar _util2 = _interopRequireDefault(_util);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar HOST = 'localhost';\nvar PORT = 6832;\nvar SOCKET_TYPE = 'udp4';\nvar UDP_PACKET_MAX_LENGTH = 65000;\n\nvar UDPSender = function () {\n  // size of currently batched spans as Thrift bytes\n\n  function UDPSender() {\n    var _this = this;\n\n    var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n\n    _classCallCheck(this, UDPSender);\n\n    this._host = options.host || HOST;\n    this._port = options.port || PORT;\n    this._socketType = options.socketType || SOCKET_TYPE;\n    this._maxPacketSize = options.maxPacketSize || UDP_PACKET_MAX_LENGTH;\n    this._logger = options.logger || new _logger2.default();\n    this._client = _dgram2.default.createSocket(this._socketType);\n    this._client.on('error', function (err) {\n      _this._logger.error('error sending spans over UDP: ' + err);\n    });\n    this._agentThrift = new _thriftrw.Thrift({\n      entryPoint: _thrift2.default.buildAgentThriftPath(),\n      allowOptionalArguments: true,\n      allowFilesystemAccess: true\n    });\n    this._jaegerThrift = new _thriftrw.Thrift({\n      source: _thrift2.default.loadJaegerThriftDefinition(),\n      allowOptionalArguments: true\n    });\n    this._totalSpanBytes = 0;\n  } // maxPacketSize - (batch + tags overhead)\n\n\n  _createClass(UDPSender, [{\n    key: '_calcBatchSize',\n    value: function _calcBatchSize(batch) {\n      return this._agentThrift.Agent.emitBatch.argumentsMessageRW.byteLength(this._convertBatchToThriftMessage()).length;\n    }\n  }, {\n    key: '_calcSpanSize',\n    value: function _calcSpanSize(span) {\n      return this._jaegerThrift.Span.rw.byteLength(new this._jaegerThrift.Span(span));\n    }\n  }, {\n    key: 'setProcess',\n    value: function setProcess(process) {\n      // This function is only called once during reporter construction, and thus will\n      // give us the length of the batch before any spans have been added to the span\n      // list in batch.\n      this._process = process;\n      this._batch = {\n        process: this._process,\n        spans: []\n      };\n\n      this._thriftProcessMessage = _sender_utils2.default.convertProcessToThrift(this._jaegerThrift, process);\n      this._emitSpanBatchOverhead = this._calcBatchSize(this._batch);\n      this._maxSpanBytes = this._maxPacketSize - this._emitSpanBatchOverhead;\n    }\n  }, {\n    key: 'append',\n    value: function append(span, callback) {\n      var _this2 = this;\n\n      var _calcSpanSize2 = this._calcSpanSize(span),\n          err = _calcSpanSize2.err,\n          length = _calcSpanSize2.length;\n\n      if (err) {\n        _sender_utils2.default.invokeCallback(callback, 1, 'error converting span to Thrift: ' + err);\n        return;\n      }\n      var spanSize = length;\n      if (spanSize > this._maxSpanBytes) {\n        _sender_utils2.default.invokeCallback(callback, 1, 'span size ' + spanSize + ' is larger than maxSpanSize ' + this._maxSpanBytes);\n        return;\n      }\n\n      if (this._totalSpanBytes + spanSize <= this._maxSpanBytes) {\n        this._batch.spans.push(span);\n        this._totalSpanBytes += spanSize;\n        if (this._totalSpanBytes < this._maxSpanBytes) {\n          // still have space in the buffer, don't flush it yet\n          _sender_utils2.default.invokeCallback(callback, 0);\n          return;\n        }\n        // buffer size === this._maxSpanBytes\n        this.flush(callback);\n        return;\n      }\n\n      this.flush(function (numSpans, err) {\n        // TODO theoretically we can have buffer overflow here too, if many spans were appended during flush()\n        _this2._batch.spans.push(span);\n        _this2._totalSpanBytes += spanSize;\n        _sender_utils2.default.invokeCallback(callback, numSpans, err);\n      });\n    }\n  }, {\n    key: 'flush',\n    value: function flush(callback) {\n      var numSpans = this._batch.spans.length;\n      if (!numSpans) {\n        _sender_utils2.default.invokeCallback(callback, 0);\n        return;\n      }\n\n      var bufferLen = this._totalSpanBytes + this._emitSpanBatchOverhead;\n      var thriftBuffer = _util2.default.newBuffer(bufferLen);\n      var writeResult = this._agentThrift.Agent.emitBatch.argumentsMessageRW.writeInto(this._convertBatchToThriftMessage(), thriftBuffer, 0);\n      this._reset();\n\n      if (writeResult.err) {\n        _sender_utils2.default.invokeCallback(callback, numSpans, 'error writing Thrift object: ' + writeResult.err);\n        return;\n      }\n\n      // Having the error callback here does not prevent uncaught exception from being thrown,\n      // that's why in the constructor we also add a general on('error') handler.\n      this._client.send(thriftBuffer, 0, thriftBuffer.length, this._port, this._host, function (err, sent) {\n        if (err) {\n          var error = err && 'error sending spans over UDP: ' + err + ', packet size: ' + writeResult.offset + ', bytes sent: ' + sent;\n          _sender_utils2.default.invokeCallback(callback, numSpans, error);\n        } else {\n          _sender_utils2.default.invokeCallback(callback, numSpans);\n        }\n      });\n    }\n  }, {\n    key: '_convertBatchToThriftMessage',\n    value: function _convertBatchToThriftMessage() {\n      var spanMessages = [];\n      for (var i = 0; i < this._batch.spans.length; i++) {\n        var span = this._batch.spans[i];\n        spanMessages.push(new this._jaegerThrift.Span(span));\n      }\n\n      return new this._agentThrift.Agent.emitBatch.ArgumentsMessage({\n        version: 1,\n        id: 0,\n        body: {\n          batch: new this._jaegerThrift.Batch({\n            process: this._thriftProcessMessage,\n            spans: spanMessages\n          })\n        }\n      });\n    }\n  }, {\n    key: '_reset',\n    value: function _reset() {\n      this._batch.spans = [];\n      this._totalSpanBytes = 0;\n    }\n  }, {\n    key: 'close',\n    value: function close() {\n      this._client.close();\n    }\n  }]);\n\n  return UDPSender;\n}();\n\nexports[\"default\"] = UDPSender;\n//# sourceMappingURL=udp_sender.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/jaeger-client/dist/src/reporters/udp_sender.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/jaeger-client/dist/src/thrift.js":
/*!*******************************************************!*\
  !*** ./node_modules/jaeger-client/dist/src/thrift.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nvar _fs = __webpack_require__(/*! fs */ \"fs\");\n\nvar _fs2 = _interopRequireDefault(_fs);\n\nvar _opentracing = __webpack_require__(/*! opentracing */ \"(action-browser)/./node_modules/opentracing/lib/index.js\");\n\nvar opentracing = _interopRequireWildcard(_opentracing);\n\nvar _path = __webpack_require__(/*! path */ \"path\");\n\nvar _path2 = _interopRequireDefault(_path);\n\nvar _thriftrw = __webpack_require__(/*! thriftrw */ \"(action-browser)/./node_modules/thriftrw/index.js\");\n\nvar _util = __webpack_require__(/*! ./util.js */ \"(action-browser)/./node_modules/jaeger-client/dist/src/util.js\");\n\nvar _util2 = _interopRequireDefault(_util);\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) newObj[key] = obj[key]; } } newObj.default = obj; return newObj; } }\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar ThriftUtils = function () {\n  function ThriftUtils() {\n    _classCallCheck(this, ThriftUtils);\n  }\n\n  _createClass(ThriftUtils, null, [{\n    key: 'loadJaegerThriftDefinition',\n    value: function loadJaegerThriftDefinition() {\n      return _fs2.default.readFileSync(_path2.default.join(__dirname, './jaeger-idl/thrift/jaeger.thrift'), 'ascii');\n    }\n  }, {\n    key: 'buildAgentThriftPath',\n    value: function buildAgentThriftPath() {\n      return _path2.default.join(__dirname, './thriftrw-idl/agent.thrift');\n    }\n  }, {\n    key: 'getThriftTags',\n    value: function getThriftTags(initialTags) {\n      var thriftTags = [];\n      for (var i = 0; i < initialTags.length; i++) {\n        var tag = initialTags[i];\n\n        var key = tag.key;\n\n        var vLong = ThriftUtils.emptyBuffer;\n        var vBinary = ThriftUtils.emptyBuffer;\n        var vBool = false;\n        var vDouble = 0;\n        var vStr = '';\n\n        var vType = '';\n        var valueType = _typeof(tag.value);\n        if (valueType === 'number') {\n          vType = ThriftUtils._thrift.TagType.DOUBLE;\n          vDouble = tag.value;\n        } else if (valueType === 'boolean') {\n          vType = ThriftUtils._thrift.TagType.BOOL;\n          vBool = tag.value;\n        } else if (tag.value instanceof Buffer) {\n          vType = ThriftUtils._thrift.TagType.BINARY;\n          vBinary = tag.value;\n        } else if (valueType === 'object') {\n          vType = ThriftUtils._thrift.TagType.STRING;\n          vStr = JSON.stringify(tag.value);\n        } else {\n          vType = ThriftUtils._thrift.TagType.STRING;\n          if (valueType === 'string') {\n            vStr = tag.value;\n          } else {\n            vStr = String(tag.value);\n          }\n        }\n\n        thriftTags.push({\n          key: key,\n          vType: vType,\n          vStr: vStr,\n          vDouble: vDouble,\n          vBool: vBool,\n          vLong: vLong,\n          vBinary: vBinary\n        });\n      }\n\n      return thriftTags;\n    }\n  }, {\n    key: 'getThriftLogs',\n    value: function getThriftLogs(logs) {\n      var thriftLogs = [];\n      for (var i = 0; i < logs.length; i++) {\n        var log = logs[i];\n        thriftLogs.push({\n          timestamp: _util2.default.encodeInt64(log.timestamp * 1000), // to microseconds\n          fields: ThriftUtils.getThriftTags(log.fields)\n        });\n      }\n\n      return thriftLogs;\n    }\n  }, {\n    key: 'spanRefsToThriftRefs',\n    value: function spanRefsToThriftRefs(refs) {\n      var thriftRefs = [];\n      for (var i = 0; i < refs.length; i++) {\n        var refEnum = void 0;\n        var ref = refs[i];\n        var context = refs[i].referencedContext();\n\n        if (ref.type() === opentracing.REFERENCE_CHILD_OF) {\n          refEnum = ThriftUtils._thrift.SpanRefType.CHILD_OF;\n        } else if (ref.type() === opentracing.REFERENCE_FOLLOWS_FROM) {\n          refEnum = ThriftUtils._thrift.SpanRefType.FOLLOWS_FROM;\n        } else {\n          continue;\n        }\n\n        thriftRefs.push({\n          refType: refEnum,\n          traceIdLow: ThriftUtils.getTraceIdLow(context.traceId),\n          traceIdHigh: ThriftUtils.getTraceIdHigh(context.traceId),\n          spanId: context.spanId\n        });\n      }\n\n      return thriftRefs;\n    }\n  }, {\n    key: 'getTraceIdLow',\n    value: function getTraceIdLow(traceId) {\n      if (traceId != null) {\n        return traceId.slice(-8);\n      }\n\n      return ThriftUtils.emptyBuffer;\n    }\n  }, {\n    key: 'getTraceIdHigh',\n    value: function getTraceIdHigh(traceId) {\n      if (traceId != null && traceId.length > 8) {\n        return traceId.slice(-16, -8);\n      }\n\n      return ThriftUtils.emptyBuffer;\n    }\n  }, {\n    key: 'spanToThrift',\n    value: function spanToThrift(span) {\n      var tags = ThriftUtils.getThriftTags(span._tags);\n      var logs = ThriftUtils.getThriftLogs(span._logs);\n      var unsigned = true;\n\n      return {\n        traceIdLow: ThriftUtils.getTraceIdLow(span._spanContext.traceId),\n        traceIdHigh: ThriftUtils.getTraceIdHigh(span._spanContext.traceId),\n        spanId: span._spanContext.spanId,\n        parentSpanId: span._spanContext.parentId || ThriftUtils.emptyBuffer,\n        operationName: span._operationName,\n        references: ThriftUtils.spanRefsToThriftRefs(span._references),\n        flags: span._spanContext.flags,\n        startTime: _util2.default.encodeInt64(span._startTime * 1000), // to microseconds\n        duration: _util2.default.encodeInt64(span._duration * 1000), // to microseconds\n        tags: tags,\n        logs: logs\n      };\n    }\n  }]);\n\n  return ThriftUtils;\n}();\n\nThriftUtils._thrift = new _thriftrw.Thrift({\n  source: ThriftUtils.loadJaegerThriftDefinition(),\n  allowOptionalArguments: true\n});\nThriftUtils.emptyBuffer = _util2.default.newBuffer(8);\nexports[\"default\"] = ThriftUtils;\n//# sourceMappingURL=thrift.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/jaeger-client/dist/src/thrift.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/jaeger-client/dist/src/util.js":
/*!*****************************************************!*\
  !*** ./node_modules/jaeger-client/dist/src/util.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n// Copyright (c) 2016 Uber Technologies, Inc.\n//\n// Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this file except\n// in compliance with the License. You may obtain a copy of the License at\n//\n// http://www.apache.org/licenses/LICENSE-2.0\n//\n// Unless required by applicable law or agreed to in writing, software distributed under the License\n// is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express\n// or implied. See the License for the specific language governing permissions and limitations under\n// the License.\n\nvar _xorshift = __webpack_require__(/*! xorshift */ \"(action-browser)/./node_modules/xorshift/xorshift.js\");\n\nvar _xorshift2 = _interopRequireDefault(_xorshift);\n\nvar _nodeInt = __webpack_require__(/*! node-int64 */ \"(action-browser)/./node_modules/node-int64/Int64.js\");\n\nvar _nodeInt2 = _interopRequireDefault(_nodeInt);\n\nvar _os = __webpack_require__(/*! os */ \"os\");\n\nvar _os2 = _interopRequireDefault(_os);\n\nvar _http = __webpack_require__(/*! http */ \"http\");\n\nvar _http2 = _interopRequireDefault(_http);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nvar Utils = function () {\n  function Utils() {\n    _classCallCheck(this, Utils);\n  }\n\n  _createClass(Utils, null, [{\n    key: 'startsWith',\n\n    /**\n     * Determines whether a string contains a given prefix.\n     *\n     * @param {string} text - the string for to search for a prefix\n     * @param {string} prefix - the prefix to search for in the text given.\n     * @return {boolean} - boolean representing whether or not the\n     * string contains the prefix.\n     **/\n    value: function startsWith(text, prefix) {\n      return text.indexOf(prefix) === 0;\n    }\n\n    /**\n     * Determines whether a string contains a given suffix.\n     *\n     * @param {string} text - the string for to search for a suffix\n     * @param {string} suffix - the suffix to search for in the text given.\n     * @return {boolean} - boolean representing whether or not the\n     * string contains the suffix.\n     **/\n\n  }, {\n    key: 'endsWith',\n    value: function endsWith(text, suffix) {\n      return text.lastIndexOf(suffix) === text.length - suffix.length;\n    }\n\n    /**\n     * Get a random buffer representing a random 64 bit.\n     *\n     * @return {Buffer}  - returns a buffer representing a random 64 bit\n     * number.\n     **/\n\n  }, {\n    key: 'getRandom64',\n    value: function getRandom64() {\n      var randint = _xorshift2.default.randomint();\n      var buf = this.newBuffer(8);\n      buf.writeUInt32BE(randint[0], 0);\n      buf.writeUInt32BE(randint[1], 4);\n      return buf;\n    }\n\n    /**\n     * Get a random buffer representing a random 128 bit.\n     *\n     * @return {Buffer}  - returns a buffer representing a random 128 bit\n     * number.\n     **/\n\n  }, {\n    key: 'getRandom128',\n    value: function getRandom128() {\n      var randint1 = _xorshift2.default.randomint();\n      var randint2 = _xorshift2.default.randomint();\n      var buf = this.newBuffer(16);\n      buf.writeUInt32BE(randint1[0], 0);\n      buf.writeUInt32BE(randint1[1], 4);\n      buf.writeUInt32BE(randint2[0], 8);\n      buf.writeUInt32BE(randint2[1], 12);\n      return buf;\n    }\n\n    /**\n     * @param {string|number} numberValue - a string or number to be encoded\n     * as a 64 bit byte array.\n     * @return {Buffer} - returns a buffer representing the encoded string, or number.\n     **/\n\n  }, {\n    key: 'encodeInt64',\n    value: function encodeInt64(numberValue) {\n      return new _nodeInt2.default(numberValue).toBuffer();\n    }\n\n    /**\n     * @param {string} input - the input for which leading zeros should be removed.\n     * @return {string} - returns the input string without leading zeros.\n     **/\n\n  }, {\n    key: 'removeLeadingZeros',\n    value: function removeLeadingZeros(input) {\n      var counter = 0;\n      var length = input.length - 1;\n      for (var i = 0; i < length; i++) {\n        if (input.charAt(i) === '0') {\n          counter++;\n        } else {\n          break;\n        }\n      }\n\n      return input.substring(counter);\n    }\n  }, {\n    key: 'myIp',\n    value: function myIp() {\n      var myIp = '0.0.0.0';\n      var ifaces = _os2.default.networkInterfaces();\n      var keys = Object.keys(ifaces);\n      loop1: for (var i = 0; i < keys.length; i++) {\n        var iface = ifaces[keys[i]];\n        for (var j = 0; j < iface.length; j++) {\n          if (iface[j].family === 'IPv4' && !iface[j].internal) {\n            myIp = iface[j].address;\n            break loop1;\n          }\n        }\n      }\n      return myIp;\n    }\n  }, {\n    key: 'clone',\n    value: function clone(obj) {\n      var newObj = {};\n      for (var key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n          newObj[key] = obj[key];\n        }\n      }\n\n      return newObj;\n    }\n  }, {\n    key: 'convertObjectToTags',\n    value: function convertObjectToTags(dict) {\n      var tags = [];\n      for (var key in dict) {\n        var value = dict[key];\n        if (Object.prototype.hasOwnProperty.call(dict, key)) {\n          tags.push({ key: key, value: value });\n        }\n      }\n\n      return tags;\n    }\n  }, {\n    key: 'httpGet',\n    value: function httpGet(host, port, path, success, error) {\n      _http2.default.get({\n        host: host,\n        port: port,\n        path: path\n      }, function (res) {\n        // explicitly treat incoming data as utf8 (avoids issues with multi-byte chars)\n        res.setEncoding('utf8');\n\n        // incrementally capture the incoming response body\n        var body = '';\n        res.on('data', function (chunk) {\n          body += chunk;\n        });\n\n        res.on('end', function () {\n          success(body);\n        });\n      }).on('error', function (err) {\n        error(err);\n      });\n    }\n\n    /**\n     * @param {string|number} input - a hex encoded string to store in the buffer.\n     * @return {Buffer} - returns a buffer representing the hex encoded string.\n     **/\n\n  }, {\n    key: 'newBufferFromHex',\n    value: function newBufferFromHex(input) {\n      var encoding = 'hex';\n      // check that 'Buffer.from' exists based on node's documentation\n      // https://nodejs.org/en/docs/guides/buffer-constructor-deprecation/#variant-3\n      if (Buffer.from && Buffer.from !== Uint8Array.from) {\n        return Buffer.from(input, encoding);\n      }\n      return new Buffer(input, encoding);\n    }\n\n    /**\n     * @param {number} input - a number of octets to allocate.\n     * @return {Buffer} - returns an empty buffer.\n     **/\n\n  }, {\n    key: 'newBuffer',\n    value: function newBuffer(size) {\n      if (Buffer.alloc) {\n        return Buffer.alloc(size);\n      }\n      var buffer = new Buffer(size);\n      buffer.fill(0);\n      return buffer;\n    }\n\n    /**\n     * Creates a callback function that only delegates to passed <code>callback</code>\n     * after <code>limit</code> invocations. Useful in types like CompositeReporter that\n     * needs to invoke the top level callback only after all delegates' close() methods\n     * are called.\n     */\n\n  }, {\n    key: 'countdownCallback',\n    value: function countdownCallback(limit, callback) {\n      var count = 0;\n      return function () {\n        count++;\n        if (count >= limit && callback) {\n          callback();\n        }\n      };\n    }\n  }]);\n\n  return Utils;\n}();\n\nexports[\"default\"] = Utils;\n//# sourceMappingURL=util.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/jaeger-client/dist/src/util.js\n");

/***/ })

};
;