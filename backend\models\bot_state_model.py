import sys
import os
# Add parent directory to path if needed
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db import db
from datetime import datetime
from sqlalchemy.orm import relationship
import json

class BotState(db.Model):
    """Model to track the state of a trading bot instance."""
    __tablename__ = 'bot_states'
    __table_args__ = {'extend_existing': True}

    id = db.Column(db.Integer, primary_key=True)
    config_id = db.Column(db.Integer, db.ForeignKey('trading_configurations.id'), nullable=False)
    user_id = db.Column(db.Integer, db.<PERSON>Key('users.id'), nullable=False)
    is_running = db.Column(db.<PERSON>, default=False)
    last_check = db.Column(db.DateTime, nullable=True)
    current_market_price = db.Column(db.Float, nullable=True)
    target_states = db.Column(db.Text, nullable=True)  # JSON array of target price states

    # Removed cooldown tracking - immediate execution per blueprint

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    trading_config = relationship("TradingConfiguration", back_populates="bot_states")
    user = relationship("User")

    def __init__(self, config_id, user_id):
        self.config_id = config_id
        self.user_id = user_id
        self.is_running = False
        self.target_states = json.dumps([])

    def get_target_states(self):
        """Convert stored JSON string to Python list."""
        if not self.target_states:
            return []
        try:
            return json.loads(self.target_states)
        except json.JSONDecodeError:
            return []

    def set_target_states(self, states):
        """Convert Python list to JSON string for storage."""
        self.target_states = json.dumps(states)

    def update_target_state(self, target_id, updates):
        """Update a specific target state by ID."""
        states = self.get_target_states()
        for i, state in enumerate(states):
            if state.get('id') == target_id:
                states[i] = {**state, **updates}
                break
        self.set_target_states(states)

    # Removed cooldown tracking methods - immediate execution per blueprint

    def to_dict(self):
        """Convert model to dictionary for API responses."""
        return {
            'id': self.id,
            'config_id': self.config_id,
            'user_id': self.user_id,
            'is_running': self.is_running,
            'last_check': self.last_check.isoformat() if self.last_check else None,
            'current_market_price': self.current_market_price,
            'target_states': self.get_target_states(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }