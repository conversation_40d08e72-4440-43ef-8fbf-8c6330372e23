"use client";

import React, { useState } from 'react';
import { useTradingContext } from '@/contexts/TradingContext';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Coins, CircleDollarSign, Bitcoin, Edit3, Check, X } from 'lucide-react';

export default function BalancesDisplay() {
  const { crypto1Balance, crypto2Balance, stablecoinBalance, config, dispatch } = useTradingContext();
  const [editingBalance, setEditingBalance] = useState<'crypto1' | 'crypto2' | 'stablecoin' | null>(null);
  const [tempValues, setTempValues] = useState({
    crypto1: crypto1Balance.toString(),
    crypto2: crypto2Balance.toString(),
    stablecoin: stablecoinBalance.toString()
  });

  const formatBalance = (balance: number) => balance.toFixed(config.numDigits);

  const handleEdit = (balanceType: 'crypto1' | 'crypto2' | 'stablecoin') => {
    setEditingBalance(balanceType);
    setTempValues({
      crypto1: crypto1Balance.toString(),
      crypto2: crypto2Balance.toString(),
      stablecoin: stablecoinBalance.toString()
    });
  };

  const handleSave = (balanceType: 'crypto1' | 'crypto2' | 'stablecoin') => {
    const newValue = parseFloat(tempValues[balanceType]);
    if (!isNaN(newValue) && newValue >= 0) {
      if (balanceType === 'crypto1') {
        dispatch({ type: 'UPDATE_BALANCES', payload: { crypto1: newValue, crypto2: crypto2Balance } });
      } else if (balanceType === 'crypto2') {
        dispatch({ type: 'UPDATE_BALANCES', payload: { crypto1: crypto1Balance, crypto2: newValue } });
      } else if (balanceType === 'stablecoin') {
        dispatch({ type: 'UPDATE_STABLECOIN_BALANCE', payload: newValue });
      }
    }
    setEditingBalance(null);
  };

  const handleCancel = () => {
    setEditingBalance(null);
    setTempValues({
      crypto1: crypto1Balance.toString(),
      crypto2: crypto2Balance.toString(),
      stablecoin: stablecoinBalance.toString()
    });
  };

  const renderBalanceCard = (
    title: string,
    balance: number,
    balanceType: 'crypto1' | 'crypto2' | 'stablecoin',
    icon: React.ReactNode,
    currency: string
  ) => (
    <Card className="border-2 border-border">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        {editingBalance === balanceType ? (
          <div className="space-y-2">
            <Input
              type="number"
              value={tempValues[balanceType]}
              onChange={(e) => setTempValues(prev => ({ ...prev, [balanceType]: e.target.value }))}
              className="text-lg font-bold"
              step="any"
              min="0"
            />
            <div className="flex gap-2">
              <Button size="sm" onClick={() => handleSave(balanceType)} className="flex-1">
                <Check className="h-4 w-4 mr-1" />
                Save
              </Button>
              <Button size="sm" variant="outline" onClick={handleCancel} className="flex-1">
                <X className="h-4 w-4 mr-1" />
                Cancel
              </Button>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-between">
            <div>
              <div className="text-2xl font-bold text-foreground">{formatBalance(balance)}</div>
              <p className="text-xs text-muted-foreground">Available {currency}</p>
            </div>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => handleEdit(balanceType)}
              className="ml-2"
            >
              <Edit3 className="h-4 w-4" />
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="grid gap-4 md:grid-cols-3 mb-6">
      {renderBalanceCard(
        `${config.crypto1 || "Crypto 1"} Balance`,
        crypto1Balance,
        'crypto1',
        <Bitcoin className="h-5 w-5 text-primary" />,
        config.crypto1 || "Crypto 1"
      )}
      {renderBalanceCard(
        `${config.crypto2 || "Crypto 2"} Balance`,
        crypto2Balance,
        'crypto2',
        <CircleDollarSign className="h-5 w-5 text-primary" />,
        config.crypto2 || "Crypto 2"
      )}
      {renderBalanceCard(
        `Stablecoin Balance (${config.preferredStablecoin || 'N/A'})`,
        stablecoinBalance,
        'stablecoin',
        <Coins className="h-5 w-5 text-primary" />,
        'Stablecoins'
      )}
    </div>
  );
}
