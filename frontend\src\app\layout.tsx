import type { Metada<PERSON> } from 'next';
import { GeistSans } from 'geist/font/sans'; // Correct import for Geist Sans
import './globals.css';
import { Toaster } from "@/components/ui/toaster";
import { AuthProvider } from '@/contexts/AuthContext';
import { TradingProvider } from '@/contexts/TradingContext';
import { AIProvider } from '@/contexts/AIContext';

// GeistSans from 'geist/font/sans' directly provides .variable and .className
// No need to call it as a function like with next/font/google.
// The variable it sets is typically --font-geist-sans.

export const metadata: Metadata = {
  title: 'Pluto Trading Bot',
  description: 'Simulated cryptocurrency trading bot with Neo Brutalist UI.',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={GeistSans.variable} suppressHydrationWarning>
      <body className="font-sans antialiased" suppressHydrationWarning> {/* Tailwind's font-sans will pick up the CSS variable */}
        <AuthProvider>
          <TradingProvider>
            <AIProvider>
              {children}
              <Toaster />
            </AIProvider>
          </TradingProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
