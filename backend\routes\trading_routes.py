from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from db import db
from models.trading_config_model import TradingConfiguration
from models.bot_state_model import BotState
from models.trade_model import Trade
from models.bot_status_model import BotStatusLog
from trading.trading_engine import TradingEngine
from services.exchange_service import ExchangeService
import logging
import uuid
from datetime import datetime

logger = logging.getLogger(__name__)
trading_bp = Blueprint('trading', __name__, url_prefix='/trading')

@trading_bp.route('/config', methods=['GET'])
@jwt_required()
def get_trading_configs():
    """Get all trading configurations for the current user."""
    current_user_id = get_jwt_identity()

    configs = TradingConfiguration.query.filter_by(user_id=current_user_id).all()

    return jsonify({
        "configs": [config.to_dict() for config in configs]
    }), 200


@trading_bp.route('/config/<int:config_id>', methods=['GET'])
@jwt_required()
def get_trading_config(config_id):
    """Get a specific trading configuration."""
    current_user_id = get_jwt_identity()

    config = TradingConfiguration.query.filter_by(id=config_id, user_id=current_user_id).first()

    if not config:
        return jsonify({"error": "Trading configuration not found"}), 404

    return jsonify({
        "config": config.to_dict()
    }), 200


@trading_bp.route('/config', methods=['POST'])
@jwt_required()
def create_trading_config():
    """Create a new trading configuration."""
    if not request.is_json:
        return jsonify({"error": "Missing JSON in request"}), 400

    current_user_id = get_jwt_identity()
    data = request.get_json()

    required_fields = ['name', 'tradingMode', 'crypto1', 'crypto2', 'baseBid',
                        'multiplier', 'numDigits', 'slippagePercent',
                        'incomeSplitCrypto1Percent', 'incomeSplitCrypto2Percent']

    # Check for required fields
    for field in required_fields:
        if field not in data:
            return jsonify({"error": f"Missing required field: {field}"}), 400

    # Create new configuration
    new_config = TradingConfiguration(
        user_id=current_user_id,
        name=data['name'],
        trading_mode=data['tradingMode'],
        crypto1=data['crypto1'],
        crypto2=data['crypto2'],
        base_bid=data['baseBid'],
        multiplier=data['multiplier'],
        num_digits=data['numDigits'],
        slippage_percent=data['slippagePercent'],
        income_split_crypto1_percent=data['incomeSplitCrypto1Percent'],
        income_split_crypto2_percent=data['incomeSplitCrypto2Percent'],
        preferred_stablecoin=data.get('preferredStablecoin')
    )

    # Set target prices if provided
    if 'targetPrices' in data:
        new_config.set_target_prices(data['targetPrices'])

    try:
        db.session.add(new_config)
        db.session.commit()

        return jsonify({
            "message": "Trading configuration created successfully",
            "config": new_config.to_dict()
        }), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error creating trading configuration: {str(e)}")
        return jsonify({"error": "Failed to create trading configuration"}), 500


@trading_bp.route('/config/<int:config_id>', methods=['PUT'])
@jwt_required()
def update_trading_config(config_id):
    """Update an existing trading configuration."""
    if not request.is_json:
        return jsonify({"error": "Missing JSON in request"}), 400

    current_user_id = get_jwt_identity()
    data = request.get_json()

    # Find the config
    config = TradingConfiguration.query.filter_by(id=config_id, user_id=current_user_id).first()

    if not config:
        return jsonify({"error": "Trading configuration not found"}), 404

    # Update fields if provided
    if 'name' in data:
        config.name = data['name']
    if 'tradingMode' in data:
        config.trading_mode = data['tradingMode']
    if 'crypto1' in data:
        config.crypto1 = data['crypto1']
    if 'crypto2' in data:
        config.crypto2 = data['crypto2']
    if 'baseBid' in data:
        config.base_bid = data['baseBid']
    if 'multiplier' in data:
        config.multiplier = data['multiplier']
    if 'numDigits' in data:
        config.num_digits = data['numDigits']
    if 'slippagePercent' in data:
        config.slippage_percent = data['slippagePercent']
    if 'incomeSplitCrypto1Percent' in data:
        config.income_split_crypto1_percent = data['incomeSplitCrypto1Percent']
    if 'incomeSplitCrypto2Percent' in data:
        config.income_split_crypto2_percent = data['incomeSplitCrypto2Percent']
    if 'preferredStablecoin' in data:
        config.preferred_stablecoin = data['preferredStablecoin']
    if 'targetPrices' in data:
        config.set_target_prices(data['targetPrices'])

    try:
        db.session.commit()

        # If config is updated while bot is active, reset the bot state
        bot_state = BotState.query.filter_by(config_id=config_id).first()
        if bot_state and bot_state.is_running:
            # Reset target states based on new target prices
            target_prices = config.get_target_prices()
            if target_prices:
                target_states = []
                sorted_prices = sorted(target_prices)
                for index, price in enumerate(sorted_prices):
                    target_states.append({
                        'id': str(uuid.uuid4()),
                        'counter': index + 1,  # Counter starts from 1, lowest price gets counter 1
                        'targetPrice': price,
                        'status': 'Free',
                        'orderLevel': 0,
                        'valueLevel': config.base_bid,  # Use base_bid from config
                        'crypto1AmountHeld': None,
                        'originalCostCrypto2': None
                    })
                bot_state.set_target_states(target_states)
                db.session.commit()

        return jsonify({
            "message": "Trading configuration updated successfully",
            "config": config.to_dict()
        }), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating trading configuration: {str(e)}")
        return jsonify({"error": "Failed to update trading configuration"}), 500


@trading_bp.route('/config/<int:config_id>', methods=['DELETE'])
@jwt_required()
def delete_trading_config(config_id):
    """Delete a trading configuration."""
    current_user_id = get_jwt_identity()

    # Find the config
    config = TradingConfiguration.query.filter_by(id=config_id, user_id=current_user_id).first()

    if not config:
        return jsonify({"error": "Trading configuration not found"}), 404

    # Check if bot is running
    bot_state = BotState.query.filter_by(config_id=config_id).first()
    if bot_state and bot_state.is_running:
        return jsonify({"error": "Cannot delete configuration while bot is running. Stop the bot first."}), 400

    try:
        # Delete bot state if exists
        if bot_state:
            db.session.delete(bot_state)

        # Delete config
        db.session.delete(config)
        db.session.commit()

        return jsonify({
            "message": "Trading configuration deleted successfully"
        }), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting trading configuration: {str(e)}")
        return jsonify({"error": "Failed to delete trading configuration"}), 500


@trading_bp.route('/bot/start/<int:config_id>', methods=['POST'])
@jwt_required()
def start_bot(config_id):
    """Start a trading bot for a specific configuration."""
    current_user_id = get_jwt_identity()

    try:
        result = TradingEngine.start_bot(config_id, current_user_id)
        return jsonify(result), 200
    except Exception as e:
        logger.error(f"Error starting bot: {str(e)}")
        return jsonify({
            "error": "Failed to start bot",
            "message": str(e)
        }), 500


@trading_bp.route('/bot/stop/<int:config_id>', methods=['POST'])
@jwt_required()
def stop_bot(config_id):
    """Stop a trading bot for a specific configuration."""
    current_user_id = get_jwt_identity()

    try:
        result = TradingEngine.stop_bot(config_id, current_user_id)
        return jsonify(result), 200
    except Exception as e:
        logger.error(f"Error stopping bot: {str(e)}")
        return jsonify({
            "error": "Failed to stop bot",
            "message": str(e)
        }), 500


@trading_bp.route('/bot/status/<int:config_id>', methods=['GET'])
@jwt_required()
def get_bot_status(config_id):
    """Get the current status of a trading bot."""
    current_user_id = get_jwt_identity()

    # Find the bot state
    bot_state = BotState.query.filter_by(config_id=config_id).first()

    if not bot_state:
        return jsonify({
            "isRunning": False,
            "currentMarketPrice": None,
            "lastCheck": None,
            "targetStates": []
        }), 200

    return jsonify({
        "isRunning": bot_state.is_running,
        "currentMarketPrice": bot_state.current_market_price,
        "lastCheck": bot_state.last_check.isoformat() if bot_state.last_check else None,
        "targetStates": bot_state.get_target_states()
    }), 200


@trading_bp.route('/bot/logs/<int:config_id>', methods=['GET'])
@jwt_required()
def get_bot_logs(config_id):
    """Get logs for a specific bot configuration."""
    current_user_id = get_jwt_identity()

    # Verify the config exists and belongs to the user
    config = TradingConfiguration.query.filter_by(id=config_id, user_id=current_user_id).first()
    if not config:
        return jsonify({"error": "Trading configuration not found"}), 404

    # Get logs
    logs = BotStatusLog.query.filter_by(config_id=config_id).order_by(BotStatusLog.timestamp.desc()).limit(100).all()

    return jsonify({
        "logs": [log.to_dict() for log in logs]
    }), 200


@trading_bp.route('/history', methods=['GET'])
@jwt_required()
def get_trade_history():
    """Get trade history for the current user."""
    current_user_id = get_jwt_identity()

    # Optional filtering by config_id
    config_id = request.args.get('configId', type=int)
    limit = request.args.get('limit', 100, type=int)

    query = Trade.query.filter_by(user_id=current_user_id)

    if config_id:
        query = query.filter_by(config_id=config_id)

    trades = query.order_by(Trade.timestamp.desc()).limit(limit).all()

    return jsonify({
        "trades": [trade.to_dict() for trade in trades]
    }), 200


@trading_bp.route('/market-data/<string:pair>', methods=['GET'])
@jwt_required()
def get_market_data(pair):
    """Get current market data for a trading pair."""
    try:
        # Format the pair symbol properly (e.g., convert BTC-USDT to BTC/USDT)
        if '-' in pair:
            pair = pair.replace('-', '/')

        # Get current price
        price = ExchangeService.get_ticker_price(pair)

        return jsonify({
            "pair": pair,
            "price": price,
            "timestamp": datetime.utcnow().isoformat()
        }), 200
    except Exception as e:
        logger.error(f"Error getting market data: {str(e)}")
        return jsonify({
            "error": "Failed to get market data",
            "message": str(e)
        }), 500


@trading_bp.route('/balances', methods=['GET'])
@jwt_required()
def get_balances():
    """Get current balances from the exchange."""
    current_user_id = get_jwt_identity()

    try:
        # Get the user's API key
        api_key = TradingEngine.get_api_key_for_user(current_user_id)

        # Get balances
        balances = ExchangeService.get_account_balance(api_key)

        return jsonify({
            "balances": balances
        }), 200
    except Exception as e:
        logger.error(f"Error getting balances: {str(e)}")
        return jsonify({
            "error": "Failed to get balances",
            "message": str(e)
        }), 500


@trading_bp.route('/exchange/trading-pairs', methods=['GET'])
def get_trading_pairs():
    """Get all available trading pairs from the exchange."""
    try:
        exchange_name = request.args.get('exchange', 'binance')
        trading_pairs = ExchangeService.get_available_trading_pairs(exchange_name)

        return jsonify({
            "tradingPairs": trading_pairs,
            "exchange": exchange_name,
            "timestamp": datetime.utcnow().isoformat()
        }), 200
    except Exception as e:
        logger.error(f"Error getting trading pairs: {str(e)}")
        return jsonify({
            "error": "Failed to get trading pairs",
            "message": str(e)
        }), 500


@trading_bp.route('/exchange/cryptocurrencies', methods=['GET'])
def get_cryptocurrencies():
    """Get all available cryptocurrencies from the exchange."""
    try:
        exchange_name = request.args.get('exchange', 'binance')
        cryptocurrencies = ExchangeService.get_available_cryptocurrencies(exchange_name)

        return jsonify({
            "cryptocurrencies": cryptocurrencies,
            "exchange": exchange_name,
            "count": len(cryptocurrencies),
            "timestamp": datetime.utcnow().isoformat()
        }), 200
    except Exception as e:
        logger.error(f"Error getting cryptocurrencies: {str(e)}")
        return jsonify({
            "error": "Failed to get cryptocurrencies",
            "message": str(e)
        }), 500