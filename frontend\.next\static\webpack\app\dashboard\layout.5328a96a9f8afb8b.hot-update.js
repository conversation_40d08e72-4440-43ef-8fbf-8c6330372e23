"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackend() {\n        // Always load from localStorage first for immediate access\n        this.loadSessionsFromStorage();\n        console.log('📂 Loaded sessions from localStorage for immediate access');\n        try {\n            // Try to establish backend connection\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 3000); // 3 second timeout\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.status < 500) {\n                this.useBackend = true;\n                this.backendInitialized = true;\n                console.log('✅ Session Manager: Backend connection established');\n                // Sync with backend - merge backend sessions with localStorage\n                await this.syncWithBackend();\n            } else {\n                throw new Error('Backend returned server error');\n            }\n        } catch (error) {\n            console.warn('⚠️ Session Manager: Backend unavailable, using localStorage only');\n            this.useBackend = false;\n            this.backendInitialized = false;\n        }\n        // Setup storage listener for cross-window sync\n        this.setupStorageListener();\n    }\n    async syncWithBackend() {\n        if (!this.useBackend || !this.backendInitialized) return;\n        try {\n            const token = localStorage.getItem('auth_token');\n            if (!token) {\n                console.warn('No auth token found, cannot sync with backend');\n                return;\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.getUserSessions();\n            const backendSessions = response.sessions;\n            // Create a map of backend sessions by ID\n            const backendSessionMap = new Map();\n            for (const backendSession of backendSessions){\n                backendSessionMap.set(backendSession.session_uuid, backendSession);\n            }\n            // Merge localStorage sessions with backend sessions\n            const mergedSessions = new Map(this.sessions);\n            // Add/update sessions from backend\n            for (const backendSession of backendSessions){\n                const session = {\n                    id: backendSession.session_uuid,\n                    name: backendSession.name,\n                    config: backendSession.config_snapshot,\n                    targetPriceRows: backendSession.target_price_rows || [],\n                    orderHistory: [],\n                    currentMarketPrice: backendSession.current_market_price || 0,\n                    crypto1Balance: backendSession.crypto1_balance || 10,\n                    crypto2Balance: backendSession.crypto2_balance || 100000,\n                    stablecoinBalance: backendSession.stablecoin_balance || 0,\n                    createdAt: new Date(backendSession.created_at).getTime(),\n                    lastModified: new Date(backendSession.last_modified).getTime(),\n                    isActive: backendSession.is_active || false,\n                    runtime: backendSession.runtime_seconds * 1000 || 0 // Convert to milliseconds\n                };\n                mergedSessions.set(session.id, session);\n                // Set current session if this one is active\n                if (session.isActive) {\n                    this.currentSessionId = session.id;\n                    const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                    localStorage.setItem(currentSessionKey, session.id);\n                }\n            }\n            // Upload localStorage-only sessions to backend\n            for (const [sessionId, localSession] of this.sessions){\n                if (!backendSessionMap.has(sessionId)) {\n                    console.log(\"\\uD83D\\uDCE4 Uploading localStorage session to backend: \".concat(localSession.name));\n                    try {\n                        await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession({\n                            name: localSession.name,\n                            config: localSession.config,\n                            targetPriceRows: localSession.targetPriceRows,\n                            currentMarketPrice: localSession.currentMarketPrice,\n                            crypto1Balance: localSession.crypto1Balance,\n                            crypto2Balance: localSession.crypto2Balance,\n                            stablecoinBalance: localSession.stablecoinBalance\n                        });\n                    } catch (error) {\n                        console.error(\"Failed to upload session \".concat(sessionId, \" to backend:\"), error);\n                    }\n                }\n            }\n            // Update local sessions map\n            this.sessions = mergedSessions;\n            console.log(\"\\uD83D\\uDD04 Synced \".concat(this.sessions.size, \" sessions between localStorage and backend\"));\n            // Save merged sessions back to localStorage\n            this.saveSessionsToStorage();\n        } catch (error) {\n            console.error('Failed to sync with backend:', error);\n        }\n    }\n    // loadSessionsFromBackend method replaced by syncWithBackend\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Load current session from window-specific storage (each window has its own current session)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            const currentSessionId = localStorage.getItem(currentSessionKey);\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config);\n    }\n    async createNewSession(name, config) {\n        if (this.useBackend && this.backendInitialized) {\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession({\n                    name,\n                    config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: 10,\n                    crypto2Balance: 100000,\n                    stablecoinBalance: 0\n                });\n                const sessionId = response.session.session_uuid;\n                // Add to local cache\n                const now = Date.now();\n                const newSession = {\n                    id: sessionId,\n                    name,\n                    config,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: 10,\n                    crypto2Balance: 100000,\n                    stablecoinBalance: 0,\n                    createdAt: now,\n                    lastModified: now,\n                    isActive: false,\n                    runtime: 0\n                };\n                this.sessions.set(sessionId, newSession);\n                this.saveSessionsToStorage(); // Backup to localStorage\n                console.log('✅ Session created on backend:', sessionId);\n                return sessionId;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n            // Don't disable backend completely, just fallback for this operation\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: 10,\n            crypto2Balance: 100000,\n            stablecoinBalance: 0,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        console.log('📝 Session created in localStorage:', sessionId);\n        return sessionId;\n    }\n    saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = session.runtime + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = session.runtime + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            // ALWAYS save to localStorage first for immediate persistence\n            this.saveSessionsToStorage();\n            console.log(\"\\uD83D\\uDCBE Session \".concat(sessionId, \" saved to localStorage\"));\n            // Also save to backend if available for permanent cloud storage\n            if (this.useBackend && this.backendInitialized) {\n                this.saveSessionToBackend(sessionId, updatedSession).catch((error)=>{\n                    console.error('Failed to save session to backend:', error);\n                // Don't fail the operation if backend save fails - localStorage is primary\n                });\n            } else {\n                console.log(\"\\uD83D\\uDCDD Session \".concat(sessionId, \" saved to localStorage only (backend unavailable)\"));\n            }\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    async saveSessionToBackend(sessionId, session) {\n        if (!this.useBackend || !this.backendInitialized) return;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, {\n                name: session.name,\n                config: session.config,\n                targetPriceRows: session.targetPriceRows,\n                currentMarketPrice: session.currentMarketPrice,\n                crypto1Balance: session.crypto1Balance,\n                crypto2Balance: session.crypto2Balance,\n                stablecoinBalance: session.stablecoinBalance,\n                isActive: session.isActive,\n                additionalRuntime: Math.floor(session.runtime / 1000) // Convert to seconds\n            });\n            console.log(\"\\uD83D\\uDCBE Session \".concat(sessionId, \" saved to backend\"));\n        } catch (error) {\n            console.error(\"Failed to save session \".concat(sessionId, \" to backend:\"), error);\n            throw error;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    deleteSession(sessionId) {\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            // Mark this session as active when it becomes the current session\n            const session = this.sessions.get(sessionId);\n            if (session && !session.isActive) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Mark current session as inactive before clearing\n        if (this.currentSessionId) {\n            const session = this.sessions.get(this.currentSessionId);\n            if (session && session.isActive) {\n                session.isActive = false;\n                session.lastModified = Date.now();\n                this.sessions.set(this.currentSessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive for window \").concat(this.windowId));\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime += additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return session.runtime + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime;\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.backendInitialized = false;\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Initialize backend connection first, then load sessions\n        this.initializeBackend();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});