"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    // Session integrity check and repair function\n    validateAndRepairSessions() {\n        console.log('🔧 Running session integrity check...');\n        let repairCount = 0;\n        for (const [sessionId, session] of this.sessions){\n            let needsRepair = false;\n            // Check if session has valid ID\n            if (!session.id || session.id !== sessionId) {\n                console.warn(\"\\uD83D\\uDD27 Repairing session ID mismatch: \".concat(sessionId, \" vs \").concat(session.id));\n                session.id = sessionId;\n                needsRepair = true;\n            }\n            // Check if session has required properties\n            if (!session.name) {\n                console.warn(\"\\uD83D\\uDD27 Repairing missing session name for \".concat(sessionId));\n                session.name = \"Recovered Session \".concat(new Date().toLocaleString());\n                needsRepair = true;\n            }\n            if (!session.config) {\n                console.warn(\"\\uD83D\\uDD27 Repairing missing config for session \".concat(sessionId));\n                session.config = {\n                    crypto1: 'BTC',\n                    crypto2: 'USDT',\n                    stablecoin: 'USDT',\n                    initialCrypto1Balance: 10,\n                    initialCrypto2Balance: 100000,\n                    initialStablecoinBalance: 0\n                };\n                needsRepair = true;\n            }\n            if (!Array.isArray(session.targetPriceRows)) {\n                console.warn(\"\\uD83D\\uDD27 Repairing missing targetPriceRows for session \".concat(sessionId));\n                session.targetPriceRows = [];\n                needsRepair = true;\n            }\n            if (!Array.isArray(session.orderHistory)) {\n                console.warn(\"\\uD83D\\uDD27 Repairing missing orderHistory for session \".concat(sessionId));\n                session.orderHistory = [];\n                needsRepair = true;\n            }\n            if (typeof session.createdAt !== 'number') {\n                console.warn(\"\\uD83D\\uDD27 Repairing missing createdAt for session \".concat(sessionId));\n                session.createdAt = Date.now();\n                needsRepair = true;\n            }\n            if (typeof session.lastModified !== 'number') {\n                console.warn(\"\\uD83D\\uDD27 Repairing missing lastModified for session \".concat(sessionId));\n                session.lastModified = Date.now();\n                needsRepair = true;\n            }\n            if (typeof session.isActive !== 'boolean') {\n                console.warn(\"\\uD83D\\uDD27 Repairing missing isActive for session \".concat(sessionId));\n                session.isActive = false;\n                needsRepair = true;\n            }\n            if (needsRepair) {\n                repairCount++;\n            }\n        }\n        if (repairCount > 0) {\n            console.log(\"\\uD83D\\uDD27 Repaired \".concat(repairCount, \" sessions, saving to storage...\"));\n            this.saveSessionsToStorage();\n        } else {\n            console.log('✅ All sessions passed integrity check');\n        }\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackend() {\n        // Always load from localStorage first for immediate access\n        this.loadSessionsFromStorage();\n        console.log('📂 Loaded sessions from localStorage for immediate access');\n        try {\n            // Try to establish backend connection\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 3000); // 3 second timeout\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.status < 500) {\n                this.useBackend = true;\n                this.backendInitialized = true;\n                console.log('✅ Session Manager: Backend connection established');\n                // Sync with backend - merge backend sessions with localStorage\n                await this.syncWithBackend();\n            } else {\n                throw new Error('Backend returned server error');\n            }\n        } catch (error) {\n            console.warn('⚠️ Session Manager: Backend unavailable, using localStorage only');\n            this.useBackend = false;\n            this.backendInitialized = false;\n        }\n        // Setup storage listener for cross-window sync\n        this.setupStorageListener();\n    }\n    async syncWithBackend() {\n        if (!this.useBackend || !this.backendInitialized) return;\n        try {\n            const token = localStorage.getItem('auth_token');\n            if (!token) {\n                console.warn('No auth token found, cannot sync with backend');\n                return;\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.getUserSessions();\n            const backendSessions = response.sessions;\n            // Create a map of backend sessions by ID\n            const backendSessionMap = new Map();\n            for (const backendSession of backendSessions){\n                backendSessionMap.set(backendSession.session_uuid, backendSession);\n            }\n            // Merge localStorage sessions with backend sessions\n            const mergedSessions = new Map(this.sessions);\n            // Add/update sessions from backend\n            for (const backendSession of backendSessions){\n                const session = {\n                    id: backendSession.session_uuid,\n                    name: backendSession.name,\n                    config: backendSession.config_snapshot,\n                    targetPriceRows: backendSession.target_price_rows || [],\n                    orderHistory: [],\n                    currentMarketPrice: backendSession.current_market_price || 0,\n                    crypto1Balance: backendSession.crypto1_balance || 10,\n                    crypto2Balance: backendSession.crypto2_balance || 100000,\n                    stablecoinBalance: backendSession.stablecoin_balance || 0,\n                    createdAt: new Date(backendSession.created_at).getTime(),\n                    lastModified: new Date(backendSession.last_modified).getTime(),\n                    isActive: backendSession.is_active || false,\n                    runtime: backendSession.runtime_seconds * 1000 || 0 // Convert to milliseconds\n                };\n                mergedSessions.set(session.id, session);\n                // Set current session if this one is active\n                if (session.isActive) {\n                    this.currentSessionId = session.id;\n                    const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                    localStorage.setItem(currentSessionKey, session.id);\n                }\n            }\n            // Upload localStorage-only sessions to backend\n            for (const [sessionId, localSession] of this.sessions){\n                if (!backendSessionMap.has(sessionId)) {\n                    console.log(\"\\uD83D\\uDCE4 Uploading localStorage session to backend: \".concat(localSession.name));\n                    try {\n                        await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession({\n                            name: localSession.name,\n                            config: localSession.config,\n                            targetPriceRows: localSession.targetPriceRows,\n                            currentMarketPrice: localSession.currentMarketPrice,\n                            crypto1Balance: localSession.crypto1Balance,\n                            crypto2Balance: localSession.crypto2Balance,\n                            stablecoinBalance: localSession.stablecoinBalance\n                        });\n                    } catch (error) {\n                        console.error(\"Failed to upload session \".concat(sessionId, \" to backend:\"), error);\n                    }\n                }\n            }\n            // Update local sessions map\n            this.sessions = mergedSessions;\n            console.log(\"\\uD83D\\uDD04 Synced \".concat(this.sessions.size, \" sessions between localStorage and backend\"));\n            // Save merged sessions back to localStorage\n            this.saveSessionsToStorage();\n        } catch (error) {\n            console.error('Failed to sync with backend:', error);\n        }\n    }\n    // loadSessionsFromBackend method replaced by syncWithBackend\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Load current session from window-specific storage (each window has its own current session)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            const currentSessionId = localStorage.getItem(currentSessionKey);\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n                // Validate all loaded sessions have proper IDs\n                for (const [sessionId, session] of this.sessions){\n                    if (!session.id || session.id !== sessionId) {\n                        console.error('❌ Session ID mismatch detected:', {\n                            sessionId,\n                            sessionObjectId: session.id\n                        });\n                        // Fix the session ID\n                        session.id = sessionId;\n                    }\n                }\n            }\n            this.currentSessionId = currentSessionId;\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId));\n            // Run integrity check and repair if needed\n            this.validateAndRepairSessions();\n            // Debug: Log all session IDs\n            console.log('📂 Session IDs loaded:', Array.from(this.sessions.keys()));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n            this.sessions = new Map();\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config);\n    }\n    async createNewSession(name, config) {\n        if (this.useBackend && this.backendInitialized) {\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession({\n                    name,\n                    config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: 10,\n                    crypto2Balance: 100000,\n                    stablecoinBalance: 0\n                });\n                const sessionId = response.session.session_uuid;\n                // Add to local cache\n                const now = Date.now();\n                const newSession = {\n                    id: sessionId,\n                    name,\n                    config,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: 10,\n                    crypto2Balance: 100000,\n                    stablecoinBalance: 0,\n                    createdAt: now,\n                    lastModified: now,\n                    isActive: false,\n                    runtime: 0\n                };\n                this.sessions.set(sessionId, newSession);\n                this.saveSessionsToStorage(); // Always save to localStorage for immediate access\n                console.log('✅ Session created on backend and localStorage:', sessionId);\n                return sessionId;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n            // Don't disable backend completely, just fallback for this operation\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: 10,\n            crypto2Balance: 100000,\n            stablecoinBalance: 0,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        console.log('📝 Session created in localStorage (permanent storage):', sessionId);\n        // Try to sync to backend in background if available\n        if (this.useBackend && this.backendInitialized) {\n            setTimeout(async ()=>{\n                try {\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession({\n                        name,\n                        config,\n                        targetPriceRows: [],\n                        currentMarketPrice: 0,\n                        crypto1Balance: 10,\n                        crypto2Balance: 100000,\n                        stablecoinBalance: 0\n                    });\n                    console.log('📤 Session also uploaded to backend:', sessionId);\n                } catch (error) {\n                    console.warn('Failed to upload session to backend (localStorage still has it):', error);\n                }\n            }, 1000);\n        }\n        return sessionId;\n    }\n    saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = session.runtime + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = session.runtime + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            // ALWAYS save to localStorage first for immediate persistence\n            this.saveSessionsToStorage();\n            console.log(\"\\uD83D\\uDCBE Session \".concat(sessionId, \" saved to localStorage\"));\n            // Also save to backend if available for permanent cloud storage\n            if (this.useBackend && this.backendInitialized) {\n                this.saveSessionToBackend(sessionId, updatedSession).catch((error)=>{\n                    console.error('Failed to save session to backend:', error);\n                // Don't fail the operation if backend save fails - localStorage is primary\n                });\n            } else {\n                console.log(\"\\uD83D\\uDCDD Session \".concat(sessionId, \" saved to localStorage only (backend unavailable)\"));\n            }\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    async deleteSessionFromBackend(sessionId) {\n        if (!this.useBackend || !this.backendInitialized) return;\n        // Validate session ID before sending to backend\n        if (!sessionId || sessionId === 'undefined' || sessionId === 'null') {\n            console.error('❌ Attempted to delete invalid session ID from backend:', sessionId);\n            return;\n        }\n        try {\n            const response = await fetch(\"http://localhost:5000/sessions/\".concat(sessionId), {\n                method: 'DELETE',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            console.log(\"✅ Session \".concat(sessionId, \" deleted from backend\"));\n        } catch (error) {\n            console.error(\"Failed to delete session \".concat(sessionId, \" from backend:\"), error);\n            throw error;\n        }\n    }\n    async saveSessionToBackend(sessionId, session) {\n        if (!this.useBackend || !this.backendInitialized) return;\n        // Validate session ID before sending to backend\n        if (!sessionId || sessionId === 'undefined' || sessionId === 'null') {\n            console.error('❌ Attempted to save invalid session ID to backend:', sessionId);\n            return;\n        }\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, {\n                name: session.name,\n                config: session.config,\n                targetPriceRows: session.targetPriceRows,\n                currentMarketPrice: session.currentMarketPrice,\n                crypto1Balance: session.crypto1Balance,\n                crypto2Balance: session.crypto2Balance,\n                stablecoinBalance: session.stablecoinBalance,\n                isActive: session.isActive,\n                additionalRuntime: Math.floor(session.runtime / 1000) // Convert to seconds\n            });\n            console.log(\"\\uD83D\\uDCBE Session \".concat(sessionId, \" saved to backend\"));\n        } catch (error) {\n            console.error(\"Failed to save session \".concat(sessionId, \" to backend:\"), error);\n            throw error;\n        }\n    }\n    loadSession(sessionId) {\n        console.log('📂 SessionManager: Loading session:', sessionId);\n        console.log('📂 Available sessions:', Array.from(this.sessions.keys()));\n        const session = this.sessions.get(sessionId) || null;\n        console.log('📂 Load result:', session ? 'Found' : 'Not found');\n        return session;\n    }\n    deleteSession(sessionId) {\n        console.log('🗑️ SessionManager: Attempting to delete session:', sessionId);\n        console.log('🗑️ Sessions before deletion:', this.sessions.size);\n        const deleted = this.sessions.delete(sessionId);\n        console.log('🗑️ Local deletion result:', deleted);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                console.log('🗑️ Clearing current session ID');\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n            console.log('🗑️ Sessions after deletion:', this.sessions.size);\n            // Also try to delete from backend\n            this.deleteSessionFromBackend(sessionId).catch((error)=>{\n                console.warn('⚠️ Failed to delete session from backend:', error);\n            });\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).filter((session)=>{\n            // Filter out sessions with invalid IDs\n            if (!session.id || session.id === 'undefined' || session.id === 'null') {\n                console.error('❌ Filtering out session with invalid ID:', session);\n                return false;\n            }\n            return true;\n        }).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        // Validate session ID before setting\n        if (!sessionId || sessionId === 'undefined' || sessionId === 'null') {\n            console.error('❌ Attempted to set invalid session ID:', sessionId);\n            return;\n        }\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            // Mark this session as active when it becomes the current session\n            const session = this.sessions.get(sessionId);\n            if (session && !session.isActive) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        // Validate current session ID\n        if (this.currentSessionId === 'undefined' || this.currentSessionId === 'null') {\n            console.warn('⚠️ Invalid currentSessionId detected:', this.currentSessionId, '- clearing it');\n            this.currentSessionId = null;\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            return null;\n        }\n        // Check if session actually exists\n        if (this.currentSessionId && !this.sessions.has(this.currentSessionId)) {\n            console.warn('⚠️ Current session ID not found in sessions:', this.currentSessionId, '- clearing it');\n            this.currentSessionId = null;\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            return null;\n        }\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Mark current session as inactive before clearing\n        if (this.currentSessionId) {\n            const session = this.sessions.get(this.currentSessionId);\n            if (session && session.isActive) {\n                session.isActive = false;\n                session.lastModified = Date.now();\n                this.sessions.set(this.currentSessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive for window \").concat(this.windowId));\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime += additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return session.runtime + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime;\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.backendInitialized = false;\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Initialize backend connection first, then load sessions\n        this.initializeBackend();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});