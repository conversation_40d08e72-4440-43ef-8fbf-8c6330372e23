from flask_sqlalchemy import SQLAlchemy
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Initialize SQLAlchemy
db = SQLAlchemy()

# Move model imports to a function to break circular dependencies
def register_models():
    # Import models to ensure they are registered with SQLAlchemy
    # These imports are intentionally inside this function to avoid circular imports
    from models.user_model import User
    from models.api_key_model import ExchangeApiKey
    from models.trading_config_model import TradingConfiguration
    from models.trade_model import Trade
    from models.bot_status_model import BotStatusLog
    from models.bot_state_model import BotState
    
    # Return imported models for convenience
    return {
        'User': User,
        'ExchangeApiKey': ExchangeApiKey,
        'TradingConfiguration': TradingConfiguration,
        'Trade': Trade,
        'BotStatusLog': BotStatusLog,
        'BotState': BotState
    } 