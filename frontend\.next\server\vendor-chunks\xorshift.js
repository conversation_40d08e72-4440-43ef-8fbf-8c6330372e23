/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xorshift";
exports.ids = ["vendor-chunks/xorshift"];
exports.modules = {

/***/ "(action-browser)/./node_modules/xorshift/xorshift.js":
/*!*******************************************!*\
  !*** ./node_modules/xorshift/xorshift.js ***!
  \*******************************************/
/***/ ((module) => {

eval("\n/**\n * Create a pseudorandom number generator, with a seed.\n * @param {array} seed \"128-bit\" integer, composed of 4x32-bit\n * integers in big endian order.\n */\nfunction XorShift(seed) {\n  // Note the extension, this === module.exports is required because\n  // the `constructor` function will be used to generate new instances.\n  // In that case `this` will point to the default RNG, and `this` will\n  // be an instance of XorShift.\n  if (!(this instanceof XorShift) || this === module.exports) {\n    return new XorShift(seed);\n  }\n\n  if (!Array.isArray(seed) || seed.length !== 4) {\n    throw new TypeError('seed must be an array with 4 numbers');\n  }\n\n  // uint64_t s = [seed ...]\n  this._state0U = seed[0] | 0;\n  this._state0L = seed[1] | 0;\n  this._state1U = seed[2] | 0;\n  this._state1L = seed[3] | 0;\n}\n\n/**\n * Returns a 64bit random number as a 2x32bit array\n * @return {array}\n */\nXorShift.prototype.randomint = function() {\n  // uint64_t s1 = s[0]\n  var s1U = this._state0U, s1L = this._state0L;\n  // uint64_t s0 = s[1]\n  var s0U = this._state1U, s0L = this._state1L;\n\n  // result = s0 + s1\n  var sumL = (s0L >>> 0) + (s1L >>> 0);\n  var resU = (s0U + s1U + (sumL / 2 >>> 31)) >>> 0;\n  var resL = sumL >>> 0;\n\n  // s[0] = s0\n  this._state0U = s0U;\n  this._state0L = s0L;\n\n  // - t1 = [0, 0]\n  var t1U = 0, t1L = 0;\n  // - t2 = [0, 0]\n  var t2U = 0, t2L = 0;\n\n  // s1 ^= s1 << 23;\n  // :: t1 = s1 << 23\n  var a1 = 23;\n  var m1 = 0xFFFFFFFF << (32 - a1);\n  t1U = (s1U << a1) | ((s1L & m1) >>> (32 - a1));\n  t1L = s1L << a1;\n  // :: s1 = s1 ^ t1\n  s1U = s1U ^ t1U;\n  s1L = s1L ^ t1L;\n\n  // t1 = ( s1 ^ s0 ^ ( s1 >> 17 ) ^ ( s0 >> 26 ) )\n  // :: t1 = s1 ^ s0\n  t1U = s1U ^ s0U;\n  t1L = s1L ^ s0L;\n  // :: t2 = s1 >> 18\n  var a2 = 18;\n  var m2 = 0xFFFFFFFF >>> (32 - a2);\n  t2U = s1U >>> a2;\n  t2L = (s1L >>> a2) | ((s1U & m2) << (32 - a2));\n  // :: t1 = t1 ^ t2\n  t1U = t1U ^ t2U;\n  t1L = t1L ^ t2L;\n  // :: t2 = s0 >> 5\n  var a3 = 5;\n  var m3 = 0xFFFFFFFF >>> (32 - a3);\n  t2U = s0U >>> a3;\n  t2L = (s0L >>> a3) | ((s0U & m3) << (32 - a3));\n  // :: t1 = t1 ^ t2\n  t1U = t1U ^ t2U;\n  t1L = t1L ^ t2L;\n\n  // s[1] = t1\n  this._state1U = t1U;\n  this._state1L = t1L;\n\n  // return result\n  return [resU, resL];\n};\n\n/**\n * Returns a random number normalized [0, 1), just like Math.random()\n * @return {number}\n */\nXorShift.prototype.random = function() {\n  var t2 = this.randomint();\n  // Math.pow(2, -32) = 2.3283064365386963e-10\n  // Math.pow(2, -52) = 2.220446049250313e-16\n  return t2[0] * 2.3283064365386963e-10 + (t2[1] >>> 12) * 2.220446049250313e-16;\n};\n\n// Seed with Math.random() by default to prevent seed collision\nfunction getRandomSeed() {\n    return Math.random() * Math.pow(2, 32);\n}\nmodule.exports = new XorShift([\n  getRandomSeed(),\n  getRandomSeed(),\n  getRandomSeed(),\n  getRandomSeed()\n]);\n\n// Export constructor under its own name so that consumers using ES2015\n// can write `import { XorShift } from 'xorshift'`.\nmodule.exports.XorShift = XorShift;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/xorshift/xorshift.js\n");

/***/ })

};
;