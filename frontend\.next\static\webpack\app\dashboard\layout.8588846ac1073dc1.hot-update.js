"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackend() {\n        // Always load from localStorage first for immediate access\n        this.loadSessionsFromStorage();\n        console.log('📂 Loaded sessions from localStorage for immediate access');\n        try {\n            // Try to establish backend connection\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 3000); // 3 second timeout\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.status < 500) {\n                this.useBackend = true;\n                this.backendInitialized = true;\n                console.log('✅ Session Manager: Backend connection established');\n                // Sync with backend - merge backend sessions with localStorage\n                await this.syncWithBackend();\n            } else {\n                throw new Error('Backend returned server error');\n            }\n        } catch (error) {\n            console.warn('⚠️ Session Manager: Backend unavailable, using localStorage only');\n            this.useBackend = false;\n            this.backendInitialized = false;\n        }\n        // Setup storage listener for cross-window sync\n        this.setupStorageListener();\n    }\n    async syncWithBackend() {\n        if (!this.useBackend || !this.backendInitialized) return;\n        try {\n            const token = localStorage.getItem('auth_token');\n            if (!token) {\n                console.warn('No auth token found, cannot sync with backend');\n                return;\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.getUserSessions();\n            const backendSessions = response.sessions;\n            // Create a map of backend sessions by ID\n            const backendSessionMap = new Map();\n            for (const backendSession of backendSessions){\n                backendSessionMap.set(backendSession.session_uuid, backendSession);\n            }\n            // Merge localStorage sessions with backend sessions\n            const mergedSessions = new Map(this.sessions);\n            // Add/update sessions from backend\n            for (const backendSession of backendSessions){\n                const session = {\n                    id: backendSession.session_uuid,\n                    name: backendSession.name,\n                    config: backendSession.config_snapshot,\n                    targetPriceRows: backendSession.target_price_rows || [],\n                    orderHistory: [],\n                    currentMarketPrice: backendSession.current_market_price || 0,\n                    crypto1Balance: backendSession.crypto1_balance || 10,\n                    crypto2Balance: backendSession.crypto2_balance || 100000,\n                    stablecoinBalance: backendSession.stablecoin_balance || 0,\n                    createdAt: new Date(backendSession.created_at).getTime(),\n                    lastModified: new Date(backendSession.last_modified).getTime(),\n                    isActive: backendSession.is_active || false,\n                    runtime: backendSession.runtime_seconds * 1000 || 0 // Convert to milliseconds\n                };\n                mergedSessions.set(session.id, session);\n                // Set current session if this one is active\n                if (session.isActive) {\n                    this.currentSessionId = session.id;\n                    const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                    localStorage.setItem(currentSessionKey, session.id);\n                }\n            }\n            // Upload localStorage-only sessions to backend\n            for (const [sessionId, localSession] of this.sessions){\n                if (!backendSessionMap.has(sessionId)) {\n                    console.log(\"\\uD83D\\uDCE4 Uploading localStorage session to backend: \".concat(localSession.name));\n                    try {\n                        await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession({\n                            name: localSession.name,\n                            config: localSession.config,\n                            targetPriceRows: localSession.targetPriceRows,\n                            currentMarketPrice: localSession.currentMarketPrice,\n                            crypto1Balance: localSession.crypto1Balance,\n                            crypto2Balance: localSession.crypto2Balance,\n                            stablecoinBalance: localSession.stablecoinBalance\n                        });\n                    } catch (error) {\n                        console.error(\"Failed to upload session \".concat(sessionId, \" to backend:\"), error);\n                    }\n                }\n            }\n            // Update local sessions map\n            this.sessions = mergedSessions;\n            console.log(\"\\uD83D\\uDD04 Synced \".concat(this.sessions.size, \" sessions between localStorage and backend\"));\n            // Save merged sessions back to localStorage\n            this.saveSessionsToStorage();\n        } catch (error) {\n            console.error('Failed to sync with backend:', error);\n        }\n    }\n    // loadSessionsFromBackend method replaced by syncWithBackend\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Load current session from window-specific storage (each window has its own current session)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            const currentSessionId = localStorage.getItem(currentSessionKey);\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config);\n    }\n    async createNewSession(name, config) {\n        if (this.useBackend && this.backendInitialized) {\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession({\n                    name,\n                    config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: 10,\n                    crypto2Balance: 100000,\n                    stablecoinBalance: 0\n                });\n                const sessionId = response.session.session_uuid;\n                // Add to local cache\n                const now = Date.now();\n                const newSession = {\n                    id: sessionId,\n                    name,\n                    config,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: 10,\n                    crypto2Balance: 100000,\n                    stablecoinBalance: 0,\n                    createdAt: now,\n                    lastModified: now,\n                    isActive: false,\n                    runtime: 0\n                };\n                this.sessions.set(sessionId, newSession);\n                this.saveSessionsToStorage(); // Always save to localStorage for immediate access\n                console.log('✅ Session created on backend and localStorage:', sessionId);\n                return sessionId;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n            // Don't disable backend completely, just fallback for this operation\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: 10,\n            crypto2Balance: 100000,\n            stablecoinBalance: 0,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        console.log('📝 Session created in localStorage (permanent storage):', sessionId);\n        // Try to sync to backend in background if available\n        if (this.useBackend && this.backendInitialized) {\n            setTimeout(async ()=>{\n                try {\n                    await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession({\n                        name,\n                        config,\n                        targetPriceRows: [],\n                        currentMarketPrice: 0,\n                        crypto1Balance: 10,\n                        crypto2Balance: 100000,\n                        stablecoinBalance: 0\n                    });\n                    console.log('📤 Session also uploaded to backend:', sessionId);\n                } catch (error) {\n                    console.warn('Failed to upload session to backend (localStorage still has it):', error);\n                }\n            }, 1000);\n        }\n        return sessionId;\n    }\n    saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = session.runtime + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = session.runtime + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            // ALWAYS save to localStorage first for immediate persistence\n            this.saveSessionsToStorage();\n            console.log(\"\\uD83D\\uDCBE Session \".concat(sessionId, \" saved to localStorage\"));\n            // Also save to backend if available for permanent cloud storage\n            if (this.useBackend && this.backendInitialized) {\n                this.saveSessionToBackend(sessionId, updatedSession).catch((error)=>{\n                    console.error('Failed to save session to backend:', error);\n                // Don't fail the operation if backend save fails - localStorage is primary\n                });\n            } else {\n                console.log(\"\\uD83D\\uDCDD Session \".concat(sessionId, \" saved to localStorage only (backend unavailable)\"));\n            }\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    async deleteSessionFromBackend(sessionId) {\n        if (!this.useBackend || !this.backendInitialized) return;\n        // Validate session ID before sending to backend\n        if (!sessionId || sessionId === 'undefined' || sessionId === 'null') {\n            console.error('❌ Attempted to delete invalid session ID from backend:', sessionId);\n            return;\n        }\n        try {\n            const response = await fetch(\"http://localhost:5000/sessions/\".concat(sessionId), {\n                method: 'DELETE',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP error! status: \".concat(response.status));\n            }\n            console.log(\"✅ Session \".concat(sessionId, \" deleted from backend\"));\n        } catch (error) {\n            console.error(\"Failed to delete session \".concat(sessionId, \" from backend:\"), error);\n            throw error;\n        }\n    }\n    async saveSessionToBackend(sessionId, session) {\n        if (!this.useBackend || !this.backendInitialized) return;\n        // Validate session ID before sending to backend\n        if (!sessionId || sessionId === 'undefined' || sessionId === 'null') {\n            console.error('❌ Attempted to save invalid session ID to backend:', sessionId);\n            return;\n        }\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, {\n                name: session.name,\n                config: session.config,\n                targetPriceRows: session.targetPriceRows,\n                currentMarketPrice: session.currentMarketPrice,\n                crypto1Balance: session.crypto1Balance,\n                crypto2Balance: session.crypto2Balance,\n                stablecoinBalance: session.stablecoinBalance,\n                isActive: session.isActive,\n                additionalRuntime: Math.floor(session.runtime / 1000) // Convert to seconds\n            });\n            console.log(\"\\uD83D\\uDCBE Session \".concat(sessionId, \" saved to backend\"));\n        } catch (error) {\n            console.error(\"Failed to save session \".concat(sessionId, \" to backend:\"), error);\n            throw error;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    deleteSession(sessionId) {\n        console.log('🗑️ SessionManager: Attempting to delete session:', sessionId);\n        console.log('🗑️ Sessions before deletion:', this.sessions.size);\n        const deleted = this.sessions.delete(sessionId);\n        console.log('🗑️ Local deletion result:', deleted);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                console.log('🗑️ Clearing current session ID');\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n            console.log('🗑️ Sessions after deletion:', this.sessions.size);\n            // Also try to delete from backend\n            this.deleteSessionFromBackend(sessionId).catch((error)=>{\n                console.warn('⚠️ Failed to delete session from backend:', error);\n            });\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        // Validate session ID before setting\n        if (!sessionId || sessionId === 'undefined' || sessionId === 'null') {\n            console.error('❌ Attempted to set invalid session ID:', sessionId);\n            return;\n        }\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            // Mark this session as active when it becomes the current session\n            const session = this.sessions.get(sessionId);\n            if (session && !session.isActive) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        // Validate current session ID\n        if (this.currentSessionId === 'undefined' || this.currentSessionId === 'null') {\n            console.warn('⚠️ Invalid currentSessionId detected:', this.currentSessionId, '- clearing it');\n            this.currentSessionId = null;\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            return null;\n        }\n        // Check if session actually exists\n        if (this.currentSessionId && !this.sessions.has(this.currentSessionId)) {\n            console.warn('⚠️ Current session ID not found in sessions:', this.currentSessionId, '- clearing it');\n            this.currentSessionId = null;\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n            return null;\n        }\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Mark current session as inactive before clearing\n        if (this.currentSessionId) {\n            const session = this.sessions.get(this.currentSessionId);\n            if (session && session.isActive) {\n                session.isActive = false;\n                session.lastModified = Date.now();\n                this.sessions.set(this.currentSessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive for window \").concat(this.windowId));\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime += additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return session.runtime + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime;\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.backendInitialized = false;\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Initialize backend connection first, then load sessions\n        this.initializeBackend();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});