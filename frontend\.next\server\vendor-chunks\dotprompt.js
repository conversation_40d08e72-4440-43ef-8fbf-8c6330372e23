"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dotprompt";
exports.ids = ["vendor-chunks/dotprompt"];
exports.modules = {

/***/ "(action-browser)/./node_modules/dotprompt/dist/index.js":
/*!**********************************************!*\
  !*** ./node_modules/dotprompt/dist/index.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar __create = Object.create;\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __getProtoOf = Object.getPrototypeOf;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(\n  // If the importer is in node compatibility mode or this is not an ESM\n  // file that has been converted to a CommonJS file using a Babel-\n  // compatible transform (i.e. \"__esModule\" has not been set), then set\n  // \"default\" to the CommonJS \"module.exports\" for node compatibility.\n  isNodeMode || !mod || !mod.__esModule ? __defProp(target, \"default\", { value: mod, enumerable: true }) : target,\n  mod\n));\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar index_exports = {};\n__export(index_exports, {\n  Dotprompt: () => Dotprompt,\n  PicoschemaParser: () => PicoschemaParser,\n  picoschema: () => picoschema\n});\nmodule.exports = __toCommonJS(index_exports);\n\n// src/dotprompt.ts\nvar Handlebars = __toESM(__webpack_require__(/*! handlebars */ \"(action-browser)/./node_modules/handlebars/lib/index.js\"));\n\n// src/helpers.ts\nvar helpers_exports = {};\n__export(helpers_exports, {\n  history: () => history,\n  ifEquals: () => ifEquals,\n  json: () => json,\n  media: () => media,\n  role: () => role,\n  section: () => section,\n  unlessEquals: () => unlessEquals\n});\nvar import_handlebars = __webpack_require__(/*! handlebars */ \"(action-browser)/./node_modules/handlebars/lib/index.js\");\nfunction json(serializable, options) {\n  return new import_handlebars.SafeString(\n    JSON.stringify(serializable, null, options.hash.indent || 0)\n  );\n}\nfunction role(role2) {\n  return new import_handlebars.SafeString(`<<<dotprompt:role:${role2}>>>`);\n}\nfunction history() {\n  return new import_handlebars.SafeString(\"<<<dotprompt:history>>>\");\n}\nfunction section(name) {\n  return new import_handlebars.SafeString(`<<<dotprompt:section ${name}>>>`);\n}\nfunction media(options) {\n  return new import_handlebars.SafeString(\n    `<<<dotprompt:media:url ${options.hash.url}${options.hash.contentType ? ` ${options.hash.contentType}` : \"\"}>>>`\n  );\n}\nfunction ifEquals(arg1, arg2, options) {\n  return arg1 === arg2 ? options.fn(this) : options.inverse(this);\n}\nfunction unlessEquals(arg1, arg2, options) {\n  return arg1 !== arg2 ? options.fn(this) : options.inverse(this);\n}\n\n// src/parse.ts\nvar import_yaml = __webpack_require__(/*! yaml */ \"(action-browser)/./node_modules/yaml/dist/index.js\");\nvar ROLE_MARKER_PREFIX = \"<<<dotprompt:role:\";\nvar HISTORY_MARKER_PREFIX = \"<<<dotprompt:history\";\nvar MEDIA_MARKER_PREFIX = \"<<<dotprompt:media:\";\nvar SECTION_MARKER_PREFIX = \"<<<dotprompt:section\";\nvar FRONTMATTER_AND_BODY_REGEX = /^---\\s*\\n([\\s\\S]*?)\\n---\\s*\\n([\\s\\S]*)$/;\nvar ROLE_AND_HISTORY_MARKER_REGEX = /(<<<dotprompt:(?:role:[a-z]+|history))>>>/g;\nvar MEDIA_AND_SECTION_MARKER_REGEX = /(<<<dotprompt:(?:media:url|section).*?)>>>/g;\nvar RESERVED_METADATA_KEYWORDS = [\n  // NOTE: KEEP SORTED\n  \"config\",\n  \"description\",\n  \"ext\",\n  \"input\",\n  \"model\",\n  \"name\",\n  \"output\",\n  \"raw\",\n  \"toolDefs\",\n  \"tools\",\n  \"variant\",\n  \"version\"\n];\nvar BASE_METADATA = {\n  ext: {},\n  metadata: {},\n  config: {}\n};\nfunction splitByRegex(source, regex) {\n  return source.split(regex).filter((s) => s.trim() !== \"\");\n}\nfunction splitByRoleAndHistoryMarkers(renderedString) {\n  return splitByRegex(renderedString, ROLE_AND_HISTORY_MARKER_REGEX);\n}\nfunction splitByMediaAndSectionMarkers(source) {\n  return splitByRegex(source, MEDIA_AND_SECTION_MARKER_REGEX);\n}\nfunction convertNamespacedEntryToNestedObject(key, value, obj = {}) {\n  const result = obj || {};\n  const lastDotIndex = key.lastIndexOf(\".\");\n  const ns = key.substring(0, lastDotIndex);\n  const field = key.substring(lastDotIndex + 1);\n  result[ns] = result[ns] || {};\n  result[ns][field] = value;\n  return result;\n}\nfunction extractFrontmatterAndBody(source) {\n  const match = source.match(FRONTMATTER_AND_BODY_REGEX);\n  if (match) {\n    const [, frontmatter, body] = match;\n    return { frontmatter, body };\n  }\n  return { frontmatter: \"\", body: \"\" };\n}\nfunction parseDocument(source) {\n  const { frontmatter, body } = extractFrontmatterAndBody(source);\n  if (frontmatter) {\n    try {\n      const parsedMetadata = (0, import_yaml.parse)(frontmatter);\n      const raw = { ...parsedMetadata };\n      const pruned = { ...BASE_METADATA };\n      const ext = {};\n      for (const k in raw) {\n        const key = k;\n        if (RESERVED_METADATA_KEYWORDS.includes(key)) {\n          pruned[key] = raw[key];\n        } else if (key.includes(\".\")) {\n          convertNamespacedEntryToNestedObject(key, raw[key], ext);\n        }\n      }\n      return { ...pruned, raw, ext, template: body.trim() };\n    } catch (error) {\n      console.error(\"Dotprompt: Error parsing YAML frontmatter:\", error);\n      return { ...BASE_METADATA, template: source.trim() };\n    }\n  }\n  return { ...BASE_METADATA, template: source };\n}\nfunction messageSourcesToMessages(messageSources) {\n  return messageSources.filter((ms) => ms.content || ms.source).map((m) => {\n    const out = {\n      role: m.role,\n      content: m.content || toParts(m.source || \"\")\n    };\n    if (m.metadata) {\n      out.metadata = m.metadata;\n    }\n    return out;\n  });\n}\nfunction transformMessagesToHistory(messages) {\n  return messages.map((m) => ({\n    ...m,\n    metadata: { ...m.metadata, purpose: \"history\" }\n  }));\n}\nfunction toMessages(renderedString, data) {\n  let currentMessage = { role: \"user\", source: \"\" };\n  const messageSources = [currentMessage];\n  for (const piece of splitByRoleAndHistoryMarkers(renderedString)) {\n    if (piece.startsWith(ROLE_MARKER_PREFIX)) {\n      const role2 = piece.substring(ROLE_MARKER_PREFIX.length);\n      if (currentMessage.source?.trim()) {\n        currentMessage = { role: role2, source: \"\" };\n        messageSources.push(currentMessage);\n      } else {\n        currentMessage.role = role2;\n      }\n    } else if (piece.startsWith(HISTORY_MARKER_PREFIX)) {\n      const historyMessages = transformMessagesToHistory(data?.messages ?? []);\n      if (historyMessages) {\n        messageSources.push(...historyMessages);\n      }\n      currentMessage = { role: \"model\", source: \"\" };\n      messageSources.push(currentMessage);\n    } else {\n      currentMessage.source += piece;\n    }\n  }\n  const messages = messageSourcesToMessages(messageSources);\n  return insertHistory(messages, data?.messages);\n}\nfunction messagesHaveHistory(messages) {\n  return messages.some((m) => m.metadata?.purpose === \"history\");\n}\nfunction insertHistory(messages, history2 = []) {\n  if (!history2 || messagesHaveHistory(messages)) {\n    return messages;\n  }\n  if (messages.length === 0) {\n    return history2;\n  }\n  const lastMessage = messages.at(-1);\n  if (lastMessage?.role === \"user\") {\n    const messagesWithoutLast = messages.slice(0, -1);\n    return [...messagesWithoutLast, ...history2, lastMessage];\n  }\n  return [...messages, ...history2];\n}\nfunction toParts(source) {\n  return splitByMediaAndSectionMarkers(source).map(parsePart);\n}\nfunction parsePart(piece) {\n  if (piece.startsWith(MEDIA_MARKER_PREFIX)) {\n    return parseMediaPart(piece);\n  } else if (piece.startsWith(SECTION_MARKER_PREFIX)) {\n    return parseSectionPart(piece);\n  }\n  return parseTextPart(piece);\n}\nfunction parseMediaPart(piece) {\n  if (!piece.startsWith(MEDIA_MARKER_PREFIX)) {\n    throw new Error(\"Invalid media piece\");\n  }\n  const [_, url, contentType] = piece.split(\" \");\n  const part = { media: { url } };\n  if (contentType) {\n    part.media.contentType = contentType;\n  }\n  return part;\n}\nfunction parseSectionPart(piece) {\n  if (!piece.startsWith(SECTION_MARKER_PREFIX)) {\n    throw new Error(\"Invalid section piece\");\n  }\n  const [_, sectionType] = piece.split(\" \");\n  return { metadata: { purpose: sectionType, pending: true } };\n}\nfunction parseTextPart(piece) {\n  return { text: piece };\n}\n\n// src/picoschema.ts\nvar JSON_SCHEMA_SCALAR_TYPES = [\n  \"string\",\n  \"boolean\",\n  \"null\",\n  \"number\",\n  \"integer\",\n  \"any\"\n];\nvar WILDCARD_PROPERTY_NAME = \"(*)\";\nasync function picoschema(schema, options) {\n  return new PicoschemaParser(options).parse(schema);\n}\nvar PicoschemaParser = class {\n  schemaResolver;\n  constructor(options) {\n    this.schemaResolver = options?.schemaResolver;\n  }\n  async mustResolveSchema(schemaName) {\n    if (!this.schemaResolver) {\n      throw new Error(`Picoschema: unsupported scalar type '${schemaName}'.`);\n    }\n    const val = await this.schemaResolver(schemaName);\n    if (!val) {\n      throw new Error(\n        `Picoschema: could not find schema with name '${schemaName}'`\n      );\n    }\n    return val;\n  }\n  async parse(schema) {\n    if (!schema) return null;\n    if (typeof schema === \"string\") {\n      const [type, description] = extractDescription(schema);\n      if (JSON_SCHEMA_SCALAR_TYPES.includes(type)) {\n        let out = { type };\n        if (description) out = { ...out, description };\n        return out;\n      }\n      const resolvedSchema = await this.mustResolveSchema(type);\n      return description ? { ...resolvedSchema, description } : resolvedSchema;\n    }\n    if ([...JSON_SCHEMA_SCALAR_TYPES, \"object\", \"array\"].includes(\n      schema?.type\n    )) {\n      return schema;\n    }\n    if (typeof schema?.properties === \"object\") {\n      return { ...schema, type: \"object\" };\n    }\n    return this.parsePico(schema);\n  }\n  async parsePico(obj, path = []) {\n    if (typeof obj === \"string\") {\n      const [type, description] = extractDescription(obj);\n      if (!JSON_SCHEMA_SCALAR_TYPES.includes(type)) {\n        let resolvedSchema = await this.mustResolveSchema(type);\n        if (description) resolvedSchema = { ...resolvedSchema, description };\n        return resolvedSchema;\n      }\n      if (type === \"any\") {\n        return description ? { description } : {};\n      }\n      return description ? { type, description } : { type };\n    } else if (typeof obj !== \"object\") {\n      throw new Error(\n        \"Picoschema: only consists of objects and strings. Got: \" + JSON.stringify(obj)\n      );\n    }\n    const schema = {\n      type: \"object\",\n      properties: {},\n      required: [],\n      additionalProperties: false\n    };\n    for (const key in obj) {\n      if (key === WILDCARD_PROPERTY_NAME) {\n        schema.additionalProperties = await this.parsePico(obj[key], [\n          ...path,\n          key\n        ]);\n        continue;\n      }\n      const [name, typeInfo] = key.split(\"(\");\n      const isOptional = name.endsWith(\"?\");\n      const propertyName = isOptional ? name.slice(0, -1) : name;\n      if (!isOptional) {\n        schema.required.push(propertyName);\n      }\n      if (!typeInfo) {\n        const prop = { ...await this.parsePico(obj[key], [...path, key]) };\n        if (isOptional && typeof prop.type === \"string\") {\n          prop.type = [prop.type, \"null\"];\n        }\n        schema.properties[propertyName] = prop;\n        continue;\n      }\n      const [type, description] = extractDescription(\n        typeInfo.substring(0, typeInfo.length - 1)\n      );\n      if (type === \"array\") {\n        schema.properties[propertyName] = {\n          type: isOptional ? [\"array\", \"null\"] : \"array\",\n          items: await this.parsePico(obj[key], [...path, key])\n        };\n      } else if (type === \"object\") {\n        const prop = await this.parsePico(obj[key], [...path, key]);\n        if (isOptional) prop.type = [prop.type, \"null\"];\n        schema.properties[propertyName] = prop;\n      } else if (type === \"enum\") {\n        const prop = { enum: obj[key] };\n        if (isOptional && !prop.enum.includes(null)) prop.enum.push(null);\n        schema.properties[propertyName] = prop;\n      } else {\n        throw new Error(\n          \"Picoschema: parenthetical types must be 'object' or 'array', got: \" + type\n        );\n      }\n      if (description) {\n        schema.properties[propertyName].description = description;\n      }\n    }\n    if (!schema.required.length) delete schema.required;\n    return schema;\n  }\n};\nfunction extractDescription(input) {\n  if (!input.includes(\",\")) return [input, null];\n  const match = input.match(/(.*?), *(.*)$/);\n  return [match[1], match[2]];\n}\n\n// src/util.ts\nfunction removeUndefinedFields(obj) {\n  if (obj === null || typeof obj !== \"object\") {\n    return obj;\n  }\n  if (Array.isArray(obj)) {\n    return obj.map((item) => removeUndefinedFields(item));\n  }\n  const result = {};\n  for (const [key, value] of Object.entries(obj)) {\n    if (value !== void 0) {\n      result[key] = removeUndefinedFields(value);\n    }\n  }\n  return result;\n}\n\n// src/dotprompt.ts\nvar Dotprompt = class {\n  handlebars;\n  knownHelpers = {};\n  defaultModel;\n  modelConfigs = {};\n  tools = {};\n  toolResolver;\n  schemas = {};\n  schemaResolver;\n  partialResolver;\n  store;\n  constructor(options) {\n    this.handlebars = Handlebars.noConflict();\n    this.modelConfigs = options?.modelConfigs || this.modelConfigs;\n    this.defaultModel = options?.defaultModel;\n    this.tools = options?.tools || {};\n    this.toolResolver = options?.toolResolver;\n    this.schemas = options?.schemas || {};\n    this.schemaResolver = options?.schemaResolver;\n    this.partialResolver = options?.partialResolver;\n    for (const key in helpers_exports) {\n      this.defineHelper(key, helpers_exports[key]);\n      this.handlebars.registerHelper(key, helpers_exports[key]);\n    }\n    if (options?.helpers) {\n      for (const key in options.helpers) {\n        this.defineHelper(key, options.helpers[key]);\n      }\n    }\n    if (options?.partials) {\n      for (const key in options.partials) {\n        this.definePartial(key, options.partials[key]);\n      }\n    }\n  }\n  /**\n   * Registers a helper function for use in templates.\n   *\n   * @param name The name of the helper function to register\n   * @param fn The helper function implementation\n   * @return This instance for method chaining\n   */\n  defineHelper(name, fn) {\n    this.handlebars.registerHelper(name, fn);\n    this.knownHelpers[name] = true;\n    return this;\n  }\n  /**\n   * Registers a partial template for use in other templates.\n   *\n   * @param name The name of the partial to register\n   * @param source The template source for the partial\n   * @return This instance for method chaining\n   */\n  definePartial(name, source) {\n    this.handlebars.registerPartial(name, source);\n    return this;\n  }\n  /**\n   * Registers a tool definition for use in prompts.\n   *\n   * @param def The tool definition to register\n   * @return This instance for method chaining\n   */\n  defineTool(def) {\n    this.tools[def.name] = def;\n    return this;\n  }\n  /**\n   * Parses a prompt template string into a structured ParsedPrompt object.\n   *\n   * @param source The template source string to parse\n   * @return A parsed prompt object with extracted metadata and template\n   */\n  parse(source) {\n    return parseDocument(source);\n  }\n  /**\n   * Renders a prompt template with the provided data.\n   *\n   * @param source The template source string to render\n   * @param data The data to use when rendering the template\n   * @param options Additional metadata and options for rendering\n   * @return A promise resolving to the rendered prompt\n   */\n  async render(source, data = {}, options) {\n    const renderer = await this.compile(source);\n    return renderer(data, options);\n  }\n  /**\n   * Processes schema definitions in picoschema format into standard JSON Schema.\n   *\n   * @param meta The prompt metadata containing schema definitions\n   * @return A promise resolving to the processed metadata with expanded schemas\n   */\n  async renderPicoschema(meta) {\n    if (!meta.output?.schema && !meta.input?.schema) {\n      return meta;\n    }\n    const newMeta = { ...meta };\n    if (meta.input?.schema) {\n      newMeta.input = {\n        ...meta.input,\n        schema: await picoschema(meta.input.schema, {\n          schemaResolver: this.wrappedSchemaResolver.bind(this)\n        })\n      };\n    }\n    if (meta.output?.schema) {\n      newMeta.output = {\n        ...meta.output,\n        schema: await picoschema(meta.output.schema, {\n          schemaResolver: this.wrappedSchemaResolver.bind(this)\n        })\n      };\n    }\n    return newMeta;\n  }\n  /**\n   * Resolves a schema name to its definition, using registered schemas or schema resolver.\n   *\n   * @param name The name of the schema to resolve\n   * @return A promise resolving to the schema definition or null if not found\n   */\n  async wrappedSchemaResolver(name) {\n    if (this.schemas[name]) {\n      return this.schemas[name];\n    }\n    if (this.schemaResolver) {\n      return await this.schemaResolver(name);\n    }\n    return null;\n  }\n  /**\n   * Merges multiple metadata objects together, resolving tools and schemas.\n   *\n   * @param base The base metadata object\n   * @param merges Additional metadata objects to merge into the base\n   * @return A promise resolving to the merged and processed metadata\n   */\n  async resolveMetadata(base, ...merges) {\n    let out = { ...base };\n    for (let i = 0; i < merges.length; i++) {\n      if (!merges[i]) continue;\n      const config = out.config || {};\n      out = { ...out, ...merges[i] };\n      out.config = { ...config, ...merges[i]?.config || {} };\n    }\n    const { template: _, ...outWithoutTemplate } = out;\n    out = outWithoutTemplate;\n    out = removeUndefinedFields(out);\n    out = await this.resolveTools(out);\n    out = await this.renderPicoschema(out);\n    return out;\n  }\n  /**\n   * Resolves tool names to their definitions using registered tools or tool resolver.\n   *\n   * @param base The metadata containing tool references to resolve\n   * @return A promise resolving to metadata with resolved tool definitions\n   */\n  async resolveTools(base) {\n    const out = { ...base };\n    if (out.tools) {\n      const outTools = [];\n      out.toolDefs = out.toolDefs || [];\n      await Promise.all(\n        out.tools.map(async (toolName) => {\n          if (this.tools[toolName]) {\n            if (out.toolDefs) {\n              out.toolDefs.push(this.tools[toolName]);\n            }\n          } else if (this.toolResolver) {\n            const resolvedTool = await this.toolResolver(toolName);\n            if (!resolvedTool) {\n              throw new Error(\n                `Dotprompt: Unable to resolve tool '${toolName}' to a recognized tool definition.`\n              );\n            }\n            if (out.toolDefs) {\n              out.toolDefs.push(resolvedTool);\n            }\n          } else {\n            outTools.push(toolName);\n          }\n        })\n      );\n      out.tools = outTools;\n    }\n    return out;\n  }\n  /**\n   * Identifies all partial references in a template.\n   *\n   * @param template The template to scan for partial references\n   * @return A set of partial names referenced in the template\n   */\n  identifyPartials(template) {\n    const ast = this.handlebars.parse(template);\n    const partials = /* @__PURE__ */ new Set();\n    const visitor = new class extends this.handlebars.Visitor {\n      // Visit partial statements and add their names to our set.\n      PartialStatement(partial) {\n        if (partial && typeof partial === \"object\" && \"name\" in partial && partial.name && typeof partial.name === \"object\" && \"original\" in partial.name && typeof partial.name.original === \"string\") {\n          partials.add(partial.name.original);\n        }\n      }\n    }();\n    visitor.accept(ast);\n    return partials;\n  }\n  /**\n   * Resolves and registers all partials referenced in a template.\n   *\n   * @param template The template containing partial references\n   * @return A promise that resolves when all partials are registered\n   */\n  async resolvePartials(template) {\n    if (!this.partialResolver && !this.store) {\n      return;\n    }\n    const partials = this.identifyPartials(template);\n    await Promise.all(\n      Array.from(partials).map(async (name) => {\n        if (!this.handlebars.partials[name]) {\n          let content = null;\n          if (this.partialResolver) {\n            content = await this.partialResolver(name);\n          }\n          if (!content && this.store) {\n            const partial = await this.store.loadPartial(name);\n            content = partial?.source;\n          }\n          if (content) {\n            this.definePartial(name, content);\n            await this.resolvePartials(content);\n          }\n        }\n      })\n    );\n  }\n  /**\n   * Compiles a template into a reusable function for rendering prompts.\n   *\n   * @param source The template source or parsed prompt to compile\n   * @param additionalMetadata Additional metadata to include in the compiled template\n   * @return A promise resolving to a function for rendering the template\n   */\n  async compile(source, additionalMetadata) {\n    let parsedSource;\n    if (typeof source === \"string\") {\n      parsedSource = this.parse(source);\n    } else {\n      parsedSource = source;\n    }\n    if (additionalMetadata) {\n      parsedSource = { ...parsedSource, ...additionalMetadata };\n    }\n    await this.resolvePartials(parsedSource.template);\n    const renderString = this.handlebars.compile(\n      parsedSource.template,\n      {\n        knownHelpers: this.knownHelpers,\n        knownHelpersOnly: true,\n        noEscape: true\n      }\n    );\n    const renderFunc = async (data, options) => {\n      const { input, ...mergedMetadata } = await this.renderMetadata(parsedSource);\n      const renderedString = renderString(\n        { ...options?.input?.default || {}, ...data.input },\n        {\n          data: {\n            metadata: {\n              prompt: mergedMetadata,\n              docs: data.docs,\n              messages: data.messages\n            },\n            ...data.context || {}\n          }\n        }\n      );\n      return {\n        ...mergedMetadata,\n        messages: toMessages(renderedString, data)\n      };\n    };\n    renderFunc.prompt = parsedSource;\n    return renderFunc;\n  }\n  /**\n   * Processes and resolves all metadata for a prompt template.\n   *\n   * @param source The template source or parsed prompt\n   * @param additionalMetadata Additional metadata to include\n   * @return A promise resolving to the fully processed metadata\n   */\n  async renderMetadata(source, additionalMetadata) {\n    let parsedSource;\n    if (typeof source === \"string\") {\n      parsedSource = this.parse(source);\n    } else {\n      parsedSource = source;\n    }\n    const selectedModel = additionalMetadata?.model || parsedSource.model || this.defaultModel;\n    let modelConfig;\n    if (selectedModel && this.modelConfigs[selectedModel]) {\n      modelConfig = this.modelConfigs[selectedModel];\n    }\n    return this.resolveMetadata(\n      modelConfig ? { config: modelConfig } : {},\n      parsedSource,\n      additionalMetadata\n    );\n  }\n};\n// Annotate the CommonJS export names for ESM import in node:\n0 && (0);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/dotprompt/dist/index.js\n");

/***/ })

};
;