# Pluto Trading Bot API Schema

## Authentication

### Register
- **URL**: `/auth/register`
- **Method**: `POST`
- **Request Body**:
```json
{
  "username": "string",
  "password": "string",
  "email": "string (optional)"
}
```
- **Response**: 
```json
{
  "message": "User registered successfully",
  "user": {
    "id": "number",
    "username": "string",
    "email": "string",
    "created_at": "string (ISO date)"
  },
  "access_token": "string",
  "refresh_token": "string"
}
```

### Login
- **URL**: `/auth/login`
- **Method**: `POST`
- **Request Body**:
```json
{
  "username": "string",
  "password": "string"
}
```
- **Response**: 
```json
{
  "message": "Login successful",
  "user": {
    "id": "number",
    "username": "string",
    "email": "string",
    "created_at": "string (ISO date)"
  },
  "access_token": "string",
  "refresh_token": "string"
}
```

### Refresh Token
- **URL**: `/auth/refresh`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer {refresh_token}`
- **Response**: 
```json
{
  "message": "Token refreshed",
  "access_token": "string"
}
```

### Get Current User
- **URL**: `/auth/me`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer {access_token}`
- **Response**: 
```json
{
  "user": {
    "id": "number",
    "username": "string",
    "email": "string",
    "created_at": "string (ISO date)"
  }
}
```

## User API Keys

### Get User's API Keys
- **URL**: `/user/apikeys`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer {access_token}`
- **Response**: 
```json
{
  "api_keys": [
    {
      "id": "number",
      "user_id": "number",
      "exchange_name": "string",
      "api_key": "string (masked)",
      "secret_key": "string (masked)",
      "label": "string",
      "is_active": "boolean",
      "created_at": "string (ISO date)"
    }
  ]
}
```

### Add API Key
- **URL**: `/user/apikeys`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer {access_token}`
- **Request Body**:
```json
{
  "exchange_name": "string (default: binance)",
  "api_key": "string",
  "secret_key": "string",
  "label": "string (optional)"
}
```
- **Response**: 
```json
{
  "message": "API key added successfully",
  "api_key": {
    "id": "number",
    "user_id": "number",
    "exchange_name": "string",
    "api_key": "string (masked)",
    "secret_key": "string (masked)",
    "label": "string",
    "is_active": "boolean",
    "created_at": "string (ISO date)"
  }
}
```

### Update API Key
- **URL**: `/user/apikeys/{key_id}`
- **Method**: `PUT`
- **Headers**: `Authorization: Bearer {access_token}`
- **Request Body** (all fields optional):
```json
{
  "label": "string",
  "api_key": "string",
  "secret_key": "string",
  "is_active": "boolean"
}
```
- **Response**: 
```json
{
  "message": "API key updated successfully",
  "api_key": {
    "id": "number",
    "user_id": "number",
    "exchange_name": "string",
    "api_key": "string (masked)",
    "secret_key": "string (masked)",
    "label": "string",
    "is_active": "boolean",
    "created_at": "string (ISO date)"
  }
}
```

### Delete API Key
- **URL**: `/user/apikeys/{key_id}`
- **Method**: `DELETE`
- **Headers**: `Authorization: Bearer {access_token}`
- **Response**: 
```json
{
  "message": "API key deleted successfully"
}
```

## Trading Configurations

### Get All Configurations
- **URL**: `/trading/config`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer {access_token}`
- **Response**: 
```json
{
  "configs": [
    {
      "id": "number",
      "user_id": "number",
      "name": "string",
      "trading_mode": "string",
      "crypto1": "string",
      "crypto2": "string",
      "base_bid": "number",
      "multiplier": "number",
      "num_digits": "number",
      "slippage_percent": "number",
      "income_split_crypto1_percent": "number",
      "income_split_crypto2_percent": "number",
      "preferred_stablecoin": "string",
      "target_prices": ["number"],
      "is_active": "boolean",
      "created_at": "string (ISO date)",
      "updated_at": "string (ISO date)"
    }
  ]
}
```

### Get Specific Configuration
- **URL**: `/trading/config/{config_id}`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer {access_token}`
- **Response**: 
```json
{
  "config": {
    "id": "number",
    "user_id": "number",
    "name": "string",
    "trading_mode": "string",
    "crypto1": "string",
    "crypto2": "string",
    "base_bid": "number",
    "multiplier": "number",
    "num_digits": "number",
    "slippage_percent": "number",
    "income_split_crypto1_percent": "number",
    "income_split_crypto2_percent": "number",
    "preferred_stablecoin": "string",
    "target_prices": ["number"],
    "is_active": "boolean",
    "created_at": "string (ISO date)",
    "updated_at": "string (ISO date)"
  }
}
```

### Create New Configuration
- **URL**: `/trading/config`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer {access_token}`
- **Request Body**:
```json
{
  "name": "string",
  "tradingMode": "string",
  "crypto1": "string",
  "crypto2": "string",
  "baseBid": "number",
  "multiplier": "number",
  "numDigits": "number",
  "slippagePercent": "number",
  "incomeSplitCrypto1Percent": "number",
  "incomeSplitCrypto2Percent": "number",
  "preferredStablecoin": "string (optional)",
  "targetPrices": ["number (optional)"]
}
```
- **Response**: 
```json
{
  "message": "Trading configuration created successfully",
  "config": {
    "id": "number",
    "user_id": "number",
    "name": "string",
    "trading_mode": "string",
    "crypto1": "string",
    "crypto2": "string",
    "base_bid": "number",
    "multiplier": "number",
    "num_digits": "number",
    "slippage_percent": "number",
    "income_split_crypto1_percent": "number",
    "income_split_crypto2_percent": "number",
    "preferred_stablecoin": "string",
    "target_prices": ["number"],
    "is_active": "boolean",
    "created_at": "string (ISO date)",
    "updated_at": "string (ISO date)"
  }
}
```

### Update Configuration
- **URL**: `/trading/config/{config_id}`
- **Method**: `PUT`
- **Headers**: `Authorization: Bearer {access_token}`
- **Request Body** (all fields optional):
```json
{
  "name": "string",
  "tradingMode": "string",
  "crypto1": "string",
  "crypto2": "string",
  "baseBid": "number",
  "multiplier": "number",
  "numDigits": "number",
  "slippagePercent": "number",
  "incomeSplitCrypto1Percent": "number",
  "incomeSplitCrypto2Percent": "number",
  "preferredStablecoin": "string",
  "targetPrices": ["number"]
}
```
- **Response**: 
```json
{
  "message": "Trading configuration updated successfully",
  "config": {
    "id": "number",
    "user_id": "number",
    "name": "string",
    "trading_mode": "string",
    "crypto1": "string",
    "crypto2": "string",
    "base_bid": "number",
    "multiplier": "number",
    "num_digits": "number",
    "slippage_percent": "number",
    "income_split_crypto1_percent": "number",
    "income_split_crypto2_percent": "number",
    "preferred_stablecoin": "string",
    "target_prices": ["number"],
    "is_active": "boolean",
    "created_at": "string (ISO date)",
    "updated_at": "string (ISO date)"
  }
}
```

### Delete Configuration
- **URL**: `/trading/config/{config_id}`
- **Method**: `DELETE`
- **Headers**: `Authorization: Bearer {access_token}`
- **Response**: 
```json
{
  "message": "Trading configuration deleted successfully"
}
```

## Bot Control

### Start Bot
- **URL**: `/trading/bot/start/{config_id}`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer {access_token}`
- **Response**: 
```json
{
  "message": "Bot started for configuration {config_name}"
}
```

### Stop Bot
- **URL**: `/trading/bot/stop/{config_id}`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer {access_token}`
- **Response**: 
```json
{
  "message": "Bot stopped for configuration {config_name}"
}
```

### Get Bot Status
- **URL**: `/trading/bot/status/{config_id}`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer {access_token}`
- **Response**: 
```json
{
  "isRunning": "boolean",
  "currentMarketPrice": "number",
  "lastCheck": "string (ISO date)",
  "targetStates": [
    {
      "id": "string",
      "targetPrice": "number",
      "status": "string",
      "orderLevel": "number",
      "valueLevel": "number",
      "crypto1AmountHeld": "number",
      "originalCostCrypto2": "number"
    }
  ]
}
```

### Get Bot Logs
- **URL**: `/trading/bot/logs/{config_id}`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer {access_token}`
- **Response**: 
```json
{
  "logs": [
    {
      "id": "number",
      "user_id": "number",
      "config_id": "number",
      "timestamp": "string (ISO date)",
      "log_level": "string",
      "message": "string",
      "details": "string"
    }
  ]
}
```

## Trading Data

### Get Trade History
- **URL**: `/trading/history`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer {access_token}`
- **Query Parameters**:
  - `configId`: Filter by configuration ID (optional)
  - `limit`: Maximum number of trades to return (default: 100)
- **Response**: 
```json
{
  "trades": [
    {
      "id": "number",
      "user_id": "number",
      "config_id": "number",
      "exchange_order_id": "string",
      "timestamp": "string (ISO date)",
      "pair": "string",
      "crypto1": "string",
      "crypto2": "string",
      "order_type": "string",
      "amount_crypto1": "number",
      "avg_price": "number",
      "value_crypto2": "number",
      "fees": "number",
      "realized_pnl_crypto2": "number",
      "target_price_id": "string"
    }
  ]
}
```

### Get Market Data
- **URL**: `/trading/market-data/{pair}`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer {access_token}`
- **Response**: 
```json
{
  "pair": "string",
  "price": "number",
  "timestamp": "string (ISO date)"
}
```

### Get Balances
- **URL**: `/trading/balances`
- **Method**: `GET`
- **Headers**: `Authorization: Bearer {access_token}`
- **Response**: 
```json
{
  "balances": {
    "BTC": "number",
    "ETH": "number",
    "USDT": "number"
    // Additional currencies as available
  }
}
```

## AI Recommendations

### Get Trading Mode Suggestion
- **URL**: `/ai/suggest-mode`
- **Method**: `POST`
- **Headers**: `Authorization: Bearer {access_token}`
- **Request Body**:
```json
{
  "riskTolerance": "string (low, medium, high)",
  "preferredCryptos": ["string"],
  "investmentGoals": "string"
}
```
- **Response**: 
```json
{
  "suggestion": {
    "recommendedMode": "string",
    "crypto1": "string",
    "crypto2": "string",
    "preferredStablecoin": "string",
    "baseBid": "number",
    "multiplier": "number",
    "explanation": "string"
  }
}
```

## Error Responses

All API endpoints may return the following error responses:

### 400 Bad Request
```json
{
  "error": "Bad request",
  "message": "Error details"
}
```

### 401 Unauthorized
```json
{
  "error": "Unauthorized",
  "message": "Authentication required"
}
```

### 404 Not Found
```json
{
  "error": "Resource not found",
  "message": "Error details"
}
```

### 500 Internal Server Error
```json
{
  "error": "Internal server error",
  "message": "An unexpected error occurred. Please try again later."
}
``` 