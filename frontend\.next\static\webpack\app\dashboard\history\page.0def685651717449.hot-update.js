"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/history/page",{

/***/ "(app-pages-browser)/./src/lib/session-manager.ts":
/*!************************************!*\
  !*** ./src/lib/session-manager.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n\n\nconst SESSIONS_STORAGE_KEY = 'pluto_trading_sessions';\nconst CURRENT_SESSION_KEY = 'pluto_current_session';\n// Generate a unique window ID for this browser tab/window\nconst generateWindowId = ()=>{\n    return \"window_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n};\n// Get or create window ID for this tab\nconst getWindowId = ()=>{\n    if (false) {}\n    // Use sessionStorage (tab-specific) instead of localStorage (shared across tabs)\n    let windowId = sessionStorage.getItem('pluto_window_id');\n    if (!windowId) {\n        windowId = generateWindowId();\n        sessionStorage.setItem('pluto_window_id', windowId);\n    }\n    return windowId;\n};\nclass SessionManager {\n    static getInstance() {\n        if (!SessionManager.instance) {\n            SessionManager.instance = new SessionManager();\n        }\n        return SessionManager.instance;\n    }\n    generateSessionName(config) {\n        const crypto1 = config.crypto1 || 'Crypto1';\n        const crypto2 = config.crypto2 || 'Crypto2';\n        const tradingMode = config.tradingMode || 'SimpleSpot';\n        const baseName = \"\".concat(crypto1, \"/\").concat(crypto2, \" \").concat(tradingMode);\n        // Check for existing sessions with the same base name\n        const existingSessions = Array.from(this.sessions.values());\n        const similarSessions = existingSessions.filter((session)=>session.name.startsWith(baseName));\n        if (similarSessions.length === 0) {\n            return baseName;\n        }\n        // Find the highest session number\n        let maxNumber = 0;\n        similarSessions.forEach((session)=>{\n            const match = session.name.match(new RegExp(\"^\".concat(baseName.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), \" Session (\\\\d+)$\")));\n            if (match) {\n                const number = parseInt(match[1], 10);\n                if (number > maxNumber) {\n                    maxNumber = number;\n                }\n            } else if (session.name === baseName) {\n                // If there's an exact match, treat it as \"Session 1\"\n                maxNumber = Math.max(maxNumber, 1);\n            }\n        });\n        return \"\".concat(baseName, \" Session \").concat(maxNumber + 1);\n    }\n    async initializeBackend() {\n        // Always load from localStorage first for immediate access\n        this.loadSessionsFromStorage();\n        console.log('📂 Loaded sessions from localStorage for immediate access');\n        try {\n            // Try to establish backend connection\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 3000); // 3 second timeout\n            const response = await fetch('http://localhost:5000/health/', {\n                method: 'GET',\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            if (response.status < 500) {\n                this.useBackend = true;\n                this.backendInitialized = true;\n                console.log('✅ Session Manager: Backend connection established');\n                // Sync with backend - merge backend sessions with localStorage\n                await this.syncWithBackend();\n            } else {\n                throw new Error('Backend returned server error');\n            }\n        } catch (error) {\n            console.warn('⚠️ Session Manager: Backend unavailable, using localStorage only');\n            this.useBackend = false;\n            this.backendInitialized = false;\n        }\n        // Setup storage listener for cross-window sync\n        this.setupStorageListener();\n    }\n    async syncWithBackend() {\n        if (!this.useBackend || !this.backendInitialized) return;\n        try {\n            const token = localStorage.getItem('auth_token');\n            if (!token) {\n                console.warn('No auth token found, cannot sync with backend');\n                return;\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.getUserSessions();\n            const backendSessions = response.sessions;\n            // Create a map of backend sessions by ID\n            const backendSessionMap = new Map();\n            for (const backendSession of backendSessions){\n                backendSessionMap.set(backendSession.session_uuid, backendSession);\n            }\n            // Merge localStorage sessions with backend sessions\n            const mergedSessions = new Map(this.sessions);\n            // Add/update sessions from backend\n            for (const backendSession of backendSessions){\n                const session = {\n                    id: backendSession.session_uuid,\n                    name: backendSession.name,\n                    config: backendSession.config_snapshot,\n                    targetPriceRows: backendSession.target_price_rows || [],\n                    orderHistory: [],\n                    currentMarketPrice: backendSession.current_market_price || 0,\n                    crypto1Balance: backendSession.crypto1_balance || 10,\n                    crypto2Balance: backendSession.crypto2_balance || 100000,\n                    stablecoinBalance: backendSession.stablecoin_balance || 0,\n                    createdAt: new Date(backendSession.created_at).getTime(),\n                    lastModified: new Date(backendSession.last_modified).getTime(),\n                    isActive: backendSession.is_active || false,\n                    runtime: backendSession.runtime_seconds * 1000 || 0 // Convert to milliseconds\n                };\n                mergedSessions.set(session.id, session);\n                // Set current session if this one is active\n                if (session.isActive) {\n                    this.currentSessionId = session.id;\n                    const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                    localStorage.setItem(currentSessionKey, session.id);\n                }\n            }\n            // Upload localStorage-only sessions to backend\n            for (const [sessionId, localSession] of this.sessions){\n                if (!backendSessionMap.has(sessionId)) {\n                    console.log(\"\\uD83D\\uDCE4 Uploading localStorage session to backend: \".concat(localSession.name));\n                    try {\n                        await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession({\n                            name: localSession.name,\n                            config: localSession.config,\n                            targetPriceRows: localSession.targetPriceRows,\n                            currentMarketPrice: localSession.currentMarketPrice,\n                            crypto1Balance: localSession.crypto1Balance,\n                            crypto2Balance: localSession.crypto2Balance,\n                            stablecoinBalance: localSession.stablecoinBalance\n                        });\n                    } catch (error) {\n                        console.error(\"Failed to upload session \".concat(sessionId, \" to backend:\"), error);\n                    }\n                }\n            }\n            // Update local sessions map\n            this.sessions = mergedSessions;\n            console.log(\"\\uD83D\\uDD04 Synced \".concat(this.sessions.size, \" sessions between localStorage and backend\"));\n            // Save merged sessions back to localStorage\n            this.saveSessionsToStorage();\n        } catch (error) {\n            console.error('Failed to sync with backend:', error);\n        }\n    }\n    async loadSessionsFromBackend() {\n        if (!this.useBackend) return;\n        try {\n            const token = localStorage.getItem('auth_token');\n            if (!token) {\n                console.warn('No auth token found, cannot load sessions from backend');\n                this.loadSessionsFromStorage();\n                return;\n            }\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.getUserSessions();\n            const backendSessions = response.sessions;\n            // Convert backend sessions to frontend format\n            this.sessions.clear();\n            for (const backendSession of backendSessions){\n                const session = {\n                    id: backendSession.session_uuid,\n                    name: backendSession.name,\n                    config: backendSession.config_snapshot,\n                    targetPriceRows: backendSession.target_price_rows || [],\n                    orderHistory: [],\n                    currentMarketPrice: backendSession.current_market_price || 0,\n                    crypto1Balance: backendSession.crypto1_balance || 10,\n                    crypto2Balance: backendSession.crypto2_balance || 100000,\n                    stablecoinBalance: backendSession.stablecoin_balance || 0,\n                    createdAt: new Date(backendSession.created_at).getTime(),\n                    lastModified: new Date(backendSession.last_modified).getTime(),\n                    isActive: backendSession.is_active || false,\n                    runtime: backendSession.runtime_seconds * 1000 || 0 // Convert to milliseconds\n                };\n                this.sessions.set(session.id, session);\n                // Set current session if this one is active\n                if (session.isActive) {\n                    this.currentSessionId = session.id;\n                    const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                    localStorage.setItem(currentSessionKey, session.id);\n                }\n            }\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" sessions from backend\"));\n            // Also save to localStorage as backup\n            this.saveSessionsToStorage();\n        } catch (error) {\n            console.error('Failed to load sessions from backend:', error);\n            // Fallback to localStorage\n            this.loadSessionsFromStorage();\n        }\n    }\n    getWindowSpecificKey(baseKey) {\n        return \"\".concat(baseKey, \"_\").concat(this.windowId);\n    }\n    setupStorageListener() {\n        if (false) {}\n        // Listen for storage changes from other windows\n        window.addEventListener('storage', (event)=>{\n            if (event.key === SESSIONS_STORAGE_KEY && event.newValue) {\n                try {\n                    // Reload sessions when they change in another window\n                    const parsedSessions = JSON.parse(event.newValue);\n                    this.sessions = new Map(Object.entries(parsedSessions));\n                    console.log(\"\\uD83D\\uDD04 Sessions synced from another window (\".concat(this.sessions.size, \" sessions)\"));\n                } catch (error) {\n                    console.error('Failed to sync sessions from storage event:', error);\n                }\n            }\n        });\n    }\n    loadSessionsFromStorage() {\n        try {\n            if (false) {}\n            // Load sessions from shared storage (all windows see same sessions)\n            const sessionsData = localStorage.getItem(SESSIONS_STORAGE_KEY);\n            // Load current session from window-specific storage (each window has its own current session)\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            const currentSessionId = localStorage.getItem(currentSessionKey);\n            if (sessionsData) {\n                const parsedSessions = JSON.parse(sessionsData);\n                this.sessions = new Map(Object.entries(parsedSessions));\n            }\n            this.currentSessionId = currentSessionId;\n            console.log(\"\\uD83D\\uDCC2 Loaded \".concat(this.sessions.size, \" shared sessions for window \").concat(this.windowId));\n        } catch (error) {\n            console.error('Failed to load sessions from storage:', error);\n        }\n    }\n    saveSessionsToStorage() {\n        try {\n            if (false) {}\n            // Save sessions to shared storage (all windows see same sessions)\n            const sessionsObject = Object.fromEntries(this.sessions);\n            localStorage.setItem(SESSIONS_STORAGE_KEY, JSON.stringify(sessionsObject));\n            // Save current session to window-specific storage\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            if (this.currentSessionId) {\n                localStorage.setItem(currentSessionKey, this.currentSessionId);\n            }\n        } catch (error) {\n            console.error('Failed to save sessions to storage:', error);\n        }\n    }\n    async createNewSessionWithAutoName(config, customName) {\n        const sessionName = customName || this.generateSessionName(config);\n        return this.createNewSession(sessionName, config);\n    }\n    async createNewSession(name, config) {\n        if (this.useBackend && this.backendInitialized) {\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.createSession({\n                    name,\n                    config,\n                    targetPriceRows: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: 10,\n                    crypto2Balance: 100000,\n                    stablecoinBalance: 0\n                });\n                const sessionId = response.session.session_uuid;\n                // Add to local cache\n                const now = Date.now();\n                const newSession = {\n                    id: sessionId,\n                    name,\n                    config,\n                    targetPriceRows: [],\n                    orderHistory: [],\n                    currentMarketPrice: 0,\n                    crypto1Balance: 10,\n                    crypto2Balance: 100000,\n                    stablecoinBalance: 0,\n                    createdAt: now,\n                    lastModified: now,\n                    isActive: false,\n                    runtime: 0\n                };\n                this.sessions.set(sessionId, newSession);\n                this.saveSessionsToStorage(); // Backup to localStorage\n                console.log('✅ Session created on backend:', sessionId);\n                return sessionId;\n            } catch (error) {\n                console.error('❌ Failed to create session on backend, falling back to localStorage:', error);\n            // Don't disable backend completely, just fallback for this operation\n            }\n        }\n        // Fallback to localStorage\n        const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n        const now = Date.now();\n        const newSession = {\n            id: sessionId,\n            name,\n            config,\n            targetPriceRows: [],\n            orderHistory: [],\n            currentMarketPrice: 0,\n            crypto1Balance: 10,\n            crypto2Balance: 100000,\n            stablecoinBalance: 0,\n            createdAt: now,\n            lastModified: now,\n            isActive: false,\n            runtime: 0\n        };\n        this.sessions.set(sessionId, newSession);\n        this.saveSessionsToStorage();\n        console.log('📝 Session created in localStorage:', sessionId);\n        return sessionId;\n    }\n    saveSession(sessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance) {\n        let isActive = arguments.length > 8 && arguments[8] !== void 0 ? arguments[8] : false, overrideRuntime = arguments.length > 9 ? arguments[9] : void 0// Optional parameter to set specific runtime\n        ;\n        try {\n            const session = this.sessions.get(sessionId);\n            if (!session) {\n                console.error('Session not found:', sessionId);\n                return false;\n            }\n            // Update runtime - use override if provided, otherwise calculate normally\n            let currentRuntime;\n            if (overrideRuntime !== undefined) {\n                // Use the provided runtime (for saved sessions)\n                currentRuntime = overrideRuntime;\n                console.log(\"\\uD83D\\uDCCA Using override runtime: \".concat(currentRuntime, \"ms for session \").concat(sessionId));\n            } else {\n                // Calculate runtime normally for active sessions\n                currentRuntime = session.runtime;\n                const startTime = this.sessionStartTimes.get(sessionId);\n                if (startTime && isActive) {\n                    // Session is running, update runtime\n                    currentRuntime = session.runtime + (Date.now() - startTime);\n                    // Reset start time for next interval\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                } else if (!isActive && startTime) {\n                    // Session stopped, finalize runtime\n                    currentRuntime = session.runtime + (Date.now() - startTime);\n                    this.sessionStartTimes.delete(sessionId);\n                } else if (isActive && !startTime) {\n                    // Session just started, record start time\n                    this.sessionStartTimes.set(sessionId, Date.now());\n                }\n            }\n            const updatedSession = {\n                ...session,\n                config,\n                targetPriceRows: [\n                    ...targetPriceRows\n                ],\n                orderHistory: [\n                    ...orderHistory\n                ],\n                currentMarketPrice,\n                crypto1Balance,\n                crypto2Balance,\n                stablecoinBalance,\n                isActive,\n                lastModified: Date.now(),\n                runtime: currentRuntime\n            };\n            this.sessions.set(sessionId, updatedSession);\n            // Save to backend first, then localStorage as backup\n            if (this.useBackend && this.backendInitialized) {\n                this.saveSessionToBackend(sessionId, updatedSession).catch((error)=>{\n                    console.error('Failed to save session to backend:', error);\n                });\n            }\n            this.saveSessionsToStorage();\n            return true;\n        } catch (error) {\n            console.error('Failed to save session:', error);\n            return false;\n        }\n    }\n    async saveSessionToBackend(sessionId, session) {\n        if (!this.useBackend || !this.backendInitialized) return;\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_0__.sessionApi.updateSession(sessionId, {\n                name: session.name,\n                config: session.config,\n                targetPriceRows: session.targetPriceRows,\n                currentMarketPrice: session.currentMarketPrice,\n                crypto1Balance: session.crypto1Balance,\n                crypto2Balance: session.crypto2Balance,\n                stablecoinBalance: session.stablecoinBalance,\n                isActive: session.isActive,\n                additionalRuntime: Math.floor(session.runtime / 1000) // Convert to seconds\n            });\n            console.log(\"\\uD83D\\uDCBE Session \".concat(sessionId, \" saved to backend\"));\n        } catch (error) {\n            console.error(\"Failed to save session \".concat(sessionId, \" to backend:\"), error);\n            throw error;\n        }\n    }\n    loadSession(sessionId) {\n        return this.sessions.get(sessionId) || null;\n    }\n    deleteSession(sessionId) {\n        const deleted = this.sessions.delete(sessionId);\n        if (deleted) {\n            if (this.currentSessionId === sessionId) {\n                this.currentSessionId = null;\n                const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n                localStorage.removeItem(currentSessionKey);\n            }\n            this.saveSessionsToStorage();\n        }\n        return deleted;\n    }\n    getAllSessions() {\n        return Array.from(this.sessions.values()).map((session)=>({\n                id: session.id,\n                name: session.name,\n                pair: \"\".concat(session.config.crypto1, \"/\").concat(session.config.crypto2),\n                createdAt: session.createdAt,\n                lastModified: session.lastModified,\n                isActive: session.isActive,\n                runtime: this.getCurrentRuntime(session.id),\n                totalTrades: session.orderHistory.length,\n                totalProfitLoss: session.orderHistory.filter((trade)=>trade.orderType === 'SELL' && trade.realizedProfitLossCrypto2 !== undefined).reduce((sum, trade)=>sum + (trade.realizedProfitLossCrypto2 || 0), 0)\n            }));\n    }\n    setCurrentSession(sessionId) {\n        if (this.sessions.has(sessionId)) {\n            this.currentSessionId = sessionId;\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.setItem(currentSessionKey, sessionId);\n            // Mark this session as active when it becomes the current session\n            const session = this.sessions.get(sessionId);\n            if (session && !session.isActive) {\n                session.isActive = true;\n                session.lastModified = Date.now();\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"✅ Session \".concat(sessionId, \" marked as active for window \").concat(this.windowId));\n            }\n        }\n    }\n    getCurrentSessionId() {\n        return this.currentSessionId;\n    }\n    clearCurrentSession() {\n        // Mark current session as inactive before clearing\n        if (this.currentSessionId) {\n            const session = this.sessions.get(this.currentSessionId);\n            if (session && session.isActive) {\n                session.isActive = false;\n                session.lastModified = Date.now();\n                this.sessions.set(this.currentSessionId, session);\n                this.saveSessionsToStorage();\n                console.log(\"⏹️ Session \".concat(this.currentSessionId, \" marked as inactive for window \").concat(this.windowId));\n            }\n        }\n        this.currentSessionId = null;\n        if (true) {\n            const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n            localStorage.removeItem(currentSessionKey);\n        }\n        console.log(\"\\uD83D\\uDDD1️ Cleared current session for window \".concat(this.windowId));\n    }\n    startSessionRuntime(sessionId) {\n        this.sessionStartTimes.set(sessionId, Date.now());\n    }\n    stopSessionRuntime(sessionId) {\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            const session = this.sessions.get(sessionId);\n            if (session) {\n                const additionalRuntime = Date.now() - startTime;\n                session.runtime += additionalRuntime;\n                session.lastModified = Date.now();\n                // Keep session active even when runtime stops - only deactivate on manual save or session clear\n                this.sessions.set(sessionId, session);\n                this.saveSessionsToStorage();\n            }\n            this.sessionStartTimes.delete(sessionId);\n        }\n    }\n    deactivateSession(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (session && session.isActive) {\n            session.isActive = false;\n            session.lastModified = Date.now();\n            this.sessions.set(sessionId, session);\n            this.saveSessionsToStorage();\n            console.log(\"⏹️ Session \".concat(sessionId, \" deactivated\"));\n        }\n    }\n    getCurrentRuntime(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return 0;\n        const startTime = this.sessionStartTimes.get(sessionId);\n        if (startTime) {\n            // Session is currently running, add current runtime to stored runtime\n            return session.runtime + (Date.now() - startTime);\n        }\n        // Session is not running, return stored runtime\n        return session.runtime;\n    }\n    exportSessionToJSON(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        return JSON.stringify(session, null, 2);\n    }\n    importSessionFromJSON(jsonData) {\n        try {\n            const sessionData = JSON.parse(jsonData);\n            const sessionId = (0,uuid__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n            const importedSession = {\n                ...sessionData,\n                id: sessionId,\n                isActive: false,\n                lastModified: Date.now()\n            };\n            this.sessions.set(sessionId, importedSession);\n            this.saveSessionsToStorage();\n            return sessionId;\n        } catch (error) {\n            console.error('Failed to import session:', error);\n            return null;\n        }\n    }\n    renameSession(sessionId, newName) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return false;\n        session.name = newName;\n        session.lastModified = Date.now();\n        this.sessions.set(sessionId, session);\n        this.saveSessionsToStorage();\n        return true;\n    }\n    getSessionHistory(sessionId) {\n        const session = this.sessions.get(sessionId);\n        return session ? [\n            ...session.orderHistory\n        ] : [];\n    }\n    exportSessionToCSV(sessionId) {\n        const session = this.sessions.get(sessionId);\n        if (!session) return null;\n        const headers = [\n            'Date',\n            'Time',\n            'Pair',\n            'Crypto',\n            'Order Type',\n            'Amount',\n            'Avg Price',\n            'Value',\n            'Price 1',\n            'Crypto 1',\n            'Price 2',\n            'Crypto 2',\n            'Profit/Loss (Crypto1)',\n            'Profit/Loss (Crypto2)'\n        ];\n        const csvContent = [\n            headers.join(','),\n            ...session.orderHistory.map((entry)=>{\n                var _entry_amountCrypto1, _entry_avgPrice, _entry_valueCrypto2, _entry_price1, _entry_price2, _entry_realizedProfitLossCrypto1, _entry_realizedProfitLossCrypto2;\n                return [\n                    new Date(entry.timestamp).toISOString().split('T')[0],\n                    new Date(entry.timestamp).toTimeString().split(' ')[0],\n                    entry.pair,\n                    entry.crypto1Symbol,\n                    entry.orderType,\n                    ((_entry_amountCrypto1 = entry.amountCrypto1) === null || _entry_amountCrypto1 === void 0 ? void 0 : _entry_amountCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_avgPrice = entry.avgPrice) === null || _entry_avgPrice === void 0 ? void 0 : _entry_avgPrice.toFixed(session.config.numDigits)) || '',\n                    ((_entry_valueCrypto2 = entry.valueCrypto2) === null || _entry_valueCrypto2 === void 0 ? void 0 : _entry_valueCrypto2.toFixed(session.config.numDigits)) || '',\n                    ((_entry_price1 = entry.price1) === null || _entry_price1 === void 0 ? void 0 : _entry_price1.toFixed(session.config.numDigits)) || '',\n                    entry.crypto1Symbol,\n                    ((_entry_price2 = entry.price2) === null || _entry_price2 === void 0 ? void 0 : _entry_price2.toFixed(session.config.numDigits)) || '',\n                    entry.crypto2Symbol,\n                    ((_entry_realizedProfitLossCrypto1 = entry.realizedProfitLossCrypto1) === null || _entry_realizedProfitLossCrypto1 === void 0 ? void 0 : _entry_realizedProfitLossCrypto1.toFixed(session.config.numDigits)) || '',\n                    ((_entry_realizedProfitLossCrypto2 = entry.realizedProfitLossCrypto2) === null || _entry_realizedProfitLossCrypto2 === void 0 ? void 0 : _entry_realizedProfitLossCrypto2.toFixed(session.config.numDigits)) || ''\n                ].join(',');\n            })\n        ].join('\\n');\n        return csvContent;\n    }\n    clearAllSessions() {\n        this.sessions.clear();\n        this.currentSessionId = null;\n        // Clear shared sessions storage\n        localStorage.removeItem(SESSIONS_STORAGE_KEY);\n        // Clear window-specific current session\n        const currentSessionKey = this.getWindowSpecificKey(CURRENT_SESSION_KEY);\n        localStorage.removeItem(currentSessionKey);\n    }\n    // Auto-save functionality\n    enableAutoSave(sessionId, getSessionData) {\n        let intervalMs = arguments.length > 2 && arguments[2] !== void 0 // 30 seconds\n         ? arguments[2] : 30000;\n        const interval = setInterval(()=>{\n            const data = getSessionData();\n            this.saveSession(sessionId, data.config, data.targetPriceRows, data.orderHistory, data.currentMarketPrice, data.crypto1Balance, data.crypto2Balance, data.stablecoinBalance, data.isActive);\n        }, intervalMs);\n        return ()=>clearInterval(interval);\n    }\n    constructor(){\n        this.sessions = new Map();\n        this.currentSessionId = null;\n        this.useBackend = true // Flag to determine if we should use backend API\n        ;\n        this.sessionStartTimes = new Map() // Track when sessions started running\n        ;\n        this.backendInitialized = false;\n        this.windowId = getWindowId();\n        console.log(\"\\uD83E\\uDE9F SessionManager initialized for window: \".concat(this.windowId));\n        // Clear any stale session start times on initialization\n        this.sessionStartTimes.clear();\n        // Initialize backend connection first, then load sessions\n        this.initializeBackend();\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/session-manager.ts\n"));

/***/ })

});