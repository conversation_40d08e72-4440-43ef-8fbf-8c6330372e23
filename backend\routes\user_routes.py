from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from db import db
from models.user_model import User
from models.api_key_model import ExchangeApiKey
import logging
import ccxt

logger = logging.getLogger(__name__)
user_bp = Blueprint('user', __name__, url_prefix='/user')

@user_bp.route('/apikeys', methods=['GET'])
@jwt_required()
def get_api_keys():
    """Get a list of user's API keys."""
    current_user_id = get_jwt_identity()
    
    # Get all api keys for the current user
    api_keys = ExchangeApiKey.query.filter_by(user_id=current_user_id).all()
    
    return jsonify({
        "api_keys": [key.to_dict(mask_secrets=True) for key in api_keys]
    }), 200


@user_bp.route('/apikeys', methods=['POST'])
@jwt_required()
def add_api_key():
    """Add a new API key for the user."""
    if not request.is_json:
        return jsonify({"error": "Missing JSON in request"}), 400
    
    current_user_id = get_jwt_identity()
    data = request.get_json()
    
    exchange_name = data.get('exchange_name', 'binance').lower()
    api_key = data.get('api_key')
    secret_key = data.get('secret_key')
    label = data.get('label', f"{exchange_name} API Key")
    
    if not api_key or not secret_key:
        return jsonify({"error": "API key and secret key are required"}), 400
    
    # Verify the API key with the exchange (optional but recommended)
    try:
        if exchange_name == 'binance':
            # Create a Binance instance with the provided credentials
            # Use testnet for validation if configured
            exchange_class = getattr(ccxt, exchange_name)
            exchange = exchange_class({
                'apiKey': api_key,
                'secret': secret_key,
                'enableRateLimit': True,
                'options': {'defaultType': 'spot'}
            })
            
            # Try to fetch account balance as a validation
            exchange.fetch_balance()
            
            # If we got here, the credentials are valid
            logger.info(f"Successfully validated {exchange_name} API key.")
        else:
            return jsonify({"error": f"Unsupported exchange: {exchange_name}"}), 400
    except Exception as e:
        logger.error(f"API key validation failed: {str(e)}")
        return jsonify({
            "error": "API key validation failed",
            "details": str(e)
        }), 400
    
    # Create and save the API key
    new_key = ExchangeApiKey(
        user_id=current_user_id,
        exchange_name=exchange_name,
        api_key=api_key,
        secret_key=secret_key,
        label=label
    )
    
    try:
        db.session.add(new_key)
        db.session.commit()
        
        return jsonify({
            "message": "API key added successfully",
            "api_key": new_key.to_dict(mask_secrets=True)
        }), 201
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error adding API key: {str(e)}")
        return jsonify({"error": "Failed to add API key"}), 500


@user_bp.route('/apikeys/<int:key_id>', methods=['PUT'])
@jwt_required()
def update_api_key(key_id):
    """Update an existing API key."""
    if not request.is_json:
        return jsonify({"error": "Missing JSON in request"}), 400
    
    current_user_id = get_jwt_identity()
    data = request.get_json()
    
    # Find the API key
    api_key = ExchangeApiKey.query.filter_by(id=key_id, user_id=current_user_id).first()
    
    if not api_key:
        return jsonify({"error": "API key not found"}), 404
    
    # Update the fields
    if 'label' in data:
        api_key.label = data['label']
    
    if 'api_key' in data:
        api_key.api_key = data['api_key']
    
    if 'secret_key' in data:
        api_key.secret_key = data['secret_key']
    
    if 'is_active' in data:
        api_key.is_active = data['is_active']
    
    try:
        db.session.commit()
        return jsonify({
            "message": "API key updated successfully",
            "api_key": api_key.to_dict(mask_secrets=True)
        }), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating API key: {str(e)}")
        return jsonify({"error": "Failed to update API key"}), 500


@user_bp.route('/apikeys/<int:key_id>', methods=['DELETE'])
@jwt_required()
def delete_api_key(key_id):
    """Delete an API key."""
    current_user_id = get_jwt_identity()
    
    # Find the API key
    api_key = ExchangeApiKey.query.filter_by(id=key_id, user_id=current_user_id).first()
    
    if not api_key:
        return jsonify({"error": "API key not found"}), 404
    
    try:
        db.session.delete(api_key)
        db.session.commit()
        return jsonify({"message": "API key deleted successfully"}), 200
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting API key: {str(e)}")
        return jsonify({"error": "Failed to delete API key"}), 500 