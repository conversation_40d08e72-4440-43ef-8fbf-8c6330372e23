from db import db
from datetime import datetime
from sqlalchemy.orm import relationship

class BotStatusLog(db.Model):
    """Model to log bot status and error messages."""
    __tablename__ = 'bot_status_logs'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    config_id = db.Column(db.Integer, db.ForeignKey('trading_configurations.id'), nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    log_level = db.Column(db.String(10), nullable=False, default='INFO')  # 'INFO', 'WARNING', 'ERROR'
    message = db.Column(db.Text, nullable=False)
    details = db.Column(db.Text, nullable=True)  # Additional details, can be JSON
    
    # Relationships
    user = relationship("User")
    trading_config = relationship("TradingConfiguration", back_populates="status_logs")
    
    def __init__(self, user_id, config_id, message, log_level='INFO', details=None):
        self.user_id = user_id
        self.config_id = config_id
        self.message = message
        self.log_level = log_level
        self.details = details
    
    def to_dict(self):
        """Convert model to dictionary for API responses."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'config_id': self.config_id,
            'timestamp': self.timestamp.isoformat(),
            'log_level': self.log_level,
            'message': self.message,
            'details': self.details
        } 