
import React from 'react';
import { Rocket } from 'lucide-react'; 

interface LogoProps {
  className?: string;
  useFullName?: boolean; // Added prop to control text
}

const Logo: React.FC<LogoProps> = ({ className, useFullName = true }) => {
  return (
    <div className={`flex items-center text-2xl font-bold text-primary ${className}`}>
      <Rocket className="mr-2 h-7 w-7" />
      <span>Pluto{useFullName ? " Trading Bot" : ""}</span>
    </div>
  );
};

export default Logo;
