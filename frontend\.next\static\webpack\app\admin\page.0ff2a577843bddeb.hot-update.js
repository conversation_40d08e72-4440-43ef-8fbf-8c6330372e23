"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_modals_SessionAlarmModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/SessionAlarmModal */ \"(app-pages-browser)/./src/components/modals/SessionAlarmModal.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    _s();\n    const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSessionId, setEditingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingName, setEditingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentRuntime, setCurrentRuntime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [alarmModalOpen, setAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAlarmSession, setSelectedAlarmSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            loadSessions();\n            const sessionId = sessionManager.getCurrentSessionId();\n            setCurrentSessionId(sessionId);\n            // Initialize current runtime\n            if (sessionId) {\n                const runtime = sessionManager.getCurrentRuntime(sessionId);\n                setCurrentRuntime(runtime);\n            }\n            // Listen for storage changes to sync sessions across windows\n            const handleStorageChange = {\n                \"SessionManager.useEffect.handleStorageChange\": (event)=>{\n                    if (event.key === 'trading_sessions' && event.newValue) {\n                        loadSessions();\n                        console.log('🔄 Sessions synced from another window');\n                    }\n                }\n            }[\"SessionManager.useEffect.handleStorageChange\"];\n            window.addEventListener('storage', handleStorageChange);\n            return ({\n                \"SessionManager.useEffect\": ()=>{\n                    window.removeEventListener('storage', handleStorageChange);\n                }\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], []);\n    // Update runtime display every second for active sessions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            const interval = setInterval({\n                \"SessionManager.useEffect.interval\": ()=>{\n                    if (currentSessionId) {\n                        const runtime = sessionManager.getCurrentRuntime(currentSessionId);\n                        setCurrentRuntime(runtime);\n                        console.log(\"⏱️ Runtime update: \".concat(formatRuntime(runtime), \" for session \").concat(currentSessionId));\n                    }\n                }\n            }[\"SessionManager.useEffect.interval\"], 5000); // Update every 5 seconds instead of 1 second\n            return ({\n                \"SessionManager.useEffect\": ()=>clearInterval(interval)\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], [\n        currentSessionId,\n        sessionManager\n    ]);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        setSessions(allSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const handleSaveCurrentSession = async ()=>{\n        if (!currentSessionId) {\n            toast({\n                title: \"Error\",\n                description: \"No active session to save\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            // Check if there's already a saved version of this session\n            const currentSession = sessionManager.loadSession(currentSessionId);\n            if (!currentSession) {\n                toast({\n                    title: \"Error\",\n                    description: \"Current session not found\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Look for existing saved session with same base name (only manually saved ones)\n            const allSessions = sessionManager.getAllSessions();\n            const baseName = currentSession.name.replace(/ \\((Saved|AutoSaved).*\\)$/, ''); // Remove existing timestamp\n            const existingSavedSession = allSessions.find((s)=>s.id !== currentSessionId && s.name.startsWith(baseName) && s.name.includes('(Saved') && // Only look for manually saved sessions\n                !s.isActive // Only look in inactive sessions\n            );\n            let targetSessionId;\n            let savedName;\n            if (existingSavedSession) {\n                // Update existing saved session - update the timestamp to show latest save\n                targetSessionId = existingSavedSession.id;\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                console.log(\"\\uD83D\\uDCDD Updating existing saved session: \".concat(savedName));\n            } else {\n                // Create new saved session with timestamp\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                targetSessionId = await sessionManager.createNewSession(savedName, config);\n                console.log(\"\\uD83D\\uDCBE Creating new saved session: \".concat(savedName));\n            }\n            // Get current runtime from the active session\n            const currentRuntime = sessionManager.getCurrentRuntime(currentSessionId);\n            // Update the session name if it's an existing session\n            if (existingSavedSession) {\n                sessionManager.renameSession(targetSessionId, savedName);\n            }\n            // Save/update the session with current data and runtime\n            const success = sessionManager.saveSession(targetSessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, false, currentRuntime // Pass the current runtime to the saved session\n            );\n            // DO NOT deactivate the current session - keep it running!\n            // The current session should remain active for continued trading\n            if (success) {\n                loadSessions();\n                toast({\n                    title: \"Session Saved\",\n                    description: existingSavedSession ? \"Save checkpoint updated (Runtime: \".concat(formatRuntime(currentRuntime), \")\") : \"Session saved as checkpoint (Runtime: \".concat(formatRuntime(currentRuntime), \")\")\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to save session\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error saving session:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save session\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleLoadSession = (sessionId)=>{\n        const session = sessionManager.loadSession(sessionId);\n        if (!session) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to load session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Load session data into context\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: session.config\n        });\n        dispatch({\n            type: 'SET_TARGET_PRICE_ROWS',\n            payload: session.targetPriceRows\n        });\n        dispatch({\n            type: 'CLEAR_ORDER_HISTORY'\n        });\n        session.orderHistory.forEach((entry)=>{\n            dispatch({\n                type: 'ADD_ORDER_HISTORY_ENTRY',\n                payload: entry\n            });\n        });\n        dispatch({\n            type: 'SET_MARKET_PRICE',\n            payload: session.currentMarketPrice\n        });\n        dispatch({\n            type: 'SET_BALANCES',\n            payload: {\n                crypto1: session.crypto1Balance,\n                crypto2: session.crypto2Balance\n            }\n        });\n        sessionManager.setCurrentSession(sessionId);\n        setCurrentSessionId(sessionId);\n        loadSessions();\n        toast({\n            title: \"Session Loaded\",\n            description: 'Session \"'.concat(session.name, '\" has been loaded')\n        });\n    };\n    const handleDeleteSession = (sessionId)=>{\n        const success = sessionManager.deleteSession(sessionId);\n        if (success) {\n            if (currentSessionId === sessionId) {\n                setCurrentSessionId(null);\n            }\n            loadSessions();\n            toast({\n                title: \"Session Deleted\",\n                description: \"Session has been deleted successfully\"\n            });\n        }\n    };\n    const handleRenameSession = (sessionId)=>{\n        if (!editingName.trim()) return;\n        const success = sessionManager.renameSession(sessionId, editingName.trim());\n        if (success) {\n            setEditingSessionId(null);\n            setEditingName('');\n            loadSessions();\n            toast({\n                title: \"Session Renamed\",\n                description: \"Session has been renamed successfully\"\n            });\n        }\n    };\n    const handleExportSession = (sessionId)=>{\n        const csvContent = sessionManager.exportSessionToCSV(sessionId);\n        if (!csvContent) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to export session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const session = sessionManager.loadSession(sessionId);\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat((session === null || session === void 0 ? void 0 : session.name) || 'session', \"_\").concat(new Date().toISOString().split('T')[0], \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        toast({\n            title: \"Export Complete\",\n            description: \"Session data has been exported to CSV\"\n        });\n    };\n    const handleOpenAlarmModal = (sessionId, sessionName)=>{\n        setSelectedAlarmSession({\n            id: sessionId,\n            name: sessionName\n        });\n        setAlarmModalOpen(true);\n    };\n    const handleCloseAlarmModal = ()=>{\n        setAlarmModalOpen(false);\n        setSelectedAlarmSession(null);\n    };\n    const formatRuntime = (runtime)=>{\n        if (!runtime || runtime < 0) return '0s';\n        const totalSeconds = Math.floor(runtime / 1000);\n        const hours = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        if (hours > 0) {\n            return \"\".concat(hours, \"h \").concat(minutes, \"m \").concat(seconds, \"s\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"m \").concat(seconds, \"s\");\n        } else {\n            return \"\".concat(seconds, \"s\");\n        }\n    };\n    const getCurrentSession = ()=>{\n        return sessions.find((s)=>s.id === currentSessionId);\n    };\n    const getActiveSessions = ()=>{\n        // Only show sessions that are actually running (bot is active and it's the current session)\n        return sessions.filter((s)=>s.isActive && s.id === currentSessionId && botSystemStatus === 'running');\n    };\n    const getInactiveSessions = ()=>{\n        // Show all other sessions (not currently running)\n        return sessions.filter((s)=>!(s.isActive && s.id === currentSessionId && botSystemStatus === 'running'));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 340,\n                                    columnNumber: 13\n                                }, this),\n                                \"Active Sessions\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 338,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: getActiveSessions().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Active Status\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Runtime\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: getActiveSessions().map((session, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: editingName,\n                                                                onChange: (e)=>setEditingName(e.target.value),\n                                                                onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                className: \"text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleRenameSession(session.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 369,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: session.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 374,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"ghost\",\n                                                                onClick: ()=>{\n                                                                    setEditingSessionId(session.id);\n                                                                    setEditingName(session.name);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"default\",\n                                                        children: \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: session.id === currentSessionId ? formatRuntime(currentRuntime) : formatRuntime(session.runtime)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: session.id === currentSessionId ? // Current session - show Save button (session is active/loaded)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: handleSaveCurrentSession,\n                                                                size: \"sm\",\n                                                                className: \"btn-neo\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"mr-2 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 404,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Save\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 407,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenAlarmModal(session.id, session.name),\n                                                                title: \"Configure Alarms\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 25\n                                                    }, this) : // Other sessions - show Load button (session is saved)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 423,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 426,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenAlarmModal(session.id, session.name),\n                                                                title: \"Configure Alarms\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, \"active-\".concat(session.id, \"-\").concat(index), true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 445,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No active session\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 446,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs\",\n                                    children: \"Start trading to create a session automatically\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 344,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 337,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Past Sessions (\",\n                                    getInactiveSessions().length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: [\n                                    \"Auto-saved: \",\n                                    getInactiveSessions().length,\n                                    \" | Manual: 0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                            className: \"h-[400px]\",\n                            children: getInactiveSessions().length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-muted-foreground py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No saved sessions yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: \"Save your current session to get started.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 472,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Session Name\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Active Status\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Total Runtime\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: getInactiveSessions().map((session, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    value: editingName,\n                                                                    onChange: (e)=>setEditingName(e.target.value),\n                                                                    onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                    className: \"text-sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 491,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleRenameSession(session.id),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 498,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 490,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: session.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>{\n                                                                        setEditingSessionId(session.id);\n                                                                        setEditingName(session.name);\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 512,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 504,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            children: \"Inactive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: formatRuntime(sessionManager.getCurrentRuntime(session.id))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 530,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 533,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 532,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenAlarmModal(session.id, session.name),\n                                                                title: \"Configure Alarms\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 541,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteSession(session.id),\n                                                                title: \"Delete Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, \"inactive-\".concat(session.id, \"-\").concat(index), true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 466,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 456,\n                columnNumber: 7\n            }, this),\n            selectedAlarmSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_SessionAlarmModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: alarmModalOpen,\n                onClose: handleCloseAlarmModal,\n                sessionId: selectedAlarmSession.id,\n                sessionName: selectedAlarmSession.name\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 558,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n        lineNumber: 335,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"lTtemk6bI4J/UVkffUm/7GBSG0Q=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SessionManager.tsx\n"));

/***/ })

});