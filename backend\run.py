import os
import sys
# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from app import socketio, create_app
from dotenv import load_dotenv
import threading
import logging # Added for logger fallback
from models.bot_state_model import BotState
from trading.trading_engine import TradingEngine
# from db import db # db access is through app_context

# Load environment variables
load_dotenv()

app = create_app()

def restart_active_bots(flask_app):
    """Query for active bots and restart their trading loops."""
    logger_to_use = flask_app.logger if hasattr(flask_app, 'logger') and len(flask_app.logger.handlers) > 0 else logging.getLogger(__name__)
    try:
        with flask_app.app_context():
            # BACKWARD COMPATIBLE QUERY: Only select essential columns to avoid missing column errors
            try:
                # Try the new way first (with all columns)
                active_bot_states = BotState.query.filter_by(is_running=True).all()
            except Exception as e_query:
                logger_to_use.warning(f"Failed to query with new columns (expected on first run): {str(e_query)}")
                # Fallback: Use raw SQL to only get essential columns
                try:
                    from db import db
                    result = db.session.execute(db.text("""
                        SELECT id, config_id, user_id, is_running
                        FROM bot_states
                        WHERE is_running = 1
                    """))
                    active_bot_data = result.fetchall()
                    logger_to_use.info(f"Found {len(active_bot_data)} bot(s) marked as running using fallback query.")

                    # Restart bots using the essential data
                    for row in active_bot_data:
                        config_id, user_id = row[1], row[2]  # config_id, user_id
                        logger_to_use.info(f"Attempting to restart bot for config_id: {config_id} (user_id: {user_id})")
                        try:
                            thread = threading.Thread(
                                target=TradingEngine._run_bot_loop,
                                args=(config_id, user_id),
                                daemon=True
                            )
                            thread.start()
                            logger_to_use.info(f"Successfully started thread for bot config_id: {config_id}")
                        except Exception as e_thread_start:
                            logger_to_use.error(f"Failed to start thread for bot config_id {config_id}: {str(e_thread_start)}")
                    return
                except Exception as e_fallback:
                    logger_to_use.error(f"Fallback query also failed: {str(e_fallback)}")
                    logger_to_use.info("Skipping bot auto-restart due to database compatibility issues.")
                    return

            # Normal path when new columns exist
            if active_bot_states:
                logger_to_use.info(f"Found {len(active_bot_states)} bot(s) marked as running. Attempting to restart.")
                for bot_state in active_bot_states:
                    logger_to_use.info(f"Attempting to restart bot for config_id: {bot_state.config_id} (user_id: {bot_state.user_id})")
                    try:
                        thread = threading.Thread(
                            target=TradingEngine._run_bot_loop,
                            args=(bot_state.config_id, bot_state.user_id),
                            daemon=True
                        )
                        thread.start()
                        logger_to_use.info(f"Successfully started thread for bot config_id: {bot_state.config_id}")
                    except Exception as e_thread_start:
                        logger_to_use.error(f"Failed to start thread for bot config_id {bot_state.config_id}: {str(e_thread_start)}")
            else:
                logger_to_use.info("No active bots found to restart on startup.")
    except Exception as e_restart_outer:
        logger_to_use.error(f"Outer error during bot auto-restart sequence: {str(e_restart_outer)}", exc_info=True)


if __name__ == '__main__':
    # Determine if this is the main Werkzeug process or a reloader child process
    # Auto-restart bots only in the main process if the reloader is active to avoid double execution.
    run_main_process = os.environ.get('WERKZEUG_RUN_MAIN') == 'true' or not os.getenv('FLASK_DEBUG', 'False').lower() == 'true'

    if run_main_process:
        try:
            restart_active_bots(app)
        except Exception as e_main_restart_call:
            # Fallback print if logger is not available or fails
            print(f"Critical error calling bot auto-restart sequence: {str(e_main_restart_call)}")
            if hasattr(app, 'logger') and len(app.logger.handlers) > 0:
                app.logger.critical(f"Critical error calling bot auto-restart sequence: {str(e_main_restart_call)}", exc_info=True)
    else:
        if hasattr(app, 'logger') and len(app.logger.handlers) > 0:
            app.logger.info("Skipping bot auto-restart in Werkzeug reloader child process.")
        else:
            print("Skipping bot auto-restart in Werkzeug reloader child process.")


    debug_mode = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    port = int(os.getenv('FLASK_PORT', '5000'))

    logger_to_use_main = app.logger if hasattr(app, 'logger') and len(app.logger.handlers) > 0 else logging.getLogger(__name__)

    logger_to_use_main.info(f"Starting Pluto Trading Bot backend on port {port}")
    logger_to_use_main.info(f"Debug mode: {'ON' if debug_mode else 'OFF'}")

    # When using Flask reloader (debug=True by default enables it),
    # use_reloader=False can prevent the app from starting twice, which is good for threads.
    # However, you lose auto-reloading on code changes. Typical setup is to let reloader manage it.
    # The check for WERKZEUG_RUN_MAIN='true' for restart_active_bots handles the double execution concern for that function.
    socketio.run(app, host="0.0.0.0", port=port, debug=debug_mode, allow_unsafe_werkzeug=True)