import logging
import uuid
from datetime import datetime
import time
import threading
import ccxt
from db import db
from models.trading_config_model import TradingConfiguration
from models.bot_state_model import BotState
from models.trade_model import Trade
from models.bot_status_model import BotStatusLog
from models.api_key_model import ExchangeApiKey
from models.trading_session_model import TradingSession
from services.exchange_service import ExchangeService
# Import DecryptionError (assuming it's in backend.auth.crypto)
from auth.crypto import DecryptionError

logger = logging.getLogger(__name__)

class TradingEngine:
    """Core engine for executing trading strategies."""

    @staticmethod
    def format_symbol(crypto1, crypto2):
        """Format a trading pair symbol for the exchange."""
        return f"{crypto1}/{crypto2}"

    @staticmethod
    def log_bot_status(user_id, config_id, message, log_level='INFO', details=None):
        """Log a bot status message to the database."""
        try:
            log_entry = BotStatusLog(
                user_id=user_id,
                config_id=config_id,
                message=message,
                log_level=log_level,
                details=details
            )
            db.session.add(log_entry)
            db.session.commit()

            if log_level == 'ERROR':
                logger.error(f"Bot {config_id}: {message}")
            else:
                logger.info(f"Bot {config_id}: {message}")
        except Exception as e:
            logger.error(f"Error logging bot status: {str(e)}")
            db.session.rollback()

    @staticmethod
    def get_active_session_id(user_id):
        """Get the active session ID for a user, if any."""
        try:
            active_session = TradingSession.get_active_session(user_id)
            return active_session.id if active_session else None
        except Exception as e:
            logger.warning(f"Could not get active session for user {user_id}: {str(e)}")
            return None

    @staticmethod
    def get_api_key_for_user(user_id, exchange_name='binance'):
        """Get active API key for a user. Handles potential DecryptionError."""
        try:
            api_key_record = ExchangeApiKey.query.filter_by(
                user_id=user_id,
                exchange_name=exchange_name,
                is_active=True
            ).first()

            if not api_key_record:
                # Log this as an error because the calling code expects a key or an exception
                logger.error(f"No active {exchange_name} API key found for user {user_id}")
                raise ValueError(f"No active {exchange_name} API key found for user {user_id}")

            # Access decrypted keys. This is where DecryptionError might occur.
            # We need to return a structure that ExchangeService expects, often including 'apiKey' and 'secret' fields.
            # The ExchangeApiKey model itself now has .api_key and .secret_key properties for decrypted values.
            # We return the model instance; ExchangeService will use these properties.

            # Trigger decryption by accessing the properties to ensure it works before returning.
            # If it fails, DecryptionError will be raised by the property access.
            _ = api_key_record.api_key
            _ = api_key_record.secret_key

            return api_key_record # Return the model instance

        except DecryptionError as e:
            logger.critical(f"Decryption failed for API key for user {user_id}, exchange {exchange_name}: {str(e)}. This API key is unusable.")
            # Depending on desired behavior, you might re-raise a more specific error
            # or an error that the calling code (e.g., start_bot) is expected to handle to stop the bot.
            raise ValueError(f"Failed to decrypt API key for user {user_id}. Key is unusable. Error: {str(e)}")
        except Exception as e:
            logger.error(f"Error getting API key for user {user_id}: {str(e)}")
            raise # Re-raise other unexpected errors

    @staticmethod
    def get_or_create_bot_state(config_id, user_id):
        """Get or create a bot state for a trading configuration."""
        try:
            bot_state = BotState.query.filter_by(config_id=config_id).first()

            if not bot_state:
                bot_state = BotState(config_id=config_id, user_id=user_id)
                db.session.add(bot_state)
                db.session.commit()

            return bot_state
        except Exception as e:
            logger.error(f"Error getting/creating bot state: {str(e)}")
            db.session.rollback()
            raise

    @staticmethod
    def start_bot(config_id, user_id):
        """Start a trading bot for the given configuration."""
        try:
            config = TradingConfiguration.query.get(config_id)
            if not config:
                raise ValueError(f"Trading configuration {config_id} not found")

            if config.user_id != user_id:
                raise ValueError("You don't have permission to start this bot")

            # Check if there are target prices configured
            target_prices = config.get_target_prices()
            if not target_prices or len(target_prices) == 0:
                raise ValueError("No target prices configured for this bot")

            # Get or create bot state
            bot_state = TradingEngine.get_or_create_bot_state(config_id, user_id)

            # Initialize target states if not already done
            if not bot_state.get_target_states():
                target_states_init = []
                # Sort prices ascending and assign counters per blueprint
                sorted_prices = sorted(config.get_target_prices())
                for index, price_val in enumerate(sorted_prices):
                    target_states_init.append({
                        'id': str(uuid.uuid4()),
                        'counter': index + 1,  # Counter starts from 1, lowest price gets counter 1
                        'targetPrice': price_val,
                        'status': 'Free',
                        'orderLevel': 0,
                        'valueLevel': config.base_bid,  # Use base_bid from config
                        'crypto1AmountHeld': None,
                        'originalCostCrypto2': None,
                        # Initialize all op_ fields to None
                        'op_operation_type': None, 'op_value_level_n_crypto2_committed': None,
                        'op_c1_to_sell_from_n_minus_1_committed': None,
                        'op_leg1_order_id': None, 'op_leg1_symbol': None, 'op_leg1_side': None,
                        'op_leg1_amount_sent': None, 'op_leg1_expected_receive_amount': None,
                        'op_leg1_actual_received_amount': None, 'op_leg1_avg_fill_price': None,
                        'op_leg1_fee': None, 'op_leg1_fee_currency': None,
                        'op_leg1_exchange_timestamp': None, 'op_leg1_completed_timestamp': None,
                        'op_leg2_order_id': None, 'op_leg2_symbol': None, 'op_leg2_side': None,
                        'op_leg2_amount_sent': None, 'op_leg2_expected_receive_amount': None,
                        'op_leg2_actual_received_amount': None, 'op_leg2_avg_fill_price': None,
                        'op_leg2_fee': None, 'op_leg2_fee_currency': None,
                        'op_leg2_exchange_timestamp': None, 'op_leg2_completed_timestamp': None,
                        'op_last_error': None, 'op_last_attempt_timestamp': None
                    })
                bot_state.set_target_states(target_states_init)

            # Check if the bot is already running
            if bot_state.is_running:
                return {"message": "Bot is already running"}

            # Mark the bot as active in both the state and the config
            bot_state.is_running = True
            config.is_active = True

            # Initialize bot state for immediate execution (no cooldowns)

            db.session.commit()

            TradingEngine.log_bot_status(
                user_id=user_id,
                config_id=config_id,
                message=f"Bot started for configuration {config.name}",
                log_level='INFO'
            )

            # Start the bot in a background thread
            thread = threading.Thread(
                target=TradingEngine._run_bot_loop,
                args=(config_id, user_id),
                daemon=True
            )
            thread.start()

            return {"message": f"Bot started for configuration {config.name}"}
        except Exception as e:
            logger.error(f"Error starting bot: {str(e)}")
            TradingEngine.log_bot_status(
                user_id=user_id,
                config_id=config_id,
                message=f"Failed to start bot: {str(e)}",
                log_level='ERROR'
            )
            raise

    @staticmethod
    def stop_bot(config_id, user_id):
        """Stop a running trading bot."""
        try:
            config = TradingConfiguration.query.get(config_id)
            if not config:
                raise ValueError(f"Trading configuration {config_id} not found")

            if config.user_id != user_id:
                raise ValueError("You don't have permission to stop this bot")

            bot_state = BotState.query.filter_by(config_id=config_id).first()

            if not bot_state or not bot_state.is_running:
                return {"message": "Bot is not running"}

            bot_state.is_running = False
            config.is_active = False
            db.session.commit()

            TradingEngine.log_bot_status(
                user_id=user_id,
                config_id=config_id,
                message=f"Bot stopped for configuration {config.name}",
                log_level='INFO'
            )

            return {"message": f"Bot stopped for configuration {config.name}"}
        except Exception as e:
            logger.error(f"Error stopping bot: {str(e)}")
            TradingEngine.log_bot_status(
                user_id=user_id,
                config_id=config_id,
                message=f"Error stopping bot: {str(e)}",
                log_level='ERROR'
            )
            raise

    @staticmethod
    def _run_bot_loop(config_id, user_id):
        """Run the trading bot loop in a background thread."""
        try:
            config = TradingConfiguration.query.get(config_id)
            bot_state = BotState.query.filter_by(config_id=config_id).first()

            if not config or not bot_state:
                logger.error(f"Config or bot state not found for config_id={config_id}")
                return

            api_key = TradingEngine.get_api_key_for_user(user_id)

            # Main bot loop
            while bot_state.is_running:
                try:
                    # Reload in case db changes
                    db.session.refresh(bot_state)
                    db.session.refresh(config)

                    if not bot_state.is_running:
                        break

                    # Update market price
                    symbol = TradingEngine.format_symbol(config.crypto1, config.crypto2)
                    current_price = None
                    try:
                        current_price = ExchangeService.get_ticker_price(symbol, None)
                        if current_price is None:
                            TradingEngine.log_bot_status(user_id=user_id, config_id=config_id, message=f"Failed to fetch market price for {symbol}. Bot loop will retry.", log_level='WARNING')
                            time.sleep(1)  # Reduced from 10s to 1s for immediate execution
                            continue
                        bot_state.current_market_price = current_price
                        bot_state.last_check = datetime.utcnow()
                        db.session.commit()
                    except (ccxt.RequestTimeout, ccxt.NetworkError, ccxt.ExchangeNotAvailable, ccxt.OnMaintenance) as e_transient:
                        TradingEngine.log_bot_status(user_id=user_id, config_id=config_id, message=f"Transient error fetching price for {symbol} in main loop: {str(e_transient)}. Bot will retry.", log_level='WARNING')
                        time.sleep(1)  # Reduced from 10s to 1s for immediate execution
                        continue
                    except ccxt.BadSymbol as e_bad_symbol:
                        TradingEngine.log_bot_status(user_id=user_id, config_id=config_id, message=f"Invalid symbol {symbol} for price fetching: {str(e_bad_symbol)}. Stopping bot.", log_level='ERROR')
                        bot_state.is_running = False
                        config.is_active = False
                        db.session.commit()
                        break
                    except ccxt.ExchangeError as e_exchange:
                        TradingEngine.log_bot_status(user_id=user_id, config_id=config_id, message=f"Exchange error fetching price for {symbol} in main loop: {str(e_exchange)}. Bot will retry.", log_level='ERROR')
                        time.sleep(1)  # Reduced from 10s to 1s for immediate execution
                        continue
                    except Exception as e_price_fetch:
                        TradingEngine.log_bot_status(user_id=user_id, config_id=config_id, message=f"Unexpected error fetching price for {symbol} in main loop: {str(e_price_fetch)}. Bot will retry.", log_level='ERROR')
                        time.sleep(1)  # Reduced from 10s to 1s for immediate execution
                        continue

                    action_taken = False
                    # Execute trading logic based on mode
                    if config.trading_mode == 'SimpleSpot':
                        action_taken = TradingEngine._execute_simple_spot_strategy(config, bot_state, api_key, current_price)
                    elif config.trading_mode == 'StablecoinSwap':
                        # Assuming _execute_stablecoin_swap_strategy will also return a boolean
                        action_taken = TradingEngine._execute_stablecoin_swap_strategy(config, bot_state, api_key, current_price)
                    else:
                        logger.warning(f"Unknown trading mode: {config.trading_mode}")

                    # Sleep interval based on whether an action was taken
                    if action_taken:
                        time.sleep(TradingEngine.ACTION_SLEEP_INTERVAL_S)  # Sleep after action taken
                    else:
                        time.sleep(TradingEngine.BOT_CYCLE_INTERVAL_S)  # Default sleep time if no action was taken

                except Exception as e:
                    logger.error(f"Error in bot loop: {str(e)}")
                    TradingEngine.log_bot_status(
                        user_id=user_id,
                        config_id=config_id,
                        message=f"Error in bot execution: {str(e)}",
                        log_level='ERROR'
                    )
                    time.sleep(TradingEngine.ERROR_SLEEP_INTERVAL_S)  # Wait longer after an error
        except Exception as e:
            logger.error(f"Fatal error in bot thread: {str(e)}")

    # IMMEDIATE EXECUTION CONFIGURATION - NO DELAYS
    BOT_CYCLE_INTERVAL_S = 0.1     # Very fast main bot loop interval
    ACTION_SLEEP_INTERVAL_S = 0.1  # Minimal sleep after action taken
    ERROR_SLEEP_INTERVAL_S = 1     # Minimal sleep after error for immediate execution

    @staticmethod
    def _execute_simple_spot_strategy(config, bot_state, api_key, current_price):
        """
        CONTINUOUS BUYING LOGIC:
        1. BUY ALL Free targets that are in slippage range (continuous buying)
        2. SELL N-1 target when any Full target is in slippage range
        3. NO action limits - buy as many as are in range
        """
        target_states = bot_state.get_target_states()
        current_time = time.time()

        # Sort targets from lowest to highest price
        sorted_states = sorted(target_states, key=lambda x: x['targetPrice'])

        # Track actions taken
        actions_taken = 0

        TradingEngine.log_bot_status(
            user_id=config.user_id,
            config_id=config.id,
            message=f"🚀 BACKEND CYCLE START: Checking {len(sorted_states)} targets at price ${current_price:.2f} | Slippage: ±{config.slippage_percent}%",
            log_level='INFO'
        )

        # Log all target states for debugging
        for i, target_state in enumerate(sorted_states):
            TradingEngine.log_bot_status(
                user_id=config.user_id,
                config_id=config.id,
                message=f"📋 Target {i+1}: ID={target_state.get('id', 'N/A')[:8]}... Price=${target_state['targetPrice']:,.0f} Status={target_state['status']}",
                log_level='DEBUG'
            )

        # CORRECTED LOGIC: Follow the exact blueprint
        for target_state in sorted_states:
            target_price = target_state['targetPrice']

            # STEP 1: Check if TargetRowN is triggered (within slippage range)
            price_diff_percent = abs(current_price - target_price) / current_price * 100

            TradingEngine.log_bot_status(
                user_id=config.user_id,
                config_id=config.id,
                message=f"📊 BACKEND SLIPPAGE CHECK: Counter {target_state.get('counter', 'N/A')} | Target ${target_price:,.0f} vs Current ${current_price:,.2f} | Diff: {price_diff_percent:.3f}% | Allowed: ±{config.slippage_percent}% | Status: {target_state['status']}",
                log_level='DEBUG'
            )

            # Check if percentage difference is within slippage tolerance
            if price_diff_percent <= config.slippage_percent:
                TradingEngine.log_bot_status(
                    user_id=config.user_id,
                    config_id=config.id,
                    message=f"🎯 BACKEND TRIGGER: Counter {target_state.get('counter', 'N/A')} (${target_price:,.0f}) is within slippage range!",
                    log_level='INFO'
                )

                # STEP 2: Evaluate Triggered TargetRowN's Status
                if target_state['status'] == 'Free':
                    # STEP 2a: Execute BUY on TargetRowN
                    TradingEngine.log_bot_status(
                        user_id=config.user_id,
                        config_id=config.id,
                        message=f"💰 BACKEND BUY: Executing buy for Counter {target_state.get('counter', 'N/A')} (${target_price:,.0f})",
                        log_level='INFO'
                    )

                    buy_success = TradingEngine._execute_buy_order(
                        config, bot_state, api_key, current_price, target_state, sorted_states, current_time
                    )
                    if buy_success:
                        actions_taken += 1
                        TradingEngine.log_bot_status(
                            user_id=config.user_id,
                            config_id=config.id,
                            message=f"✅ BACKEND BUY COMPLETED: Counter {target_state.get('counter', 'N/A')} (${target_price:,.0f}) - Action #{actions_taken}",
                            log_level='INFO'
                        )

                # STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY
                current_counter = target_state.get('counter', 0)
                target_n_minus_1 = None
                for ts in sorted_states:
                    if ts.get('counter', 0) == current_counter - 1:
                        target_n_minus_1 = ts
                        break

                if (target_n_minus_1 and target_n_minus_1['status'] == 'Full' and
                    target_n_minus_1.get('crypto1AmountHeld') and
                    target_n_minus_1.get('originalCostCrypto2')):

                    TradingEngine.log_bot_status(
                        user_id=config.user_id,
                        config_id=config.id,
                        message=f"🔄 BACKEND N-1 SELL: Counter {current_counter} triggered, selling from Counter {current_counter - 1}",
                        log_level='INFO'
                    )

                    # Execute SELL from N-1
                    sell_success = TradingEngine._execute_sell_order(
                        config, bot_state, api_key, current_price, target_n_minus_1, sorted_states, current_time
                    )
                    if sell_success:
                        actions_taken += 1
                        TradingEngine.log_bot_status(
                            user_id=config.user_id,
                            config_id=config.id,
                            message=f"✅ BACKEND SELL COMPLETED: Counter {current_counter - 1} sold - Action #{actions_taken}",
                            log_level='INFO'
                        )

        # Update bot state
        bot_state.set_target_states(sorted_states)
        db.session.commit()

        TradingEngine.log_bot_status(
            user_id=config.user_id,
            config_id=config.id,
            message=f"📊 CYCLE COMPLETE: {actions_taken} actions taken this cycle",
            log_level='INFO' if actions_taken > 0 else 'DEBUG'
        )

        return actions_taken > 0

    @staticmethod
    def _execute_stablecoin_swap_strategy(config, bot_state, api_key, current_price):
        """
        CONTINUOUS STABLECOIN SWAP LOGIC:
        1. BUY ALL Free targets that are in slippage range via stablecoin (continuous buying)
        2. SELL N-1 target when any Full target is in slippage range via stablecoin
        3. NO action limits - buy as many as are in range
        """
        target_states = bot_state.get_target_states()
        current_time = time.time()

        # Get preferred stablecoin from config or default to USDT
        preferred_stablecoin = config.preferred_stablecoin or 'USDT'

        # Sort targets from lowest to highest price
        sorted_states = sorted(target_states, key=lambda x: x['targetPrice'])

        # Track actions taken
        actions_taken = 0

        TradingEngine.log_bot_status(
            user_id=config.user_id,
            config_id=config.id,
            message=f"🔍 CONTINUOUS STABLECOIN SWAP: Checking {len(sorted_states)} targets at price {current_price:.{config.num_digits}f} via {preferred_stablecoin}",
            log_level='DEBUG'
        )

        # CORRECTED LOGIC: Follow the exact blueprint (same as Simple Spot)
        for target_state in sorted_states:
            target_price = target_state['targetPrice']

            # STEP 1: Check if TargetRowN is triggered (within slippage range)
            price_diff_percent = abs(current_price - target_price) / current_price * 100

            # Check if percentage difference is within slippage tolerance
            if price_diff_percent <= config.slippage_percent:
                TradingEngine.log_bot_status(
                    user_id=config.user_id,
                    config_id=config.id,
                    message=f"🎯 STABLECOIN TRIGGER: Counter {target_state.get('counter', 'N/A')} (${target_price:,.0f}) is within slippage range!",
                    log_level='INFO'
                )

                # STEP 2: Evaluate Triggered TargetRowN's Status
                if target_state['status'] == 'Free':
                    # STEP 2a: Execute BUY on TargetRowN
                    TradingEngine.log_bot_status(
                        user_id=config.user_id,
                        config_id=config.id,
                        message=f"💰 STABLECOIN BUY: Executing buy for Counter {target_state.get('counter', 'N/A')} (${target_price:,.0f})",
                        log_level='INFO'
                    )

                    # Execute TWO-STEP BUY immediately
                    buy_success = TradingEngine._execute_stablecoin_buy_order(
                        config, bot_state, api_key, current_price, target_state, sorted_states, current_time, preferred_stablecoin
                    )
                    if buy_success:
                        actions_taken += 1
                        TradingEngine.log_bot_status(
                            user_id=config.user_id,
                            config_id=config.id,
                            message=f"✅ STABLECOIN BUY COMPLETED: Counter {target_state.get('counter', 'N/A')} (${target_price:,.0f}) - Action #{actions_taken}",
                            log_level='INFO'
                        )

                # STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY
                current_counter = target_state.get('counter', 0)
                target_n_minus_1 = None
                for ts in sorted_states:
                    if ts.get('counter', 0) == current_counter - 1:
                        target_n_minus_1 = ts
                        break

                if (target_n_minus_1 and target_n_minus_1['status'] == 'Full' and
                    target_n_minus_1.get('crypto1AmountHeld') and
                    target_n_minus_1.get('originalCostCrypto2')):

                    TradingEngine.log_bot_status(
                        user_id=config.user_id,
                        config_id=config.id,
                        message=f"🔄 STABLECOIN N-1 SELL: Counter {current_counter} triggered, selling from Counter {current_counter - 1}",
                        log_level='INFO'
                    )

                    # Execute TWO-STEP SELL immediately
                    sell_success = TradingEngine._execute_stablecoin_sell_order(
                        config, bot_state, api_key, current_price, target_n_minus_1, sorted_states, current_time, preferred_stablecoin
                    )
                    if sell_success:
                        actions_taken += 1
                        TradingEngine.log_bot_status(
                            user_id=config.user_id,
                            config_id=config.id,
                            message=f"✅ STABLECOIN SELL COMPLETED: Counter {current_counter - 1} sold - Action #{actions_taken}",
                            log_level='INFO'
                        )

        # Update bot state
        bot_state.set_target_states(sorted_states)
        db.session.commit()

        TradingEngine.log_bot_status(
            user_id=config.user_id,
            config_id=config.id,
            message=f"📊 STABLECOIN CYCLE COMPLETE: {actions_taken} actions taken this cycle",
            log_level='DEBUG'
        )

        return actions_taken > 0

    @staticmethod
    def _execute_buy_order(config, bot_state, api_key, current_price, target_row, sorted_states, current_time):
        """
        Execute a BUY order for a specific target level.
        Returns True if order was successfully executed, False otherwise.
        """
        target_id = target_row['id']

        try:
            # Check balance before attempting buy
            balances = ExchangeService.get_account_balance(api_key)
            cost_crypto2 = target_row['valueLevel']

            if config.crypto2 not in balances or balances[config.crypto2] < cost_crypto2:
                TradingEngine.log_bot_status(
                    user_id=config.user_id,
                    config_id=config.id,
                    message=f"Insufficient {config.crypto2} balance for BUY at {target_row['targetPrice']}. Have {balances.get(config.crypto2, 0):.{config.num_digits}f}, need {cost_crypto2:.{config.num_digits}f}",
                    log_level='WARNING'
                )
                return False

            # Calculate amount to buy
            amount_crypto1_to_buy = cost_crypto2 / current_price
            symbol = TradingEngine.format_symbol(config.crypto1, config.crypto2)

            TradingEngine.log_bot_status(
                user_id=config.user_id,
                config_id=config.id,
                message=f"Executing BUY: {amount_crypto1_to_buy:.8f} {config.crypto1} at {current_price:.{config.num_digits}f} for target {target_row['targetPrice']}",
                log_level='INFO'
            )

            # Place the order
            order = ExchangeService.place_order(
                api_key=api_key,
                symbol=symbol,
                order_type='market',
                side='buy',
                amount=amount_crypto1_to_buy
            )

            # Update target state
            target_row['status'] = 'Full'
            target_row['orderLevel'] += 1
            target_row['valueLevel'] = config.base_bid * (config.multiplier ** target_row['orderLevel'])
            target_row['crypto1AmountHeld'] = amount_crypto1_to_buy
            target_row['originalCostCrypto2'] = cost_crypto2

            # Update bot state
            bot_state.set_target_states(sorted_states)
            db.session.commit()

            # Get active session ID for trade recording
            session_id = TradingEngine.get_active_session_id(config.user_id)

            # Record trade
            trade = Trade(
                user_id=config.user_id,
                config_id=config.id,
                session_id=session_id,
                pair=symbol,
                crypto1=config.crypto1,
                crypto2=config.crypto2,
                order_type='BUY',
                amount_crypto1=amount_crypto1_to_buy,
                avg_price=current_price,
                value_crypto2=cost_crypto2,
                exchange_order_id=str(order.get('id')),
                fees=order.get('fee', {}).get('cost'),
                target_price_id=target_id
            )
            db.session.add(trade)
            db.session.commit()

            TradingEngine.log_bot_status(
                user_id=config.user_id,
                config_id=config.id,
                message=f"✅ BUY SUCCESS: {amount_crypto1_to_buy:.8f} {config.crypto1} at {current_price:.{config.num_digits}f} for target {target_row['targetPrice']} (Level {target_row['orderLevel']})",
                log_level='INFO'
            )

            return True

        except Exception as e:
            logger.error(f"Error executing BUY for target {target_row['targetPrice']}: {str(e)}")
            TradingEngine.log_bot_status(
                user_id=config.user_id,
                config_id=config.id,
                message=f"❌ BUY FAILED for target {target_row['targetPrice']}: {str(e)}",
                log_level='ERROR'
            )
            return False

    @staticmethod
    def _execute_sell_order(config, bot_state, api_key, current_price, target_row, sorted_states, current_time):
        """
        Execute a SELL order for a specific target level.
        Returns True if order was successfully executed, False otherwise.
        """
        target_id = target_row['id']

        try:
            amount_crypto1_to_sell = target_row['crypto1AmountHeld']
            symbol = TradingEngine.format_symbol(config.crypto1, config.crypto2)

            TradingEngine.log_bot_status(
                user_id=config.user_id,
                config_id=config.id,
                message=f"Executing SELL: {amount_crypto1_to_sell:.8f} {config.crypto1} at {current_price:.{config.num_digits}f} from target {target_row['targetPrice']}",
                log_level='INFO'
            )

            # Place the order
            order = ExchangeService.place_order(
                api_key=api_key,
                symbol=symbol,
                order_type='market',
                side='sell',
                amount=amount_crypto1_to_sell
            )

            # Calculate profit
            crypto2_received = amount_crypto1_to_sell * current_price
            realized_profit = crypto2_received - target_row['originalCostCrypto2']

            # Update target state
            target_row['status'] = 'Free'
            # orderLevel remains unchanged on sell
            target_row['valueLevel'] = config.base_bid * (config.multiplier ** target_row['orderLevel'])
            target_row['crypto1AmountHeld'] = None
            target_row['originalCostCrypto2'] = None

            # Update bot state
            bot_state.set_target_states(sorted_states)
            db.session.commit()

            # Get active session ID for trade recording
            session_id = TradingEngine.get_active_session_id(config.user_id)

            # Record trade
            trade = Trade(
                user_id=config.user_id,
                config_id=config.id,
                session_id=session_id,
                pair=symbol,
                crypto1=config.crypto1,
                crypto2=config.crypto2,
                order_type='SELL',
                amount_crypto1=amount_crypto1_to_sell,
                avg_price=current_price,
                value_crypto2=crypto2_received,
                exchange_order_id=str(order.get('id')),
                fees=order.get('fee', {}).get('cost'),
                realized_pnl_crypto2=realized_profit,
                target_price_id=target_id
            )
            db.session.add(trade)
            db.session.commit()

            TradingEngine.log_bot_status(
                user_id=config.user_id,
                config_id=config.id,
                message=f"✅ SELL SUCCESS: {amount_crypto1_to_sell:.8f} {config.crypto1} at {current_price:.{config.num_digits}f} from target {target_row['targetPrice']}. Profit: {realized_profit:.{config.num_digits}f} {config.crypto2}",
                log_level='INFO'
            )

            return True

        except Exception as e:
            logger.error(f"Error executing SELL from target {target_row['targetPrice']}: {str(e)}")
            TradingEngine.log_bot_status(
                user_id=config.user_id,
                config_id=config.id,
                message=f"❌ SELL FAILED from target {target_row['targetPrice']}: {str(e)}",
                log_level='ERROR'
            )
            return False

    @staticmethod
    def _execute_stablecoin_buy_order(config, bot_state, api_key, current_price, target_row, sorted_states, current_time, preferred_stablecoin):
        """
        Execute a two-step BUY order for stablecoin swap mode.
        Step 1: Sell Crypto2 for PreferredStablecoin
        Step 2: Buy Crypto1 with PreferredStablecoin
        Returns True if both steps were successful, False otherwise.
        """
        target_id = target_row['id']

        try:
            cost_crypto2 = target_row['valueLevel']

            # Step 1: Convert Crypto2 to Stablecoin
            if config.crypto2 == preferred_stablecoin:
                # No conversion needed
                stablecoin_amount = cost_crypto2
                TradingEngine.log_bot_status(
                    user_id=config.user_id,
                    config_id=config.id,
                    message=f"Step 1: {config.crypto2} is already {preferred_stablecoin}. Amount: {stablecoin_amount:.8f}",
                    log_level='INFO'
                )
            else:
                # Need to sell Crypto2 for stablecoin
                balances = ExchangeService.get_account_balance(api_key)
                if config.crypto2 not in balances or balances[config.crypto2] < cost_crypto2:
                    TradingEngine.log_bot_status(
                        user_id=config.user_id,
                        config_id=config.id,
                        message=f"Insufficient {config.crypto2} balance for stablecoin swap BUY. Have {balances.get(config.crypto2, 0):.{config.num_digits}f}, need {cost_crypto2:.{config.num_digits}f}",
                        log_level='WARNING'
                    )
                    return False

                symbol_c2_stable = TradingEngine.format_symbol(config.crypto2, preferred_stablecoin)
                order_step1 = ExchangeService.place_order(
                    api_key=api_key,
                    symbol=symbol_c2_stable,
                    order_type='market',
                    side='sell',
                    amount=cost_crypto2
                )
                stablecoin_amount = float(order_step1.get('cost', 0))  # Amount of stablecoin received

                TradingEngine.log_bot_status(
                    user_id=config.user_id,
                    config_id=config.id,
                    message=f"Step 1: Sold {cost_crypto2:.8f} {config.crypto2} for {stablecoin_amount:.8f} {preferred_stablecoin}",
                    log_level='INFO'
                )

            # Step 2: Buy Crypto1 with Stablecoin
            if config.crypto1 == preferred_stablecoin:
                # Highly unlikely but handle it
                amount_crypto1_obtained = stablecoin_amount
                TradingEngine.log_bot_status(
                    user_id=config.user_id,
                    config_id=config.id,
                    message=f"Step 2: {config.crypto1} is {preferred_stablecoin}. Amount: {amount_crypto1_obtained:.8f}",
                    log_level='INFO'
                )
            else:
                symbol_c1_stable = TradingEngine.format_symbol(config.crypto1, preferred_stablecoin)
                # Get current price to calculate amount to buy
                c1_stable_price = ExchangeService.get_ticker_price(symbol_c1_stable)
                amount_c1_to_buy = stablecoin_amount / c1_stable_price

                order_step2 = ExchangeService.place_order(
                    api_key=api_key,
                    symbol=symbol_c1_stable,
                    order_type='market',
                    side='buy',
                    amount=amount_c1_to_buy
                )
                amount_crypto1_obtained = float(order_step2.get('filled', 0))

                TradingEngine.log_bot_status(
                    user_id=config.user_id,
                    config_id=config.id,
                    message=f"Step 2: Bought {amount_crypto1_obtained:.8f} {config.crypto1} with {stablecoin_amount:.8f} {preferred_stablecoin}",
                    log_level='INFO'
                )

            # Update target state
            target_row['status'] = 'Full'
            target_row['orderLevel'] += 1
            target_row['valueLevel'] = config.base_bid * (config.multiplier ** target_row['orderLevel'])
            target_row['crypto1AmountHeld'] = amount_crypto1_obtained
            target_row['originalCostCrypto2'] = cost_crypto2

            # Update bot state
            bot_state.set_target_states(sorted_states)
            db.session.commit()

            TradingEngine.log_bot_status(
                user_id=config.user_id,
                config_id=config.id,
                message=f"✅ STABLECOIN BUY SUCCESS: {amount_crypto1_obtained:.8f} {config.crypto1} for target {target_row['targetPrice']} (Level {target_row['orderLevel']})",
                log_level='INFO'
            )

            return True

        except Exception as e:
            logger.error(f"Error executing stablecoin BUY for target {target_row['targetPrice']}: {str(e)}")
            TradingEngine.log_bot_status(
                user_id=config.user_id,
                config_id=config.id,
                message=f"❌ STABLECOIN BUY FAILED for target {target_row['targetPrice']}: {str(e)}",
                log_level='ERROR'
            )
            return False

    @staticmethod
    def _execute_stablecoin_sell_order(config, bot_state, api_key, current_price, target_row, sorted_states, current_time, preferred_stablecoin):
        """
        Execute a two-step SELL order for stablecoin swap mode.
        Step 1: Sell Crypto1 for PreferredStablecoin
        Step 2: Buy Crypto2 with PreferredStablecoin
        Returns True if both steps were successful, False otherwise.
        """
        target_id = target_row['id']

        try:
            amount_crypto1_to_sell = target_row['crypto1AmountHeld']
            original_cost = target_row['originalCostCrypto2']

            # Step 1: Sell Crypto1 for Stablecoin
            if config.crypto1 == preferred_stablecoin:
                # Highly unlikely but handle it
                stablecoin_amount = amount_crypto1_to_sell
                TradingEngine.log_bot_status(
                    user_id=config.user_id,
                    config_id=config.id,
                    message=f"Step 1: {config.crypto1} is {preferred_stablecoin}. Amount: {stablecoin_amount:.8f}",
                    log_level='INFO'
                )
            else:
                symbol_c1_stable = TradingEngine.format_symbol(config.crypto1, preferred_stablecoin)
                order_step1 = ExchangeService.place_order(
                    api_key=api_key,
                    symbol=symbol_c1_stable,
                    order_type='market',
                    side='sell',
                    amount=amount_crypto1_to_sell
                )
                stablecoin_amount = float(order_step1.get('cost', 0))  # Amount of stablecoin received

                TradingEngine.log_bot_status(
                    user_id=config.user_id,
                    config_id=config.id,
                    message=f"Step 1: Sold {amount_crypto1_to_sell:.8f} {config.crypto1} for {stablecoin_amount:.8f} {preferred_stablecoin}",
                    log_level='INFO'
                )

            # Step 2: Buy Crypto2 with Stablecoin
            if config.crypto2 == preferred_stablecoin:
                # No conversion needed
                crypto2_reacquired = stablecoin_amount
                TradingEngine.log_bot_status(
                    user_id=config.user_id,
                    config_id=config.id,
                    message=f"Step 2: {config.crypto2} is already {preferred_stablecoin}. Amount: {crypto2_reacquired:.8f}",
                    log_level='INFO'
                )
            else:
                symbol_c2_stable = TradingEngine.format_symbol(config.crypto2, preferred_stablecoin)
                # Get current price to calculate amount to buy
                c2_stable_price = ExchangeService.get_ticker_price(symbol_c2_stable)
                amount_c2_to_buy = stablecoin_amount / c2_stable_price

                order_step2 = ExchangeService.place_order(
                    api_key=api_key,
                    symbol=symbol_c2_stable,
                    order_type='market',
                    side='buy',
                    amount=amount_c2_to_buy
                )
                crypto2_reacquired = float(order_step2.get('filled', 0))

                TradingEngine.log_bot_status(
                    user_id=config.user_id,
                    config_id=config.id,
                    message=f"Step 2: Bought {crypto2_reacquired:.8f} {config.crypto2} with {stablecoin_amount:.8f} {preferred_stablecoin}",
                    log_level='INFO'
                )

            # Calculate profit
            realized_profit = crypto2_reacquired - original_cost

            # Update target state
            target_row['status'] = 'Free'
            # orderLevel remains unchanged on sell
            target_row['valueLevel'] = config.base_bid * (config.multiplier ** target_row['orderLevel'])
            target_row['crypto1AmountHeld'] = None
            target_row['originalCostCrypto2'] = None

            # Update bot state
            bot_state.set_target_states(sorted_states)
            db.session.commit()

            TradingEngine.log_bot_status(
                user_id=config.user_id,
                config_id=config.id,
                message=f"✅ STABLECOIN SELL SUCCESS: {amount_crypto1_to_sell:.8f} {config.crypto1} from target {target_row['targetPrice']}. Profit: {realized_profit:.{config.num_digits}f} {config.crypto2}",
                log_level='INFO'
            )

            return True

        except Exception as e:
            logger.error(f"Error executing stablecoin SELL from target {target_row['targetPrice']}: {str(e)}")
            TradingEngine.log_bot_status(
                user_id=config.user_id,
                config_id=config.id,
                message=f"❌ STABLECOIN SELL FAILED from target {target_row['targetPrice']}: {str(e)}",
                log_level='ERROR'
            )
            return False

    # TODO: Implement more robust order handling:
    # 1. Asynchronous order status checking instead of assuming immediate fills.
    # 2. Handling partial fills.
    # 3. Graceful error handling for failed orders (e.g., insufficient balance, market unavailable, minimum order size).
    # 4. Consider atomicity: If one leg of a swap fails, how to handle the completed leg (e.g., revert, alert).
    # 5. Minimum order size checks for EACH pair before attempting a trade. (e.g. using exchange.load_markets())

    # TODO: Implement actual database balance updates for user's crypto1, crypto2, and stablecoin balances
    # after each confirmed trade leg, accounting for amounts and fees.



