#!/usr/bin/env python3
"""
Database migration script to add session management tables.
Run this script to update your existing database with session management functionality.
"""

import os
import sys
import logging
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from db import db
from models.trading_session_model import TradingSession
from models.trade_model import Trade

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def run_migration():
    """Run the database migration to add session management."""
    
    logger.info("Starting session management database migration...")
    
    # Create Flask app
    app = create_app()
    
    with app.app_context():
        try:
            # Check if trading_sessions table already exists
            inspector = db.inspect(db.engine)
            existing_tables = inspector.get_table_names()
            
            if 'trading_sessions' in existing_tables:
                logger.info("✅ trading_sessions table already exists")
            else:
                logger.info("📝 Creating trading_sessions table...")
                
            # Check if session_id column exists in trades table
            trades_columns = [col['name'] for col in inspector.get_columns('trades')]
            
            if 'session_id' in trades_columns:
                logger.info("✅ session_id column already exists in trades table")
            else:
                logger.info("📝 Adding session_id column to trades table...")
                
                # Add session_id column to trades table
                try:
                    db.engine.execute(db.text("""
                        ALTER TABLE trades 
                        ADD COLUMN session_id INTEGER 
                        REFERENCES trading_sessions(id)
                    """))
                    logger.info("✅ Added session_id column to trades table")
                except Exception as e:
                    logger.warning(f"⚠️ Could not add session_id column (may already exist): {e}")
            
            # Create all tables (this will create new tables and skip existing ones)
            db.create_all()
            logger.info("✅ Database tables created/updated successfully")
            
            # Verify the migration
            inspector = db.inspect(db.engine)
            tables_after = inspector.get_table_names()
            
            if 'trading_sessions' in tables_after:
                logger.info("✅ Migration completed successfully!")
                logger.info("📊 Available tables:")
                for table in sorted(tables_after):
                    logger.info(f"   - {table}")
                
                # Show trading_sessions table structure
                session_columns = inspector.get_columns('trading_sessions')
                logger.info("📋 trading_sessions table structure:")
                for col in session_columns:
                    logger.info(f"   - {col['name']}: {col['type']}")
                    
            else:
                logger.error("❌ Migration failed - trading_sessions table not found")
                return False
                
        except Exception as e:
            logger.error(f"❌ Migration failed with error: {e}")
            return False
    
    return True

def verify_migration():
    """Verify that the migration was successful."""
    
    logger.info("Verifying migration...")
    
    app = create_app()
    
    with app.app_context():
        try:
            # Test creating a session
            from models.user_model import User
            
            # Check if we have any users
            user_count = User.query.count()
            logger.info(f"📊 Found {user_count} users in database")
            
            if user_count == 0:
                logger.info("📝 Creating test user for verification...")
                test_user = User(username="test_migration", email="<EMAIL>")
                test_user.set_password("test123")
                db.session.add(test_user)
                db.session.commit()
                logger.info("✅ Test user created")
            
            # Get first user
            user = User.query.first()
            
            # Test creating a session
            test_session = TradingSession(
                user_id=user.id,
                session_uuid="test-migration-session",
                name="Migration Test Session",
                config_snapshot='{"crypto1": "BTC", "crypto2": "USDT", "tradingMode": "SimpleSpot"}'
            )
            
            db.session.add(test_session)
            db.session.commit()
            
            # Verify the session was created
            created_session = TradingSession.query.filter_by(session_uuid="test-migration-session").first()
            
            if created_session:
                logger.info("✅ Session creation test passed")
                
                # Clean up test session
                db.session.delete(created_session)
                db.session.commit()
                logger.info("🧹 Test session cleaned up")
                
                return True
            else:
                logger.error("❌ Session creation test failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Verification failed: {e}")
            return False

def main():
    """Main migration function."""
    
    print("🚀 Pluto Trading Bot - Session Management Migration")
    print("=" * 50)
    
    # Run migration
    if run_migration():
        print("\n✅ Migration completed successfully!")
        
        # Run verification
        if verify_migration():
            print("✅ Migration verification passed!")
            print("\n🎉 Session management is now ready to use!")
            print("\nNew features available:")
            print("  - Create and manage trading sessions")
            print("  - Save/load session states")
            print("  - Session-specific trade history")
            print("  - Session analytics and reporting")
        else:
            print("⚠️ Migration completed but verification failed")
            print("Please check the logs for details")
    else:
        print("❌ Migration failed!")
        print("Please check the logs and try again")
        sys.exit(1)

if __name__ == "__main__":
    main()
