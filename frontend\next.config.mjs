/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    // Ignore handlebars warnings
    config.module.rules.push({
      test: /\.handlebars$/,
      use: 'null-loader'
    });
    
    // Ignore specific source map warnings
    config.ignoreWarnings = [
      { module: /node_modules\/handlebars/ },
      { file: /node_modules\/handlebars/ }
    ];
    
    return config;
  },
  // Add other Next.js configurations here
  reactStrictMode: true,
  // Add any other configurations you need
};

export default nextConfig;
