"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@js-sdsl";
exports.ids = ["vendor-chunks/@js-sdsl"];
exports.modules = {

/***/ "(action-browser)/./node_modules/@js-sdsl/ordered-map/dist/esm/index.js":
/*!*************************************************************!*\
  !*** ./node_modules/@js-sdsl/ordered-map/dist/esm/index.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrderedMap: () => (/* binding */ OrderedMap)\n/* harmony export */ });\nvar extendStatics = function(e, r) {\n    extendStatics = Object.setPrototypeOf || {\n        __proto__: []\n    } instanceof Array && function(e, r) {\n        e.__proto__ = r;\n    } || function(e, r) {\n        for (var t in r) if (Object.prototype.hasOwnProperty.call(r, t)) e[t] = r[t];\n    };\n    return extendStatics(e, r);\n};\n\nfunction __extends(e, r) {\n    if (typeof r !== \"function\" && r !== null) throw new TypeError(\"Class extends value \" + String(r) + \" is not a constructor or null\");\n    extendStatics(e, r);\n    function __() {\n        this.constructor = e;\n    }\n    e.prototype = r === null ? Object.create(r) : (__.prototype = r.prototype, new __);\n}\n\nfunction __generator(e, r) {\n    var t = {\n        label: 0,\n        sent: function() {\n            if (s[0] & 1) throw s[1];\n            return s[1];\n        },\n        trys: [],\n        ops: []\n    }, i, n, s, h;\n    return h = {\n        next: verb(0),\n        throw: verb(1),\n        return: verb(2)\n    }, typeof Symbol === \"function\" && (h[Symbol.iterator] = function() {\n        return this;\n    }), h;\n    function verb(e) {\n        return function(r) {\n            return step([ e, r ]);\n        };\n    }\n    function step(a) {\n        if (i) throw new TypeError(\"Generator is already executing.\");\n        while (h && (h = 0, a[0] && (t = 0)), t) try {\n            if (i = 1, n && (s = a[0] & 2 ? n[\"return\"] : a[0] ? n[\"throw\"] || ((s = n[\"return\"]) && s.call(n), \n            0) : n.next) && !(s = s.call(n, a[1])).done) return s;\n            if (n = 0, s) a = [ a[0] & 2, s.value ];\n            switch (a[0]) {\n              case 0:\n              case 1:\n                s = a;\n                break;\n\n              case 4:\n                t.label++;\n                return {\n                    value: a[1],\n                    done: false\n                };\n\n              case 5:\n                t.label++;\n                n = a[1];\n                a = [ 0 ];\n                continue;\n\n              case 7:\n                a = t.ops.pop();\n                t.trys.pop();\n                continue;\n\n              default:\n                if (!(s = t.trys, s = s.length > 0 && s[s.length - 1]) && (a[0] === 6 || a[0] === 2)) {\n                    t = 0;\n                    continue;\n                }\n                if (a[0] === 3 && (!s || a[1] > s[0] && a[1] < s[3])) {\n                    t.label = a[1];\n                    break;\n                }\n                if (a[0] === 6 && t.label < s[1]) {\n                    t.label = s[1];\n                    s = a;\n                    break;\n                }\n                if (s && t.label < s[2]) {\n                    t.label = s[2];\n                    t.ops.push(a);\n                    break;\n                }\n                if (s[2]) t.ops.pop();\n                t.trys.pop();\n                continue;\n            }\n            a = r.call(e, t);\n        } catch (e) {\n            a = [ 6, e ];\n            n = 0;\n        } finally {\n            i = s = 0;\n        }\n        if (a[0] & 5) throw a[1];\n        return {\n            value: a[0] ? a[1] : void 0,\n            done: true\n        };\n    }\n}\n\ntypeof SuppressedError === \"function\" ? SuppressedError : function(e, r, t) {\n    var i = new Error(t);\n    return i.name = \"SuppressedError\", i.error = e, i.suppressed = r, i;\n};\n\nvar TreeNode = function() {\n    function TreeNode(e, r, t) {\n        if (t === void 0) {\n            t = 1;\n        }\n        this.t = undefined;\n        this.i = undefined;\n        this.h = undefined;\n        this.u = e;\n        this.o = r;\n        this.l = t;\n    }\n    TreeNode.prototype.v = function() {\n        var e = this;\n        var r = e.h.h === e;\n        if (r && e.l === 1) {\n            e = e.i;\n        } else if (e.t) {\n            e = e.t;\n            while (e.i) {\n                e = e.i;\n            }\n        } else {\n            if (r) {\n                return e.h;\n            }\n            var t = e.h;\n            while (t.t === e) {\n                e = t;\n                t = e.h;\n            }\n            e = t;\n        }\n        return e;\n    };\n    TreeNode.prototype.p = function() {\n        var e = this;\n        if (e.i) {\n            e = e.i;\n            while (e.t) {\n                e = e.t;\n            }\n            return e;\n        } else {\n            var r = e.h;\n            while (r.i === e) {\n                e = r;\n                r = e.h;\n            }\n            if (e.i !== r) {\n                return r;\n            } else return e;\n        }\n    };\n    TreeNode.prototype.T = function() {\n        var e = this.h;\n        var r = this.i;\n        var t = r.t;\n        if (e.h === this) e.h = r; else if (e.t === this) e.t = r; else e.i = r;\n        r.h = e;\n        r.t = this;\n        this.h = r;\n        this.i = t;\n        if (t) t.h = this;\n        return r;\n    };\n    TreeNode.prototype.I = function() {\n        var e = this.h;\n        var r = this.t;\n        var t = r.i;\n        if (e.h === this) e.h = r; else if (e.t === this) e.t = r; else e.i = r;\n        r.h = e;\n        r.i = this;\n        this.h = r;\n        this.t = t;\n        if (t) t.h = this;\n        return r;\n    };\n    return TreeNode;\n}();\n\nvar TreeNodeEnableIndex = function(e) {\n    __extends(TreeNodeEnableIndex, e);\n    function TreeNodeEnableIndex() {\n        var r = e !== null && e.apply(this, arguments) || this;\n        r.O = 1;\n        return r;\n    }\n    TreeNodeEnableIndex.prototype.T = function() {\n        var r = e.prototype.T.call(this);\n        this.M();\n        r.M();\n        return r;\n    };\n    TreeNodeEnableIndex.prototype.I = function() {\n        var r = e.prototype.I.call(this);\n        this.M();\n        r.M();\n        return r;\n    };\n    TreeNodeEnableIndex.prototype.M = function() {\n        this.O = 1;\n        if (this.t) {\n            this.O += this.t.O;\n        }\n        if (this.i) {\n            this.O += this.i.O;\n        }\n    };\n    return TreeNodeEnableIndex;\n}(TreeNode);\n\nvar ContainerIterator = function() {\n    function ContainerIterator(e) {\n        if (e === void 0) {\n            e = 0;\n        }\n        this.iteratorType = e;\n    }\n    ContainerIterator.prototype.equals = function(e) {\n        return this.C === e.C;\n    };\n    return ContainerIterator;\n}();\n\nvar Base = function() {\n    function Base() {\n        this._ = 0;\n    }\n    Object.defineProperty(Base.prototype, \"length\", {\n        get: function() {\n            return this._;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Base.prototype.size = function() {\n        return this._;\n    };\n    Base.prototype.empty = function() {\n        return this._ === 0;\n    };\n    return Base;\n}();\n\nvar Container = function(e) {\n    __extends(Container, e);\n    function Container() {\n        return e !== null && e.apply(this, arguments) || this;\n    }\n    return Container;\n}(Base);\n\nfunction throwIteratorAccessError() {\n    throw new RangeError(\"Iterator access denied!\");\n}\n\nvar TreeContainer = function(e) {\n    __extends(TreeContainer, e);\n    function TreeContainer(r, t) {\n        if (r === void 0) {\n            r = function(e, r) {\n                if (e < r) return -1;\n                if (e > r) return 1;\n                return 0;\n            };\n        }\n        if (t === void 0) {\n            t = false;\n        }\n        var i = e.call(this) || this;\n        i.N = undefined;\n        i.g = r;\n        i.enableIndex = t;\n        i.S = t ? TreeNodeEnableIndex : TreeNode;\n        i.A = new i.S;\n        return i;\n    }\n    TreeContainer.prototype.m = function(e, r) {\n        var t = this.A;\n        while (e) {\n            var i = this.g(e.u, r);\n            if (i < 0) {\n                e = e.i;\n            } else if (i > 0) {\n                t = e;\n                e = e.t;\n            } else return e;\n        }\n        return t;\n    };\n    TreeContainer.prototype.B = function(e, r) {\n        var t = this.A;\n        while (e) {\n            var i = this.g(e.u, r);\n            if (i <= 0) {\n                e = e.i;\n            } else {\n                t = e;\n                e = e.t;\n            }\n        }\n        return t;\n    };\n    TreeContainer.prototype.j = function(e, r) {\n        var t = this.A;\n        while (e) {\n            var i = this.g(e.u, r);\n            if (i < 0) {\n                t = e;\n                e = e.i;\n            } else if (i > 0) {\n                e = e.t;\n            } else return e;\n        }\n        return t;\n    };\n    TreeContainer.prototype.k = function(e, r) {\n        var t = this.A;\n        while (e) {\n            var i = this.g(e.u, r);\n            if (i < 0) {\n                t = e;\n                e = e.i;\n            } else {\n                e = e.t;\n            }\n        }\n        return t;\n    };\n    TreeContainer.prototype.R = function(e) {\n        while (true) {\n            var r = e.h;\n            if (r === this.A) return;\n            if (e.l === 1) {\n                e.l = 0;\n                return;\n            }\n            if (e === r.t) {\n                var t = r.i;\n                if (t.l === 1) {\n                    t.l = 0;\n                    r.l = 1;\n                    if (r === this.N) {\n                        this.N = r.T();\n                    } else r.T();\n                } else {\n                    if (t.i && t.i.l === 1) {\n                        t.l = r.l;\n                        r.l = 0;\n                        t.i.l = 0;\n                        if (r === this.N) {\n                            this.N = r.T();\n                        } else r.T();\n                        return;\n                    } else if (t.t && t.t.l === 1) {\n                        t.l = 1;\n                        t.t.l = 0;\n                        t.I();\n                    } else {\n                        t.l = 1;\n                        e = r;\n                    }\n                }\n            } else {\n                var t = r.t;\n                if (t.l === 1) {\n                    t.l = 0;\n                    r.l = 1;\n                    if (r === this.N) {\n                        this.N = r.I();\n                    } else r.I();\n                } else {\n                    if (t.t && t.t.l === 1) {\n                        t.l = r.l;\n                        r.l = 0;\n                        t.t.l = 0;\n                        if (r === this.N) {\n                            this.N = r.I();\n                        } else r.I();\n                        return;\n                    } else if (t.i && t.i.l === 1) {\n                        t.l = 1;\n                        t.i.l = 0;\n                        t.T();\n                    } else {\n                        t.l = 1;\n                        e = r;\n                    }\n                }\n            }\n        }\n    };\n    TreeContainer.prototype.G = function(e) {\n        if (this._ === 1) {\n            this.clear();\n            return;\n        }\n        var r = e;\n        while (r.t || r.i) {\n            if (r.i) {\n                r = r.i;\n                while (r.t) r = r.t;\n            } else {\n                r = r.t;\n            }\n            var t = e.u;\n            e.u = r.u;\n            r.u = t;\n            var i = e.o;\n            e.o = r.o;\n            r.o = i;\n            e = r;\n        }\n        if (this.A.t === r) {\n            this.A.t = r.h;\n        } else if (this.A.i === r) {\n            this.A.i = r.h;\n        }\n        this.R(r);\n        var n = r.h;\n        if (r === n.t) {\n            n.t = undefined;\n        } else n.i = undefined;\n        this._ -= 1;\n        this.N.l = 0;\n        if (this.enableIndex) {\n            while (n !== this.A) {\n                n.O -= 1;\n                n = n.h;\n            }\n        }\n    };\n    TreeContainer.prototype.P = function(e) {\n        var r = typeof e === \"number\" ? e : undefined;\n        var t = typeof e === \"function\" ? e : undefined;\n        var i = typeof e === \"undefined\" ? [] : undefined;\n        var n = 0;\n        var s = this.N;\n        var h = [];\n        while (h.length || s) {\n            if (s) {\n                h.push(s);\n                s = s.t;\n            } else {\n                s = h.pop();\n                if (n === r) return s;\n                i && i.push(s);\n                t && t(s, n, this);\n                n += 1;\n                s = s.i;\n            }\n        }\n        return i;\n    };\n    TreeContainer.prototype.q = function(e) {\n        while (true) {\n            var r = e.h;\n            if (r.l === 0) return;\n            var t = r.h;\n            if (r === t.t) {\n                var i = t.i;\n                if (i && i.l === 1) {\n                    i.l = r.l = 0;\n                    if (t === this.N) return;\n                    t.l = 1;\n                    e = t;\n                    continue;\n                } else if (e === r.i) {\n                    e.l = 0;\n                    if (e.t) {\n                        e.t.h = r;\n                    }\n                    if (e.i) {\n                        e.i.h = t;\n                    }\n                    r.i = e.t;\n                    t.t = e.i;\n                    e.t = r;\n                    e.i = t;\n                    if (t === this.N) {\n                        this.N = e;\n                        this.A.h = e;\n                    } else {\n                        var n = t.h;\n                        if (n.t === t) {\n                            n.t = e;\n                        } else n.i = e;\n                    }\n                    e.h = t.h;\n                    r.h = e;\n                    t.h = e;\n                    t.l = 1;\n                } else {\n                    r.l = 0;\n                    if (t === this.N) {\n                        this.N = t.I();\n                    } else t.I();\n                    t.l = 1;\n                    return;\n                }\n            } else {\n                var i = t.t;\n                if (i && i.l === 1) {\n                    i.l = r.l = 0;\n                    if (t === this.N) return;\n                    t.l = 1;\n                    e = t;\n                    continue;\n                } else if (e === r.t) {\n                    e.l = 0;\n                    if (e.t) {\n                        e.t.h = t;\n                    }\n                    if (e.i) {\n                        e.i.h = r;\n                    }\n                    t.i = e.t;\n                    r.t = e.i;\n                    e.t = t;\n                    e.i = r;\n                    if (t === this.N) {\n                        this.N = e;\n                        this.A.h = e;\n                    } else {\n                        var n = t.h;\n                        if (n.t === t) {\n                            n.t = e;\n                        } else n.i = e;\n                    }\n                    e.h = t.h;\n                    r.h = e;\n                    t.h = e;\n                    t.l = 1;\n                } else {\n                    r.l = 0;\n                    if (t === this.N) {\n                        this.N = t.T();\n                    } else t.T();\n                    t.l = 1;\n                    return;\n                }\n            }\n            if (this.enableIndex) {\n                r.M();\n                t.M();\n                e.M();\n            }\n            return;\n        }\n    };\n    TreeContainer.prototype.D = function(e, r, t) {\n        if (this.N === undefined) {\n            this._ += 1;\n            this.N = new this.S(e, r, 0);\n            this.N.h = this.A;\n            this.A.h = this.A.t = this.A.i = this.N;\n            return this._;\n        }\n        var i;\n        var n = this.A.t;\n        var s = this.g(n.u, e);\n        if (s === 0) {\n            n.o = r;\n            return this._;\n        } else if (s > 0) {\n            n.t = new this.S(e, r);\n            n.t.h = n;\n            i = n.t;\n            this.A.t = i;\n        } else {\n            var h = this.A.i;\n            var a = this.g(h.u, e);\n            if (a === 0) {\n                h.o = r;\n                return this._;\n            } else if (a < 0) {\n                h.i = new this.S(e, r);\n                h.i.h = h;\n                i = h.i;\n                this.A.i = i;\n            } else {\n                if (t !== undefined) {\n                    var u = t.C;\n                    if (u !== this.A) {\n                        var f = this.g(u.u, e);\n                        if (f === 0) {\n                            u.o = r;\n                            return this._;\n                        } else if (f > 0) {\n                            var o = u.v();\n                            var d = this.g(o.u, e);\n                            if (d === 0) {\n                                o.o = r;\n                                return this._;\n                            } else if (d < 0) {\n                                i = new this.S(e, r);\n                                if (o.i === undefined) {\n                                    o.i = i;\n                                    i.h = o;\n                                } else {\n                                    u.t = i;\n                                    i.h = u;\n                                }\n                            }\n                        }\n                    }\n                }\n                if (i === undefined) {\n                    i = this.N;\n                    while (true) {\n                        var c = this.g(i.u, e);\n                        if (c > 0) {\n                            if (i.t === undefined) {\n                                i.t = new this.S(e, r);\n                                i.t.h = i;\n                                i = i.t;\n                                break;\n                            }\n                            i = i.t;\n                        } else if (c < 0) {\n                            if (i.i === undefined) {\n                                i.i = new this.S(e, r);\n                                i.i.h = i;\n                                i = i.i;\n                                break;\n                            }\n                            i = i.i;\n                        } else {\n                            i.o = r;\n                            return this._;\n                        }\n                    }\n                }\n            }\n        }\n        if (this.enableIndex) {\n            var l = i.h;\n            while (l !== this.A) {\n                l.O += 1;\n                l = l.h;\n            }\n        }\n        this.q(i);\n        this._ += 1;\n        return this._;\n    };\n    TreeContainer.prototype.F = function(e, r) {\n        while (e) {\n            var t = this.g(e.u, r);\n            if (t < 0) {\n                e = e.i;\n            } else if (t > 0) {\n                e = e.t;\n            } else return e;\n        }\n        return e || this.A;\n    };\n    TreeContainer.prototype.clear = function() {\n        this._ = 0;\n        this.N = undefined;\n        this.A.h = undefined;\n        this.A.t = this.A.i = undefined;\n    };\n    TreeContainer.prototype.updateKeyByIterator = function(e, r) {\n        var t = e.C;\n        if (t === this.A) {\n            throwIteratorAccessError();\n        }\n        if (this._ === 1) {\n            t.u = r;\n            return true;\n        }\n        var i = t.p().u;\n        if (t === this.A.t) {\n            if (this.g(i, r) > 0) {\n                t.u = r;\n                return true;\n            }\n            return false;\n        }\n        var n = t.v().u;\n        if (t === this.A.i) {\n            if (this.g(n, r) < 0) {\n                t.u = r;\n                return true;\n            }\n            return false;\n        }\n        if (this.g(n, r) >= 0 || this.g(i, r) <= 0) return false;\n        t.u = r;\n        return true;\n    };\n    TreeContainer.prototype.eraseElementByPos = function(e) {\n        if (e < 0 || e > this._ - 1) {\n            throw new RangeError;\n        }\n        var r = this.P(e);\n        this.G(r);\n        return this._;\n    };\n    TreeContainer.prototype.eraseElementByKey = function(e) {\n        if (this._ === 0) return false;\n        var r = this.F(this.N, e);\n        if (r === this.A) return false;\n        this.G(r);\n        return true;\n    };\n    TreeContainer.prototype.eraseElementByIterator = function(e) {\n        var r = e.C;\n        if (r === this.A) {\n            throwIteratorAccessError();\n        }\n        var t = r.i === undefined;\n        var i = e.iteratorType === 0;\n        if (i) {\n            if (t) e.next();\n        } else {\n            if (!t || r.t === undefined) e.next();\n        }\n        this.G(r);\n        return e;\n    };\n    TreeContainer.prototype.getHeight = function() {\n        if (this._ === 0) return 0;\n        function traversal(e) {\n            if (!e) return 0;\n            return Math.max(traversal(e.t), traversal(e.i)) + 1;\n        }\n        return traversal(this.N);\n    };\n    return TreeContainer;\n}(Container);\n\nvar TreeIterator = function(e) {\n    __extends(TreeIterator, e);\n    function TreeIterator(r, t, i) {\n        var n = e.call(this, i) || this;\n        n.C = r;\n        n.A = t;\n        if (n.iteratorType === 0) {\n            n.pre = function() {\n                if (this.C === this.A.t) {\n                    throwIteratorAccessError();\n                }\n                this.C = this.C.v();\n                return this;\n            };\n            n.next = function() {\n                if (this.C === this.A) {\n                    throwIteratorAccessError();\n                }\n                this.C = this.C.p();\n                return this;\n            };\n        } else {\n            n.pre = function() {\n                if (this.C === this.A.i) {\n                    throwIteratorAccessError();\n                }\n                this.C = this.C.p();\n                return this;\n            };\n            n.next = function() {\n                if (this.C === this.A) {\n                    throwIteratorAccessError();\n                }\n                this.C = this.C.v();\n                return this;\n            };\n        }\n        return n;\n    }\n    Object.defineProperty(TreeIterator.prototype, \"index\", {\n        get: function() {\n            var e = this.C;\n            var r = this.A.h;\n            if (e === this.A) {\n                if (r) {\n                    return r.O - 1;\n                }\n                return 0;\n            }\n            var t = 0;\n            if (e.t) {\n                t += e.t.O;\n            }\n            while (e !== r) {\n                var i = e.h;\n                if (e === i.i) {\n                    t += 1;\n                    if (i.t) {\n                        t += i.t.O;\n                    }\n                }\n                e = i;\n            }\n            return t;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    TreeIterator.prototype.isAccessible = function() {\n        return this.C !== this.A;\n    };\n    return TreeIterator;\n}(ContainerIterator);\n\nvar OrderedMapIterator = function(e) {\n    __extends(OrderedMapIterator, e);\n    function OrderedMapIterator(r, t, i, n) {\n        var s = e.call(this, r, t, n) || this;\n        s.container = i;\n        return s;\n    }\n    Object.defineProperty(OrderedMapIterator.prototype, \"pointer\", {\n        get: function() {\n            if (this.C === this.A) {\n                throwIteratorAccessError();\n            }\n            var e = this;\n            return new Proxy([], {\n                get: function(r, t) {\n                    if (t === \"0\") return e.C.u; else if (t === \"1\") return e.C.o;\n                    r[0] = e.C.u;\n                    r[1] = e.C.o;\n                    return r[t];\n                },\n                set: function(r, t, i) {\n                    if (t !== \"1\") {\n                        throw new TypeError(\"prop must be 1\");\n                    }\n                    e.C.o = i;\n                    return true;\n                }\n            });\n        },\n        enumerable: false,\n        configurable: true\n    });\n    OrderedMapIterator.prototype.copy = function() {\n        return new OrderedMapIterator(this.C, this.A, this.container, this.iteratorType);\n    };\n    return OrderedMapIterator;\n}(TreeIterator);\n\nvar OrderedMap = function(e) {\n    __extends(OrderedMap, e);\n    function OrderedMap(r, t, i) {\n        if (r === void 0) {\n            r = [];\n        }\n        var n = e.call(this, t, i) || this;\n        var s = n;\n        r.forEach((function(e) {\n            s.setElement(e[0], e[1]);\n        }));\n        return n;\n    }\n    OrderedMap.prototype.begin = function() {\n        return new OrderedMapIterator(this.A.t || this.A, this.A, this);\n    };\n    OrderedMap.prototype.end = function() {\n        return new OrderedMapIterator(this.A, this.A, this);\n    };\n    OrderedMap.prototype.rBegin = function() {\n        return new OrderedMapIterator(this.A.i || this.A, this.A, this, 1);\n    };\n    OrderedMap.prototype.rEnd = function() {\n        return new OrderedMapIterator(this.A, this.A, this, 1);\n    };\n    OrderedMap.prototype.front = function() {\n        if (this._ === 0) return;\n        var e = this.A.t;\n        return [ e.u, e.o ];\n    };\n    OrderedMap.prototype.back = function() {\n        if (this._ === 0) return;\n        var e = this.A.i;\n        return [ e.u, e.o ];\n    };\n    OrderedMap.prototype.lowerBound = function(e) {\n        var r = this.m(this.N, e);\n        return new OrderedMapIterator(r, this.A, this);\n    };\n    OrderedMap.prototype.upperBound = function(e) {\n        var r = this.B(this.N, e);\n        return new OrderedMapIterator(r, this.A, this);\n    };\n    OrderedMap.prototype.reverseLowerBound = function(e) {\n        var r = this.j(this.N, e);\n        return new OrderedMapIterator(r, this.A, this);\n    };\n    OrderedMap.prototype.reverseUpperBound = function(e) {\n        var r = this.k(this.N, e);\n        return new OrderedMapIterator(r, this.A, this);\n    };\n    OrderedMap.prototype.forEach = function(e) {\n        this.P((function(r, t, i) {\n            e([ r.u, r.o ], t, i);\n        }));\n    };\n    OrderedMap.prototype.setElement = function(e, r, t) {\n        return this.D(e, r, t);\n    };\n    OrderedMap.prototype.getElementByPos = function(e) {\n        if (e < 0 || e > this._ - 1) {\n            throw new RangeError;\n        }\n        var r = this.P(e);\n        return [ r.u, r.o ];\n    };\n    OrderedMap.prototype.find = function(e) {\n        var r = this.F(this.N, e);\n        return new OrderedMapIterator(r, this.A, this);\n    };\n    OrderedMap.prototype.getElementByKey = function(e) {\n        var r = this.F(this.N, e);\n        return r.o;\n    };\n    OrderedMap.prototype.union = function(e) {\n        var r = this;\n        e.forEach((function(e) {\n            r.setElement(e[0], e[1]);\n        }));\n        return this._;\n    };\n    OrderedMap.prototype[Symbol.iterator] = function() {\n        var e, r, t, i;\n        return __generator(this, (function(n) {\n            switch (n.label) {\n              case 0:\n                e = this._;\n                r = this.P();\n                t = 0;\n                n.label = 1;\n\n              case 1:\n                if (!(t < e)) return [ 3, 4 ];\n                i = r[t];\n                return [ 4, [ i.u, i.o ] ];\n\n              case 2:\n                n.sent();\n                n.label = 3;\n\n              case 3:\n                ++t;\n                return [ 3, 1 ];\n\n              case 4:\n                return [ 2 ];\n            }\n        }));\n    };\n    return OrderedMap;\n}(TreeContainer);\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@js-sdsl/ordered-map/dist/esm/index.js\n");

/***/ })

};
;