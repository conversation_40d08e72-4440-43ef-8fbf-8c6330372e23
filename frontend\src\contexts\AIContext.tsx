"use client";

import type { ReactNode } from 'react';
import React, { createContext, useContext, useState } from 'react';
import { suggestTradingMode, type TradingModeSuggestionInput, type TradingModeSuggestionOutput } from '@/ai/flows/trading-mode-suggestion';

interface AIContextType {
  suggestion: TradingModeSuggestionOutput | null;
  isLoading: boolean;
  error: string | null;
  getTradingModeSuggestion: (input: TradingModeSuggestionInput) => Promise<void>;
}

const AIContext = createContext<AIContextType | undefined>(undefined);

export const AIProvider = ({ children }: { children: ReactNode }) => {
  const [suggestion, setSuggestion] = useState<TradingModeSuggestionOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const getTradingModeSuggestion = async (input: TradingModeSuggestionInput) => {
    setIsLoading(true);
    setError(null);
    setSuggestion(null);
    try {
      const result = await suggestTradingMode(input);
      setSuggestion(result);
    } catch (e) {
      setError(e instanceof Error ? e.message : "An unknown error occurred during AI suggestion.");
      console.error("Error fetching trading mode suggestion:", e);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AIContext.Provider value={{ suggestion, isLoading, error, getTradingModeSuggestion }}>
      {children}
    </AIContext.Provider>
  );
};

export const useAIContext = (): AIContextType => {
  const context = useContext(AIContext);
  if (context === undefined) {
    throw new Error('useAIContext must be used within an AIProvider');
  }
  return context;
};
