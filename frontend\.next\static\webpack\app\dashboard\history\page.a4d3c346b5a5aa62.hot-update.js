"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/history/page",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateFallbackMarketPrice(config);\n};\n// Enhanced API function to get market price for any trading pair\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        // For both SimpleSpot and StablecoinSwap modes, calculate crypto1/crypto2 price\n        const symbol = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n        // First try Binance API with proper error handling\n        try {\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 5000); // 5 second timeout\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol), {\n                signal: controller.signal,\n                headers: {\n                    'Accept': 'application/json'\n                }\n            });\n            clearTimeout(timeoutId);\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0 && !isNaN(price)) {\n                    console.log(\"✅ Binance price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price.toFixed(6)));\n                    return price;\n                }\n            } else if (response.status === 400) {\n                // Symbol not found on Binance, try reverse pair\n                const reverseSymbol = \"\".concat(config.crypto2).concat(config.crypto1).toUpperCase();\n                const reverseResponse = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(reverseSymbol), {\n                    headers: {\n                        'Accept': 'application/json'\n                    }\n                });\n                if (reverseResponse.ok) {\n                    const reverseData = await reverseResponse.json();\n                    const reversePrice = parseFloat(reverseData.price);\n                    if (reversePrice > 0 && !isNaN(reversePrice)) {\n                        const price = 1 / reversePrice;\n                        console.log(\"✅ Binance reverse price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price.toFixed(6), \" (from \").concat(reverseSymbol, \")\"));\n                        return price;\n                    }\n                }\n            }\n        } catch (binanceError) {\n            console.warn('Binance API failed:', binanceError);\n        }\n        // Try CoinGecko API as fallback\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                const controller = new AbortController();\n                const timeoutId = setTimeout(()=>controller.abort(), 5000);\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id), {\n                    signal: controller.signal,\n                    headers: {\n                        'Accept': 'application/json'\n                    }\n                });\n                clearTimeout(timeoutId);\n                if (response.ok) {\n                    var _data_crypto1Id;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[crypto2Id];\n                    if (price > 0 && !isNaN(price)) {\n                        console.log(\"✅ CoinGecko price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price.toFixed(6)));\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed:', geckoError);\n        }\n        // Final fallback to calculated price using USD values\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(\"⚠️ Using calculated price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(mockPrice.toFixed(6)));\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Helper function to get USD price (extracted from calculateFallbackMarketPrice)\nconst getUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies\n        'BTC': 109000,\n        'ETH': 4000,\n        'SOL': 240,\n        'ADA': 1.2,\n        'DOGE': 0.4,\n        'LINK': 25,\n        'MATIC': 0.5,\n        'DOT': 8,\n        'AVAX': 45,\n        'SHIB': 0.000030,\n        'XRP': 2.5,\n        'LTC': 110,\n        'BCH': 500,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    // Calculate the ratio: crypto1/crypto2 = (crypto1 USD price) / (crypto2 USD price)\n    // This gives us how many units of crypto2 equals 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    // Add small random fluctuation for realistic simulation\n    const fluctuation = (Math.random() - 0.5) * 0.01; // ±0.5% for more stable prices\n    const finalPrice = basePrice * (1 + fluctuation);\n    // Ensure price is positive and reasonable\n    const validPrice = Math.max(finalPrice, 0.000001);\n    console.log(\"\\uD83D\\uDCCA Calculated price: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice.toFixed(2), \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice.toFixed(2), \") = \").concat(validPrice.toFixed(8)));\n    return validPrice;\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        case 'FLUCTUATE_MARKET_PRICE':\n            {\n                if (state.currentMarketPrice <= 0) return state;\n                // More realistic fluctuation: smaller, more frequent changes\n                const fluctuationFactor = (Math.random() - 0.5) * 0.006; // Approx +/- 0.3% per update\n                const newPrice = state.currentMarketPrice * (1 + fluctuationFactor);\n                return {\n                    ...state,\n                    currentMarketPrice: newPrice > 0 ? newPrice : state.currentMarketPrice\n                };\n            }\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset)\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with completely fresh state');\n                // Clear the URL parameter to avoid confusion\n                const newUrl = window.location.pathname;\n                window.history.replaceState({}, '', newUrl);\n                return initialTradingState;\n            }\n        }\n        // Check if this is a new window/tab by checking if we have a current session\n        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 New window detected - starting with fresh state');\n            return initialTradingState;\n        }\n        // If we have a session, try to load saved state\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Initialize fetchMarketPrice first\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                const price = await getMarketPriceFromAPI(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n                // Send Telegram notification for price fetch failure\n                sendTelegramErrorNotification('Price Fetch Error', \"Failed to fetch market price for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2), \"Error: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n                // Use fallback price calculation\n                const fallbackPrice = calculateFallbackMarketPrice(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: fallbackPrice\n                });\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch,\n        sendTelegramErrorNotification\n    ]);\n    // Market price fluctuation effect - simulates real-time price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up price fluctuation interval for simulation - hardcoded to 2 seconds\n            const priceFluctuationInterval = setInterval({\n                \"TradingProvider.useEffect.priceFluctuationInterval\": ()=>{\n                    // Only fluctuate price if online\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        dispatch({\n                            type: 'FLUCTUATE_MARKET_PRICE'\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceFluctuationInterval\"], 2000); // Fixed at 2 seconds as requested\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceFluctuationInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        dispatch\n    ]);\n    // Other effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            if (state.appSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && state.appSettings.alertOnOrderExecution) {\n                    soundPath = state.appSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && state.appSettings.alertOnError) {\n                    soundPath = state.appSettings.soundError;\n                }\n                if (soundPath) {\n                    audioRef.current.src = soundPath;\n                    audioRef.current.currentTime = 0; // Reset to beginning\n                    // Play the sound and limit duration to 2 seconds\n                    audioRef.current.play().then({\n                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                            // Set a timeout to pause the audio after 2 seconds\n                            setTimeout({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.pause();\n                                        audioRef.current.currentTime = 0; // Reset for next play\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                        }\n                    }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                        \"TradingProvider.useCallback[playSound]\": (err)=>console.error(\"Error playing sound:\", err)\n                    }[\"TradingProvider.useCallback[playSound]\"]);\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Session-based alarm playing function\n    const playSessionAlarm = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSessionAlarm]\": (alarmType)=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (!currentSessionId) return;\n            try {\n                const alarmSettings = localStorage.getItem(\"session_alarm_\".concat(currentSessionId));\n                if (!alarmSettings) return;\n                const settings = JSON.parse(alarmSettings);\n                const isEnabled = alarmType === 'success' ? settings.successAlarmsEnabled : settings.errorAlarmsEnabled;\n                if (!isEnabled) return;\n                const soundType = alarmType === 'success' ? settings.successSoundType : settings.errorSoundType;\n                let soundUrl = '';\n                if (soundType === 'custom_success' && settings.customSuccessSoundDataUri) {\n                    soundUrl = settings.customSuccessSoundDataUri;\n                } else if (soundType === 'custom_error' && settings.customErrorSoundDataUri) {\n                    soundUrl = settings.customErrorSoundDataUri;\n                } else {\n                    soundUrl = \"/sounds/\".concat(soundType, \".mp3\");\n                }\n                if (audioRef.current && soundUrl) {\n                    audioRef.current.src = soundUrl;\n                    audioRef.current.currentTime = 0;\n                    audioRef.current.play().then({\n                        \"TradingProvider.useCallback[playSessionAlarm]\": ()=>{\n                            setTimeout({\n                                \"TradingProvider.useCallback[playSessionAlarm]\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.pause();\n                                        audioRef.current.currentTime = 0;\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSessionAlarm]\"], 2000);\n                        }\n                    }[\"TradingProvider.useCallback[playSessionAlarm]\"]).catch({\n                        \"TradingProvider.useCallback[playSessionAlarm]\": (error)=>{\n                            console.error('Failed to play session alarm:', error);\n                        }\n                    }[\"TradingProvider.useCallback[playSessionAlarm]\"]);\n                }\n            } catch (error) {\n                console.error('Error playing session alarm:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[playSessionAlarm]\"], []);\n    // Enhanced Telegram notification function with error handling\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async function(message) {\n            let isError = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return false;\n                }\n                const controller = new AbortController();\n                const timeoutId = setTimeout({\n                    \"TradingProvider.useCallback[sendTelegramNotification].timeoutId\": ()=>controller.abort()\n                }[\"TradingProvider.useCallback[sendTelegramNotification].timeoutId\"], 10000); // 10 second timeout\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    }),\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (response.ok) {\n                    console.log('✅ Telegram notification sent successfully');\n                    return true;\n                } else {\n                    const errorText = await response.text();\n                    console.error('❌ Failed to send Telegram notification:', response.status, errorText);\n                    return false;\n                }\n            } catch (error) {\n                console.error('❌ Error sending Telegram notification:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Specific error notification functions\n    const sendTelegramErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramErrorNotification]\": async (errorType, errorMessage, context)=>{\n            const timestamp = new Date().toLocaleString();\n            const contextInfo = context ? \"\\n\\uD83D\\uDCCD Context: \".concat(context) : '';\n            const message = \"\\uD83D\\uDEA8 <b>TRADING BOT ERROR</b>\\n\\n\" + \"⚠️ Error Type: \".concat(errorType, \"\\n\") + \"\\uD83D\\uDCAC Message: \".concat(errorMessage).concat(contextInfo, \"\\n\") + \"\\uD83D\\uDD50 Time: \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 Bot Status: \".concat(state.botSystemStatus);\n            return await sendTelegramNotification(message, true);\n        }\n    }[\"TradingProvider.useCallback[sendTelegramErrorNotification]\"], [\n        sendTelegramNotification,\n        state.botSystemStatus\n    ]);\n    const sendTelegramBalanceWarning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramBalanceWarning]\": async (balanceType, currentBalance, requiredBalance)=>{\n            const timestamp = new Date().toLocaleString();\n            const pair = \"\".concat(state.config.crypto1, \"/\").concat(state.config.crypto2);\n            const message = \"⚠️ <b>LOW BALANCE WARNING</b>\\n\\n\" + \"\\uD83D\\uDCB0 Balance Type: \".concat(balanceType, \"\\n\") + \"\\uD83D\\uDCCA Current: \".concat(currentBalance.toFixed(6), \"\\n\") + \"\\uD83D\\uDCC8 Required: \".concat(requiredBalance.toFixed(6), \"\\n\") + \"\\uD83D\\uDCC9 Pair: \".concat(pair, \"\\n\") + \"\\uD83D\\uDD50 Time: \".concat(timestamp, \"\\n\") + \"\\uD83D\\uDED1 Trading may be paused until balance is sufficient\";\n            return await sendTelegramNotification(message, true);\n        }\n    }[\"TradingProvider.useCallback[sendTelegramBalanceWarning]\"], [\n        sendTelegramNotification,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    const sendTelegramNetworkError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNetworkError]\": async (isDisconnected)=>{\n            const timestamp = new Date().toLocaleString();\n            const status = isDisconnected ? 'DISCONNECTED' : 'RECONNECTED';\n            const emoji = isDisconnected ? '🔴' : '🟢';\n            const message = \"\".concat(emoji, \" <b>NETWORK \").concat(status, \"</b>\\n\\n\") + \"\\uD83D\\uDCE1 Status: \".concat(isDisconnected ? 'Connection lost' : 'Connection restored', \"\\n\") + \"\\uD83E\\uDD16 Bot Action: \".concat(isDisconnected ? 'Stopped and session saved' : 'Ready to resume', \"\\n\") + \"\\uD83D\\uDD50 Time: \".concat(timestamp);\n            return await sendTelegramNotification(message, isDisconnected);\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNetworkError]\"], [\n        sendTelegramNotification\n    ]);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                playSessionAlarm('success');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram error notification\n                                console.warn(\"⚠️ Insufficient \".concat(config.crypto2, \" balance for BUY order. Required: \").concat(costCrypto2.toFixed(6), \", Available: \").concat(currentCrypto2Balance.toFixed(6)));\n                                sendTelegramBalanceWarning(\"\".concat(config.crypto2, \" (BUY Order)\"), currentCrypto2Balance, costCrypto2);\n                                // Play error alarm\n                                playSessionAlarm('error');\n                                // Also send general error notification\n                                sendTelegramErrorNotification('Insufficient Balance', \"Cannot execute BUY order for counter \".concat(activeRow.counter), \"Required: \".concat(costCrypto2.toFixed(6), \" \").concat(config.crypto2, \", Available: \").concat(currentCrypto2Balance.toFixed(6), \" \").concat(config.crypto2));\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            playSessionAlarm('success');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Get real market price for Crypto2/Stablecoin pair (synchronous for now)\n                                const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Get real market price for Crypto1/Stablecoin pair\n                                const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            const realizedProfitInCrypto2 = crypto2Reacquired - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 * config.incomeSplitCrypto1Percent / 100 / crypto1StablecoinPrice : 0;\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(config.preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            playSessionAlarm('success');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                // Send Telegram error notification\n                sendTelegramErrorNotification('Bot Start Failed', \"Failed to start trading bot on backend\", \"Config ID: \".concat(configId, \", Error: \").concat(error instanceof Error ? error.message : 'Unknown error'));\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast,\n        sendTelegramErrorNotification\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                // Send Telegram error notification\n                sendTelegramErrorNotification('Bot Stop Failed', \"Failed to stop trading bot on backend\", \"Config ID: \".concat(configId, \", Error: \").concat(error instanceof Error ? error.message : 'Unknown error'));\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast,\n        sendTelegramErrorNotification\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Session restoration effect - runs once on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const restoreSession = {\n                \"TradingProvider.useEffect.restoreSession\": async ()=>{\n                    try {\n                        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                        // Wait for session manager to initialize properly\n                        await new Promise({\n                            \"TradingProvider.useEffect.restoreSession\": (resolve)=>setTimeout(resolve, 1500)\n                        }[\"TradingProvider.useEffect.restoreSession\"]);\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            const session = sessionManager.loadSession(currentSessionId);\n                            if (session) {\n                                console.log('🔄 Restoring session from backend/storage:', currentSessionId, session.name);\n                                // Restore session data to context in the correct order\n                                dispatch({\n                                    type: 'SET_CONFIG',\n                                    payload: session.config\n                                });\n                                // Wait a moment for config to be set before setting other data\n                                await new Promise({\n                                    \"TradingProvider.useEffect.restoreSession\": (resolve)=>setTimeout(resolve, 100)\n                                }[\"TradingProvider.useEffect.restoreSession\"]);\n                                dispatch({\n                                    type: 'SET_TARGET_PRICE_ROWS',\n                                    payload: session.targetPriceRows\n                                });\n                                dispatch({\n                                    type: 'SET_MARKET_PRICE',\n                                    payload: session.currentMarketPrice\n                                });\n                                dispatch({\n                                    type: 'SET_BALANCES',\n                                    payload: {\n                                        crypto1: session.crypto1Balance,\n                                        crypto2: session.crypto2Balance\n                                    }\n                                });\n                                // Restore order history\n                                dispatch({\n                                    type: 'CLEAR_ORDER_HISTORY'\n                                });\n                                session.orderHistory.forEach({\n                                    \"TradingProvider.useEffect.restoreSession\": (entry)=>{\n                                        dispatch({\n                                            type: 'ADD_ORDER_HISTORY_ENTRY',\n                                            payload: entry\n                                        });\n                                    }\n                                }[\"TradingProvider.useEffect.restoreSession\"]);\n                                // If session was active (running), restore bot status after a delay\n                                if (session.isActive) {\n                                    console.log('🤖 Restoring active bot session - will start in 2 seconds');\n                                    setTimeout({\n                                        \"TradingProvider.useEffect.restoreSession\": ()=>{\n                                            dispatch({\n                                                type: 'SYSTEM_START_BOT_INITIATE'\n                                            });\n                                            console.log('🤖 Active bot session restored and started');\n                                        }\n                                    }[\"TradingProvider.useEffect.restoreSession\"], 2000);\n                                }\n                                console.log('✅ Session restored successfully');\n                                // Send Telegram notification about session restoration\n                                sendTelegramNotification(\"\\uD83D\\uDD04 <b>SESSION RESTORED</b>\\n\\n\" + \"\\uD83D\\uDCDD Session: \".concat(session.name, \"\\n\") + \"\\uD83D\\uDCCA Pair: \".concat(session.config.crypto1, \"/\").concat(session.config.crypto2, \"\\n\") + \"\\uD83D\\uDCB0 Balance: \".concat(session.crypto1Balance.toFixed(6), \" \").concat(session.config.crypto1, \", \").concat(session.crypto2Balance.toFixed(2), \" \").concat(session.config.crypto2, \"\\n\") + \"\\uD83E\\uDD16 Status: \".concat(session.isActive ? 'Will resume trading' : 'Stopped', \"\\n\") + \"\\uD83D\\uDD50 Time: \".concat(new Date().toLocaleString()));\n                            } else {\n                                console.log('No session found to restore');\n                            }\n                        } else {\n                            console.log('No current session ID found');\n                        }\n                    } catch (error) {\n                        console.error('Failed to restore session:', error);\n                        // Send Telegram error notification\n                        sendTelegramErrorNotification('Session Restoration Failed', 'Failed to restore session after page refresh', \"Error: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n                    }\n                }\n            }[\"TradingProvider.useEffect.restoreSession\"];\n            restoreSession();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        sendTelegramNotification,\n        sendTelegramErrorNotification\n    ]); // Run only once on mount\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Auto-save session periodically and on page unload\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Auto-save every 30 seconds if bot is running\n            const autoSaveInterval = setInterval({\n                \"TradingProvider.useEffect.autoSaveInterval\": ()=>{\n                    if (state.botSystemStatus === 'Running') {\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, true // isActive\n                            );\n                            console.log('💾 Auto-saved running session');\n                        }\n                    }\n                }\n            }[\"TradingProvider.useEffect.autoSaveInterval\"], 30000); // Every 30 seconds\n            // Save session before page unload\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": ()=>{\n                    const currentSessionId = sessionManager.getCurrentSessionId();\n                    if (currentSessionId) {\n                        sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        console.log('💾 Session saved before page unload');\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(autoSaveInterval);\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config,\n        state.targetPriceRows,\n        state.orderHistory,\n        state.currentMarketPrice,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot starts if none exists and handle runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Check if we need to create a session when bot starts\n            if (state.botSystemStatus === 'WarmingUp' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            stopBackendBot();\n                        }\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config).then({\n                            \"TradingProvider.useEffect\": (savedSessionId)=>{\n                                sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Create new session for the new crypto pair\n                    if (state.config.crypto1 && state.config.crypto2) {\n                        sessionManager.createNewSessionWithAutoName(state.config).then({\n                            \"TradingProvider.useEffect\": (newSessionId)=>{\n                                sessionManager.setCurrentSession(newSessionId);\n                                console.log('🆕 Created new session for crypto pair:', state.config.crypto1, '/', state.config.crypto2);\n                                toast({\n                                    title: \"Crypto Pair Changed\",\n                                    description: \"Previous session AutoSaved. New session created for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2),\n                                    duration: 5000\n                                });\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Send Telegram notification about network disconnection\n                            sendTelegramNetworkError(true);\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": (backupSessionId)=>{\n                                            sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"Bot stopped and session saved. Trading paused until connection restored.\",\n                            variant: \"destructive\",\n                            duration: 8000\n                        });\n                    } else if (isOnline && !isInitial) {\n                        // Send Telegram notification about network reconnection\n                        sendTelegramNetworkError(false);\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. You can resume trading.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, create one first\n                    if (state.config.crypto1 && state.config.crypto2) {\n                        sessionManager.createNewSessionWithAutoName(state.config).then({\n                            \"TradingProvider.useCallback[saveCurrentSession]\": (sessionId)=>{\n                                sessionManager.setCurrentSession(sessionId);\n                                sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                            }\n                        }[\"TradingProvider.useCallback[saveCurrentSession]\"]);\n                        return true;\n                    }\n                    return false;\n                }\n                // Save existing session\n                return sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1884,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"dqoyK3Cmb88Z2rxqV87z96o6fX8=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});