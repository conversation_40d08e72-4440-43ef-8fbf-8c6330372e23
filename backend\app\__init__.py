from flask import Flask
from flask_cors import CORS
from flask_jwt_extended import J<PERSON><PERSON>anager
from flask_socketio import <PERSON><PERSON><PERSON>
from flask.logging import default_handler
import logging
import os
from datetime import timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Create SocketIO instance
socketio = SocketIO(cors_allowed_origins="*")

def create_app(testing=False):
    """Initialize the core application."""
    app = Flask(__name__, instance_relative_config=False)
    
    # Set configuration
    if testing:
        app.config.from_object('app.config.TestConfig')
    else:
        app.config.from_object('app.config.Config')
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(os.path.join(app.root_path, 'logs/pluto.log'))
        ]
    )
    
    # Ensure log directory exists
    if not os.path.exists(os.path.join(app.root_path, 'logs')):
        os.makedirs(os.path.join(app.root_path, 'logs'))
    
    # Initialize extensions with proper CORS settings
    CORS(app, 
         resources={r"/*": {"origins": "*"}},  # Allow all routes for development
         methods=["GET", "HEAD", "POST", "OPTIONS", "PUT", "PATCH", "DELETE"],
         allow_headers=["Content-Type", "Authorization", "X-Requested-With"],
         supports_credentials=True,
         expose_headers=["Content-Disposition"])
    jwt = JWTManager(app)
    socketio.init_app(app)
    
    with app.app_context():
        # Import parts of our application
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from routes import auth_routes, trading_routes, user_routes, ai_routes, health_routes, session_routes
        from db import db, register_models
        
        # Register blueprints
        app.register_blueprint(auth_routes.auth_bp)
        app.register_blueprint(trading_routes.trading_bp)
        app.register_blueprint(user_routes.user_bp)
        app.register_blueprint(ai_routes.ai_bp)
        app.register_blueprint(health_routes.health_bp)
        app.register_blueprint(session_routes.session_bp)
        
        # Initialize database
        db.init_app(app)
        
        # Register models after db is initialized but before create_all
        register_models()
        
        # Create tables
        db.create_all()
        
        # Register error handlers
        from app.error_handlers import register_error_handlers
        register_error_handlers(app)
        
        return app 