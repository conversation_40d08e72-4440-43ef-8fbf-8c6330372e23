nohup: ignoring input
2025-05-24 05:44:43,221 - __main__ - INFO - No active bots found to restart on startup.
2025-05-24 05:44:43,221 - __main__ - INFO - Starting Pluto Trading Bot backend on port 5000
2025-05-24 05:44:43,222 - __main__ - INFO - Debug mode: OFF
2025-05-24 05:44:43,222 - werkzeug - WARNING - Werkzeug appears to be used in a production deployment. Consider switching to a production web server instead.
 * Serving Flask app 'app'
 * Debug mode: off
2025-05-24 05:44:43,224 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://************:5000
2025-05-24 05:44:43,224 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-24 05:44:50,041 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:44:50] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:45:00,040 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:45:00] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:45:10,041 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:45:10] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:45:10,043 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:45:10] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:45:14,320 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:45:14] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:45:14,322 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:45:14] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:45:14,326 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:45:14] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:45:14,329 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:45:14] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:45:24,328 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:45:24] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:45:34,328 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:45:34] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:45:44,328 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:45:44] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:45:44,330 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:45:44] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:45:54,328 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:45:54] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:46:04,328 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:46:04] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:46:14,328 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:46:14] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:46:14,331 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:46:14] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:46:24,328 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:46:24] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:46:28,684 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:46:28] "OPTIONS /auth/login HTTP/1.1" 200 -
2025-05-24 05:46:28,712 - routes.auth_routes - INFO - Login attempt for user: sakib
2025-05-24 05:46:28,848 - routes.auth_routes - INFO - User logged in: sakib
2025-05-24 05:46:28,848 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:46:28] "POST /auth/login HTTP/1.1" 200 -
2025-05-24 05:46:28,878 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:46:28] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:46:28,884 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:46:28] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:46:28,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:46:28] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:46:28,891 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:46:28] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:46:58,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:46:58] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:47:28,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:47:28] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:47:58,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:47:58] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:48:28,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:48:28] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:48:31,895 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:48:31] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:48:31,900 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:48:31] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:48:58,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:48:58] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:49:01,979 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:49:01] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:49:28,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:49:28] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:49:31,980 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:49:31] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:49:58,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:49:58] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:50:01,972 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:50:01] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:50:28,968 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:50:28] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:50:31,986 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:50:31] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:50:58,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:50:58] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:51:01,982 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:51:01] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:51:28,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:51:28] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:51:31,979 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:51:31] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:51:58,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:51:58] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:52:01,973 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:52:01] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:52:28,885 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:52:28] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:52:31,977 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:52:31] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:52:58,886 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:52:58] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:53:01,984 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:53:01] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:53:28,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:53:28] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:53:31,982 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:53:31] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:53:58,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:53:58] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:54:01,975 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:54:01] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:54:28,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:54:28] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:54:31,980 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:54:31] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:54:58,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:54:58] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:55:01,980 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:55:01] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:55:28,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:55:28] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:55:31,980 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:55:31] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:55:58,888 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:55:58] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:56:01,980 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:56:01] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:56:28,906 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:56:28] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:56:30,628 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:56:30] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:56:30,634 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:56:30] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:56:30,869 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:56:30] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:56:30,875 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:56:30] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:57:00,640 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:57:00] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:57:00,881 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:57:00] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:57:24,215 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:57:24] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:57:24,220 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:57:24] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:57:30,642 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:57:30] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:57:30,880 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:57:30] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:57:54,224 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:57:54] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:58:00,639 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:58:00] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:58:00,880 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:58:00] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:58:24,224 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:58:24] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:58:30,638 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:58:30] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:58:30,881 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:58:30] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:58:54,224 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:58:54] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:59:00,635 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:59:00] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:59:00,880 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:59:00] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:59:24,225 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:59:24] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:59:30,637 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:59:30] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:59:30,880 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:59:30] "GET /health/ HTTP/1.1" 200 -
2025-05-24 05:59:54,224 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 05:59:54] "GET /health/ HTTP/1.1" 200 -
2025-05-24 06:00:00,637 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 06:00:00] "GET /health/ HTTP/1.1" 200 -
2025-05-24 06:00:00,880 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 06:00:00] "GET /health/ HTTP/1.1" 200 -
2025-05-24 06:00:24,224 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 06:00:24] "GET /health/ HTTP/1.1" 200 -
2025-05-24 06:00:30,635 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 06:00:30] "GET /health/ HTTP/1.1" 200 -
2025-05-24 06:00:30,880 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 06:00:30] "GET /health/ HTTP/1.1" 200 -
2025-05-24 06:00:54,224 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 06:00:54] "GET /health/ HTTP/1.1" 200 -
2025-05-24 06:01:00,640 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 06:01:00] "GET /health/ HTTP/1.1" 200 -
2025-05-24 06:01:00,880 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 06:01:00] "GET /health/ HTTP/1.1" 200 -
2025-05-24 06:01:24,224 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 06:01:24] "GET /health/ HTTP/1.1" 200 -
2025-05-24 06:01:30,640 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 06:01:30] "GET /health/ HTTP/1.1" 200 -
2025-05-24 06:01:30,880 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 06:01:30] "GET /health/ HTTP/1.1" 200 -
2025-05-24 06:01:54,225 - werkzeug - INFO - 127.0.0.1 - - [24/May/2025 06:01:54] "GET /health/ HTTP/1.1" 200 -
