"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx":
/*!********************************************************!*\
  !*** ./src/components/layout/TradingConfigSidebar.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TradingConfigSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/TargetPriceModal */ \"(app-pages-browser)/./src/components/modals/TargetPriceModal.tsx\");\n/* harmony import */ var _components_modals_AlarmConfigModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/AlarmConfigModal */ \"(app-pages-browser)/./src/components/modals/AlarmConfigModal.tsx\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var _components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/crypto-input */ \"(app-pages-browser)/./src/components/ui/crypto-input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power-off.js\");\n/* harmony import */ var _barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Lightbulb,Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n// Force refresh of stablecoins list\nconst STABLECOINS_LIST = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n\n\n\nfunction TradingConfigSidebar() {\n    _s();\n    // Safely get trading context with error handling\n    let tradingContext;\n    try {\n        tradingContext = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    } catch (error) {\n        console.error('Trading context not available:', error);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n            className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"Trading context not available. Please refresh the page.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this);\n    }\n    const { config, dispatch, botSystemStatus, appSettings, setTargetPrices: contextSetTargetPrices } = tradingContext;\n    const isBotActive = botSystemStatus === 'Running';\n    const isBotWarmingUp = botSystemStatus === 'WarmingUp';\n    // AI context removed as requested\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    const [isTargetModalOpen, setIsTargetModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAlarmModalOpen, setIsAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // AI Suggestion form state removed\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        let val;\n        if (type === 'checkbox') {\n            val = checked;\n        } else if (type === 'number') {\n            // Handle empty string or invalid numbers gracefully\n            if (value === '' || value === null || value === undefined) {\n                val = 0;\n            } else {\n                const parsed = parseFloat(value);\n                val = isNaN(parsed) ? 0 : parsed;\n            }\n        } else {\n            val = value;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: val\n            }\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: value\n            }\n        });\n        if (name === 'crypto1') {\n            // Reset crypto2 if new crypto1 doesn't support current crypto2\n            const validQuotes = _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_QUOTES_SIMPLE[value] || DEFAULT_QUOTE_CURRENCIES || [\n                \"USDT\",\n                \"USDC\",\n                \"BTC\"\n            ];\n            if (!config.crypto2 || !Array.isArray(validQuotes) || !validQuotes.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: validQuotes[0] || 'USDT'\n                    }\n                }); // Ensure crypto2 gets a valid default\n            }\n        }\n    };\n    const handleCrypto1Selection = (crypto)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto1: crypto\n            }\n        });\n        // Reset crypto2 based on trading mode when crypto1 changes\n        if (config.tradingMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (_lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO1 || [\n                \"BTC\",\n                \"ETH\",\n                \"BNB\",\n                \"SOL\",\n                \"LINK\",\n                \"AVAX\",\n                \"DOT\",\n                \"UNI\",\n                \"NEAR\",\n                \"AAVE\",\n                \"ATOM\",\n                \"VET\",\n                \"RENDER\",\n                \"POL\",\n                \"ALGO\",\n                \"ARB\",\n                \"FET\",\n                \"PAXG\",\n                \"GALA\",\n                \"CRV\",\n                \"COMP\",\n                \"ENJ\"\n            ]).filter((c)=>c !== crypto);\n            if (!allowedCrypto2.includes(config.crypto2) || config.crypto2 === crypto) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO2 || [\n                \"USDC\",\n                \"DAI\",\n                \"TUSD\",\n                \"FDUSD\",\n                \"USDT\",\n                \"EUR\"\n            ];\n            if (!allowedCrypto2.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        }\n    };\n    const handleCrypto2Selection = (crypto)=>{\n        // In stablecoin swap mode, ensure crypto2 is different from crypto1\n        if (config.tradingMode === \"StablecoinSwap\" && crypto === config.crypto1) {\n            // Don't allow selecting the same crypto as crypto1 in swap mode\n            return;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto2: crypto\n            }\n        });\n    };\n    const handleTradingModeChange = (checked)=>{\n        const newMode = checked ? \"StablecoinSwap\" : \"SimpleSpot\";\n        // Reset crypto2 based on the new trading mode\n        let newCrypto2;\n        if (newMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (_lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO1 || [\n                \"BTC\",\n                \"ETH\",\n                \"BNB\",\n                \"SOL\",\n                \"LINK\",\n                \"AVAX\",\n                \"DOT\",\n                \"UNI\",\n                \"NEAR\",\n                \"AAVE\",\n                \"ATOM\",\n                \"VET\",\n                \"RENDER\",\n                \"POL\",\n                \"ALGO\",\n                \"ARB\",\n                \"FET\",\n                \"PAXG\",\n                \"GALA\",\n                \"CRV\",\n                \"COMP\",\n                \"ENJ\"\n            ]).filter((c)=>c !== config.crypto1);\n            newCrypto2 = allowedCrypto2[0];\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO2 || [\n                \"USDC\",\n                \"DAI\",\n                \"TUSD\",\n                \"FDUSD\",\n                \"USDT\",\n                \"EUR\"\n            ];\n            newCrypto2 = allowedCrypto2[0];\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                tradingMode: newMode,\n                crypto2: newCrypto2\n            }\n        });\n    };\n    const handleIncomeSplitChange = (cryptoKey, value)=>{\n        let percent = parseFloat(value);\n        if (isNaN(percent)) percent = 0;\n        if (percent < 0) percent = 0;\n        if (percent > 100) percent = 100;\n        if (cryptoKey === 'incomeSplitCrypto1Percent') {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto1Percent: percent,\n                    incomeSplitCrypto2Percent: 100 - percent\n                }\n            });\n        } else {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto2Percent: percent,\n                    incomeSplitCrypto1Percent: 100 - percent\n                }\n            });\n        }\n    };\n    // AI suggestion handler removed\n    // AI suggestion effect removed\n    // Use static crypto lists\n    const crypto1Options = _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS || [];\n    const crypto2Options = config.tradingMode === \"SimpleSpot\" ? _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_QUOTES_SIMPLE[config.crypto1] || DEFAULT_QUOTE_CURRENCIES || [\n        \"USDT\",\n        \"USDC\",\n        \"BTC\"\n    ] : (_lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS || []).filter((c)=>c !== config.crypto1); // Ensure Crypto2 is not same as Crypto1 in Swap mode\n    // Debug log to see what's actually loaded\n    console.log('🔍 DEBUG: AVAILABLE_CRYPTOS length:', _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS.length);\n    console.log('🔍 DEBUG: crypto1Options length:', crypto1Options.length);\n    console.log('🔍 DEBUG: First 20 cryptos:', _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_CRYPTOS.slice(0, 20));\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO1:', _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO1);\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO2:', _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO2);\n    console.log('🔍 DEBUG: AVAILABLE_STABLECOINS:', _lib_types__WEBPACK_IMPORTED_MODULE_13__.AVAILABLE_STABLECOINS);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-sidebar-primary\",\n                    children: \"Trading Configuration\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 171,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                className: \"flex-1 pr-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Mode\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                    id: \"enableStablecoinSwap\",\n                                                    checked: config.tradingMode === \"StablecoinSwap\",\n                                                    onCheckedChange: handleTradingModeChange\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"enableStablecoinSwap\",\n                                                    className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n                                                    children: \"Enable Stablecoin Swap Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"Current mode: \",\n                                                config.tradingMode === \"SimpleSpot\" ? \"Simple Spot\" : \"Stablecoin Swap\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this),\n                                        config.tradingMode === \"StablecoinSwap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"preferredStablecoin\",\n                                                    children: \"Preferred Stablecoin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    name: \"preferredStablecoin\",\n                                                    value: config.preferredStablecoin,\n                                                    onValueChange: (val)=>handleSelectChange(\"preferredStablecoin\", val),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            id: \"preferredStablecoin\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select stablecoin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            className: \"max-h-[300px] overflow-y-auto\",\n                                                            children: STABLECOINS_LIST.map((sc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: sc,\n                                                                    children: sc\n                                                                }, sc, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 200,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 18\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"mr-2 h-5 w-5 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 87\n                                            }, this),\n                                            \" AI Mode Suggestion\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"riskTolerance\",\n                                                    children: \"Risk Tolerance\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    value: riskTolerance,\n                                                    onValueChange: setRiskTolerance,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            id: \"riskTolerance\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select risk tolerance\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 220,\n                                                                columnNumber: 53\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 220,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"low\",\n                                                                    children: \"Low\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 222,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"medium\",\n                                                                    children: \"Medium\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 223,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: \"high\",\n                                                                    children: \"High\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 224,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"preferredCryptos\",\n                                                    children: \"Preferred Cryptocurrencies (comma-separated)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"preferredCryptos\",\n                                                    value: preferredCryptos,\n                                                    onChange: (e)=>setPreferredCryptos(e.target.value),\n                                                    placeholder: \"e.g., BTC, ETH\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"investmentGoals\",\n                                                    children: \"Investment Goals\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"investmentGoals\",\n                                                    value: investmentGoals,\n                                                    onChange: (e)=>setInvestmentGoals(e.target.value),\n                                                    placeholder: \"e.g., Long term, Short term profit\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: handleAISuggestion,\n                                            disabled: aiLoading,\n                                            className: \"w-full btn-neo\",\n                                            children: [\n                                                aiLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4 animate-spin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 31\n                                                }, this),\n                                                \"Get AI Suggestion\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Pair\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_14__.CryptoInput, {\n                                            label: \"Crypto 1 (Base)\",\n                                            value: config.crypto1,\n                                            allowedCryptos: _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ],\n                                            onValidCrypto: handleCrypto1Selection,\n                                            placeholder: \"e.g., BTC, ETH, SOL\",\n                                            description: \"Enter the base cryptocurrency symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_14__.CryptoInput, {\n                                            label: config.tradingMode === \"StablecoinSwap\" ? \"Crypto 2\" : \"Crypto 2 (Stablecoin)\",\n                                            value: config.crypto2,\n                                            allowedCryptos: config.tradingMode === \"StablecoinSwap\" ? (_lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ]).filter((c)=>c !== config.crypto1) : _lib_types__WEBPACK_IMPORTED_MODULE_13__.ALLOWED_CRYPTO2 || [\n                                                \"USDC\",\n                                                \"DAI\",\n                                                \"TUSD\",\n                                                \"FDUSD\",\n                                                \"USDT\",\n                                                \"EUR\"\n                                            ],\n                                            onValidCrypto: handleCrypto2Selection,\n                                            placeholder: config.tradingMode === \"StablecoinSwap\" ? \"e.g., BTC, ETH, SOL\" : \"e.g., USDT, USDC, DAI\",\n                                            description: config.tradingMode === \"StablecoinSwap\" ? \"Enter the second cryptocurrency symbol\" : \"Enter the quote/stablecoin symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 255,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Parameters\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        [\n                                            {\n                                                name: \"baseBid\",\n                                                label: \"Base Bid (Crypto 2)\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            },\n                                            {\n                                                name: \"multiplier\",\n                                                label: \"Multiplier\",\n                                                type: \"number\",\n                                                step: \"0.001\"\n                                            },\n                                            {\n                                                name: \"numDigits\",\n                                                label: \"Display Digits\",\n                                                type: \"number\",\n                                                step: \"1\"\n                                            },\n                                            {\n                                                name: \"slippagePercent\",\n                                                label: \"Slippage %\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            }\n                                        ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: field.name,\n                                                        children: field.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: field.name,\n                                                        name: field.name,\n                                                        type: field.type,\n                                                        value: config[field.name],\n                                                        onChange: handleInputChange,\n                                                        step: field.step,\n                                                        min: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    children: \"Couple Income % Split (must sum to 100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto1Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto1 || \"Crypto 1\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 296,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"incomeSplitCrypto1Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto1Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto1Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto2Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto2 || \"Crypto 2\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"incomeSplitCrypto2Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto2Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto2Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 301,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 174,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                        className: \"mb-4 bg-sidebar-border\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_16__.cn)(\"text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border\", isBotActive ? \"bg-green-600 text-primary-foreground\" : \"bg-muted text-muted-foreground\"),\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 129\n                                    }, this),\n                                    \"Bot Status: \",\n                                    isBotActive ? 'Running' : isBotWarmingUp ? 'Warming Up' : 'Stopped'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setIsTargetModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Target Prices\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setIsAlarmModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Alarm\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    if (isBotActive) {\n                                        dispatch({\n                                            type: 'SYSTEM_STOP_BOT'\n                                        });\n                                    } else {\n                                        dispatch({\n                                            type: 'SYSTEM_START_BOT_INITIATE'\n                                        });\n                                    }\n                                },\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_16__.cn)('w-full btn-neo', isBotActive || isBotWarmingUp ? 'bg-destructive hover:bg-destructive/90' : 'bg-green-600 hover:bg-green-600/90'),\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 129\n                                    }, this),\n                                    isBotActive ? 'Stop Bot' : isBotWarmingUp ? 'Warming Up...' : 'Start Bot'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    dispatch({\n                                        type: 'SYSTEM_RESET_BOT'\n                                    });\n                                    toast({\n                                        title: \"Bot Reset\",\n                                        description: \"Trading bot has been reset to fresh state. All positions cleared. Saved sessions are preserved.\",\n                                        duration: 4000\n                                    });\n                                },\n                                variant: \"outline\",\n                                className: \"w-full btn-outline-neo\",\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Lightbulb_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Reset Bot\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 339,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 310,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_11__.TargetPriceModal, {\n                isOpen: isTargetModalOpen,\n                onClose: ()=>setIsTargetModalOpen(false),\n                onSetTargetPrices: contextSetTargetPrices\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_AlarmConfigModal__WEBPACK_IMPORTED_MODULE_12__.AlarmConfigModal, {\n                isOpen: isAlarmModalOpen,\n                onClose: ()=>setIsAlarmModalOpen(false)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, this);\n}\n_s(TradingConfigSidebar, \"1Vqtl1Gof3HGb0UJDhvSoNc/2i4=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast\n    ];\n});\n_c = TradingConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TradingConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx\n"));

/***/ })

});