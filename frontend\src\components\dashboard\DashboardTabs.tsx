"use client";

import React from 'react';
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useRouter, usePathname } from 'next/navigation';
import { ListOrdered, History } from 'lucide-react';

const tabConfig = [
  { value: "orders", label: "Orders", href: "/dashboard", icon: <ListOrdered /> },
  { value: "history", label: "History", href: "/dashboard/history", icon: <History /> },
];

export default function DashboardTabs() {
  const router = useRouter();
  const pathname = usePathname();

  // Determine active tab based on pathname
  let activeTabValue = "orders"; // Default
  if (pathname === "/dashboard/history") activeTabValue = "history";


  const onTabChange = (value: string) => {
    const tab = tabConfig.find(t => t.value === value);
    if (tab) {
      router.push(tab.href);
    }
  };

  return (
    <Tabs value={activeTabValue} onValueChange={onTabChange} className="w-full mb-6">
      <TabsList className="grid w-full grid-cols-2 bg-card border-2 border-border">
        {tabConfig.map((tab) => (
          <TabsTrigger key={tab.value} value={tab.value} className="text-base data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:font-bold">
            <div className="flex items-center gap-2">
              {tab.icon}
              {tab.label}
            </div>
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}
