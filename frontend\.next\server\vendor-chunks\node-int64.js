/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/node-int64";
exports.ids = ["vendor-chunks/node-int64"];
exports.modules = {

/***/ "(action-browser)/./node_modules/node-int64/Int64.js":
/*!******************************************!*\
  !*** ./node_modules/node-int64/Int64.js ***!
  \******************************************/
/***/ ((module) => {

eval("//     Int64.js\n//\n//     Copyright (c) 2012 Robert Kieffer\n//     MIT License - http://opensource.org/licenses/mit-license.php\n\n/**\n * Support for handling 64-bit int numbers in Javascript (node.js)\n *\n * JS Numbers are IEEE-754 binary double-precision floats, which limits the\n * range of values that can be represented with integer precision to:\n *\n * 2^^53 <= N <= 2^53\n *\n * Int64 objects wrap a node Buffer that holds the 8-bytes of int64 data.  These\n * objects operate directly on the buffer which means that if they are created\n * using an existing buffer then setting the value will modify the Buffer, and\n * vice-versa.\n *\n * Internal Representation\n *\n * The internal buffer format is Big Endian.  I.e. the most-significant byte is\n * at buffer[0], the least-significant at buffer[7].  For the purposes of\n * converting to/from JS native numbers, the value is assumed to be a signed\n * integer stored in 2's complement form.\n *\n * For details about IEEE-754 see:\n * http://en.wikipedia.org/wiki/Double_precision_floating-point_format\n */\n\n// Useful masks and values for bit twiddling\nvar MASK31 =  0x7fffffff, VAL31 = 0x80000000;\nvar MASK32 =  0xffffffff, VAL32 = 0x100000000;\n\n// Map for converting hex octets to strings\nvar _HEX = [];\nfor (var i = 0; i < 256; i++) {\n  _HEX[i] = (i > 0xF ? '' : '0') + i.toString(16);\n}\n\n//\n// Int64\n//\n\n/**\n * Constructor accepts any of the following argument types:\n *\n * new Int64(buffer[, offset=0]) - Existing Buffer with byte offset\n * new Int64(Uint8Array[, offset=0]) - Existing Uint8Array with a byte offset\n * new Int64(string)             - Hex string (throws if n is outside int64 range)\n * new Int64(number)             - Number (throws if n is outside int64 range)\n * new Int64(hi, lo)             - Raw bits as two 32-bit values\n */\nvar Int64 = module.exports = function(a1, a2) {\n  if (a1 instanceof Buffer) {\n    this.buffer = a1;\n    this.offset = a2 || 0;\n  } else if (Object.prototype.toString.call(a1) == '[object Uint8Array]') {\n    // Under Browserify, Buffers can extend Uint8Arrays rather than an\n    // instance of Buffer. We could assume the passed in Uint8Array is actually\n    // a buffer but that won't handle the case where a raw Uint8Array is passed\n    // in. We construct a new Buffer just in case.\n    this.buffer = new Buffer(a1);\n    this.offset = a2 || 0;\n  } else {\n    this.buffer = this.buffer || new Buffer(8);\n    this.offset = 0;\n    this.setValue.apply(this, arguments);\n  }\n};\n\n\n// Max integer value that JS can accurately represent\nInt64.MAX_INT = Math.pow(2, 53);\n\n// Min integer value that JS can accurately represent\nInt64.MIN_INT = -Math.pow(2, 53);\n\nInt64.prototype = {\n\n  constructor: Int64,\n\n  /**\n   * Do in-place 2's compliment.  See\n   * http://en.wikipedia.org/wiki/Two's_complement\n   */\n  _2scomp: function() {\n    var b = this.buffer, o = this.offset, carry = 1;\n    for (var i = o + 7; i >= o; i--) {\n      var v = (b[i] ^ 0xff) + carry;\n      b[i] = v & 0xff;\n      carry = v >> 8;\n    }\n  },\n\n  /**\n   * Set the value. Takes any of the following arguments:\n   *\n   * setValue(string) - A hexidecimal string\n   * setValue(number) - Number (throws if n is outside int64 range)\n   * setValue(hi, lo) - Raw bits as two 32-bit values\n   */\n  setValue: function(hi, lo) {\n    var negate = false;\n    if (arguments.length == 1) {\n      if (typeof(hi) == 'number') {\n        // Simplify bitfield retrieval by using abs() value.  We restore sign\n        // later\n        negate = hi < 0;\n        hi = Math.abs(hi);\n        lo = hi % VAL32;\n        hi = hi / VAL32;\n        if (hi > VAL32) throw new RangeError(hi  + ' is outside Int64 range');\n        hi = hi | 0;\n      } else if (typeof(hi) == 'string') {\n        hi = (hi + '').replace(/^0x/, '');\n        lo = hi.substr(-8);\n        hi = hi.length > 8 ? hi.substr(0, hi.length - 8) : '';\n        hi = parseInt(hi, 16);\n        lo = parseInt(lo, 16);\n      } else {\n        throw new Error(hi + ' must be a Number or String');\n      }\n    }\n\n    // Technically we should throw if hi or lo is outside int32 range here, but\n    // it's not worth the effort. Anything past the 32'nd bit is ignored.\n\n    // Copy bytes to buffer\n    var b = this.buffer, o = this.offset;\n    for (var i = 7; i >= 0; i--) {\n      b[o+i] = lo & 0xff;\n      lo = i == 4 ? hi : lo >>> 8;\n    }\n\n    // Restore sign of passed argument\n    if (negate) this._2scomp();\n  },\n\n  /**\n   * Convert to a native JS number.\n   *\n   * WARNING: Do not expect this value to be accurate to integer precision for\n   * large (positive or negative) numbers!\n   *\n   * @param allowImprecise If true, no check is performed to verify the\n   * returned value is accurate to integer precision.  If false, imprecise\n   * numbers (very large positive or negative numbers) will be forced to +/-\n   * Infinity.\n   */\n  toNumber: function(allowImprecise) {\n    var b = this.buffer, o = this.offset;\n\n    // Running sum of octets, doing a 2's complement\n    var negate = b[o] & 0x80, x = 0, carry = 1;\n    for (var i = 7, m = 1; i >= 0; i--, m *= 256) {\n      var v = b[o+i];\n\n      // 2's complement for negative numbers\n      if (negate) {\n        v = (v ^ 0xff) + carry;\n        carry = v >> 8;\n        v = v & 0xff;\n      }\n\n      x += v * m;\n    }\n\n    // Return Infinity if we've lost integer precision\n    if (!allowImprecise && x >= Int64.MAX_INT) {\n      return negate ? -Infinity : Infinity;\n    }\n\n    return negate ? -x : x;\n  },\n\n  /**\n   * Convert to a JS Number. Returns +/-Infinity for values that can't be\n   * represented to integer precision.\n   */\n  valueOf: function() {\n    return this.toNumber(false);\n  },\n\n  /**\n   * Return string value\n   *\n   * @param radix Just like Number#toString()'s radix\n   */\n  toString: function(radix) {\n    return this.valueOf().toString(radix || 10);\n  },\n\n  /**\n   * Return a string showing the buffer octets, with MSB on the left.\n   *\n   * @param sep separator string. default is '' (empty string)\n   */\n  toOctetString: function(sep) {\n    var out = new Array(8);\n    var b = this.buffer, o = this.offset;\n    for (var i = 0; i < 8; i++) {\n      out[i] = _HEX[b[o+i]];\n    }\n    return out.join(sep || '');\n  },\n\n  /**\n   * Returns the int64's 8 bytes in a buffer.\n   *\n   * @param {bool} [rawBuffer=false]  If no offset and this is true, return the internal buffer.  Should only be used if\n   *                                  you're discarding the Int64 afterwards, as it breaks encapsulation.\n   */\n  toBuffer: function(rawBuffer) {\n    if (rawBuffer && this.offset === 0) return this.buffer;\n\n    var out = new Buffer(8);\n    this.buffer.copy(out, 0, this.offset, this.offset + 8);\n    return out;\n  },\n\n  /**\n   * Copy 8 bytes of int64 into target buffer at target offset.\n   *\n   * @param {Buffer} targetBuffer       Buffer to copy into.\n   * @param {number} [targetOffset=0]   Offset into target buffer.\n   */\n  copy: function(targetBuffer, targetOffset) {\n    this.buffer.copy(targetBuffer, targetOffset || 0, this.offset, this.offset + 8);\n  },\n\n  /**\n   * Returns a number indicating whether this comes before or after or is the\n   * same as the other in sort order.\n   *\n   * @param {Int64} other  Other Int64 to compare.\n   */\n  compare: function(other) {\n\n    // If sign bits differ ...\n    if ((this.buffer[this.offset] & 0x80) != (other.buffer[other.offset] & 0x80)) {\n      return other.buffer[other.offset] - this.buffer[this.offset];\n    }\n\n    // otherwise, compare bytes lexicographically\n    for (var i = 0; i < 8; i++) {\n      if (this.buffer[this.offset+i] !== other.buffer[other.offset+i]) {\n        return this.buffer[this.offset+i] - other.buffer[other.offset+i];\n      }\n    }\n    return 0;\n  },\n\n  /**\n   * Returns a boolean indicating if this integer is equal to other.\n   *\n   * @param {Int64} other  Other Int64 to compare.\n   */\n  equals: function(other) {\n    return this.compare(other) === 0;\n  },\n\n  /**\n   * Pretty output in console.log\n   */\n  inspect: function() {\n    return '[Int64 value:' + this + ' octets:' + this.toOctetString(' ') + ']';\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/node-int64/Int64.js\n");

/***/ })

};
;