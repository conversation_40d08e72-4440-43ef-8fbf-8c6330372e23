# 📱 Telegram Integration Setup Guide

## 🎯 **Overview**
Set up Telegram notifications for your Pluto Trading Bot to receive real-time alerts about:
- ✅ Trade executions (Buy/Sell orders)
- ✅ Bot start/stop notifications
- ✅ Error alerts and warnings
- ✅ Session management updates
- ✅ Network connectivity issues

---

## 📋 **Step-by-Step Setup**

### **Step 1: Create Your Telegram Bot**

1. **Open Telegram** and search for `@BotFather`
2. **Start a conversation** with BotFather
3. **Send the command**: `/newbot`
4. **Choose a name** for your bot (e.g., "Pluto Trading Bot")
5. **Choose a username** for your bot (must end with 'bot', e.g., "pluto_trading_bot")
6. **Copy the bot token** that BotFather provides (looks like: `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`)

### **Step 2: Get Your Chat ID**

1. **Start a conversation** with your newly created bot
2. **Send any message** to your bot (e.g., "Hello")
3. **Open your browser** and visit:
   ```
   https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
   ```
   Replace `<YOUR_BOT_TOKEN>` with the token from Step 1
4. **Find your chat ID** in the JSON response (look for `"chat":{"id":123456789}`)
5. **Copy the chat ID** (it's a number, could be positive or negative)

### **Step 3: Configure Backend Environment**

1. **Navigate to backend folder**:
   ```cmd
   cd E:\bot\tradingbot_final\backend
   ```

2. **Edit the `.env` file** (create if it doesn't exist):
   ```env
   # Telegram Configuration
   TELEGRAM_BOT_TOKEN=123456789:ABCdefGHIjklMNOpqrsTUVwxyz
   TELEGRAM_CHAT_ID=123456789
   
   # Other existing configurations...
   DATABASE_URL=sqlite:///trading_bot.db
   SECRET_KEY=your-secret-key-here
   ```

3. **Save the file**

### **Step 4: Test the Integration**

1. **Restart your backend server**:
   ```cmd
   python run.py
   ```

2. **Check the logs** for Telegram initialization:
   ```
   INFO - Telegram service initialized successfully
   ```

3. **Test by starting/stopping a bot** - you should receive notifications!

---

## 🔧 **Configuration Options**

### **Environment Variables**

| Variable | Description | Example |
|----------|-------------|---------|
| `TELEGRAM_BOT_TOKEN` | Bot token from BotFather | `123456789:ABCdefGHI...` |
| `TELEGRAM_CHAT_ID` | Your chat ID | `123456789` |

### **Optional Settings**

You can also configure these in your `.env` file:

```env
# Telegram Settings (Optional)
TELEGRAM_ENABLED=true
TELEGRAM_PARSE_MODE=HTML
TELEGRAM_TIMEOUT=10
```

---

## 📨 **Notification Types**

### **Trade Notifications**
```
🟢 BUY order executed

Pair: BTC/USDT
Amount: 0.001000 BTC
Price: 45000.00 USDT
Value: 45.00 USDT
```

### **Bot Status Notifications**
```
🤖 Bot Started

Configuration: BTC/USDT Strategy
Time: 2024-01-15 14:30:25
```

### **Error Alerts**
```
⚠️ Error Alert

Error: Insufficient balance for trade
Context: BTC/USDT trading pair
Time: 2024-01-15 14:35:10
```

### **Session Notifications**
```
💾 Session Saved

Session: BTC/USDT Strategy 1
Time: 2024-01-15 14:40:00
```

---

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **"Telegram service disabled" in logs**
- ✅ Check that `TELEGRAM_BOT_TOKEN` and `TELEGRAM_CHAT_ID` are set in `.env`
- ✅ Restart the backend server after adding environment variables

#### **"Failed to send Telegram message"**
- ✅ Verify bot token is correct
- ✅ Verify chat ID is correct (try both positive and negative values)
- ✅ Make sure you've sent at least one message to your bot

#### **Bot not responding**
- ✅ Check if bot is active (search for it in Telegram)
- ✅ Try creating a new bot with BotFather
- ✅ Ensure bot username ends with 'bot'

#### **Wrong chat ID**
- ✅ Send a message to your bot first
- ✅ Use the exact URL with your bot token
- ✅ Look for `"chat":{"id":NUMBER}` in the JSON response

### **Testing Commands**

#### **Test Bot Token**
```bash
curl "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getMe"
```

#### **Test Sending Message**
```bash
curl -X POST "https://api.telegram.org/bot<YOUR_BOT_TOKEN>/sendMessage" \
     -H "Content-Type: application/json" \
     -d '{"chat_id": "<YOUR_CHAT_ID>", "text": "Test message"}'
```

---

## 🔒 **Security Best Practices**

### **Protect Your Bot Token**
- ✅ Never share your bot token publicly
- ✅ Use environment variables (`.env` file)
- ✅ Add `.env` to your `.gitignore` file
- ✅ Regenerate token if compromised

### **Chat ID Privacy**
- ✅ Only use your own chat ID
- ✅ Don't share chat IDs with others
- ✅ Consider using group chats for team notifications

---

## 📱 **Advanced Features**

### **Group Notifications**
1. Add your bot to a Telegram group
2. Make the bot an admin (optional)
3. Get the group chat ID (negative number)
4. Use group chat ID in configuration

### **Multiple Recipients**
To send notifications to multiple chats, you can:
1. Create multiple bots
2. Use different environment variables
3. Modify the backend code to support multiple chat IDs

### **Custom Notification Formatting**
Edit `backend/services/telegram_service.py` to customize:
- Message formatting
- Emoji usage
- Additional data fields
- Notification frequency

---

## ✅ **Verification Checklist**

- [ ] Bot created with BotFather
- [ ] Bot token copied correctly
- [ ] Chat ID obtained and verified
- [ ] `.env` file configured
- [ ] Backend server restarted
- [ ] Test message sent successfully
- [ ] Trade notifications working
- [ ] Error notifications working

---

## 🎉 **You're All Set!**

Your Pluto Trading Bot will now send real-time notifications to your Telegram chat. You'll be instantly informed about all trading activities, errors, and important events!

**Need help?** Check the backend logs for detailed error messages and troubleshooting information.
