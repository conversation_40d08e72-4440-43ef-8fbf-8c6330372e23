"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateFallbackMarketPrice(config);\n};\n// Enhanced API function to get market price for any trading pair\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        // For both SimpleSpot and StablecoinSwap modes, calculate crypto1/crypto2 price\n        const symbol = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n        // First try Binance API with proper error handling\n        try {\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 5000); // 5 second timeout\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol), {\n                signal: controller.signal,\n                headers: {\n                    'Accept': 'application/json'\n                }\n            });\n            clearTimeout(timeoutId);\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0 && !isNaN(price)) {\n                    console.log(\"✅ Binance price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price, \" (exact: \").concat(data.price, \")\"));\n                    return price; // Return exact price without rounding\n                }\n            } else if (response.status === 400) {\n                // Symbol not found on Binance, try reverse pair\n                const reverseSymbol = \"\".concat(config.crypto2).concat(config.crypto1).toUpperCase();\n                const reverseResponse = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(reverseSymbol), {\n                    headers: {\n                        'Accept': 'application/json'\n                    }\n                });\n                if (reverseResponse.ok) {\n                    const reverseData = await reverseResponse.json();\n                    const reversePrice = parseFloat(reverseData.price);\n                    if (reversePrice > 0 && !isNaN(reversePrice)) {\n                        const price = 1 / reversePrice;\n                        console.log(\"✅ Binance reverse price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price, \" (from \").concat(reverseSymbol, \", exact: \").concat(reverseData.price, \")\"));\n                        return price; // Return exact calculated price\n                    }\n                }\n            }\n        } catch (binanceError) {\n            console.warn('Binance API failed:', binanceError);\n        }\n        // Try CoinGecko API as fallback\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                const controller = new AbortController();\n                const timeoutId = setTimeout(()=>controller.abort(), 5000);\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id), {\n                    signal: controller.signal,\n                    headers: {\n                        'Accept': 'application/json'\n                    }\n                });\n                clearTimeout(timeoutId);\n                if (response.ok) {\n                    var _data_crypto1Id;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[crypto2Id];\n                    if (price > 0 && !isNaN(price)) {\n                        console.log(\"✅ CoinGecko price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price.toFixed(6)));\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed:', geckoError);\n        }\n        // Final fallback to calculated price using USD values\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(\"⚠️ Using calculated price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(mockPrice.toFixed(6)));\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Helper function to get USD price (extracted from calculateFallbackMarketPrice)\nconst getUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies\n        'BTC': 109000,\n        'ETH': 4000,\n        'SOL': 240,\n        'ADA': 1.2,\n        'DOGE': 0.4,\n        'LINK': 25,\n        'MATIC': 0.5,\n        'DOT': 8,\n        'AVAX': 45,\n        'SHIB': 0.000030,\n        'XRP': 2.5,\n        'LTC': 110,\n        'BCH': 500,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    // Calculate the ratio: crypto1/crypto2 = (crypto1 USD price) / (crypto2 USD price)\n    // This gives us how many units of crypto2 equals 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    // No fluctuation - use exact calculated price for consistency\n    const validPrice = Math.max(basePrice, 0.000001);\n    console.log(\"\\uD83D\\uDCCA Calculated price: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice.toFixed(2), \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice.toFixed(2), \") = \").concat(validPrice.toFixed(8)));\n    return validPrice;\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset)\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            console.log('▶️ Reducer: Processing SYSTEM_START_BOT_INITIATE - changing status from', state.botSystemStatus, 'to WarmingUp');\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            console.log('✅ Reducer: Processing SYSTEM_COMPLETE_WARMUP - changing status from', state.botSystemStatus, 'to Running');\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            console.log('🛑 Reducer: Processing SYSTEM_STOP_BOT - changing status from', state.botSystemStatus, 'to Stopped');\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with completely fresh state');\n                // Clear the URL parameter to avoid confusion\n                const newUrl = window.location.pathname;\n                window.history.replaceState({}, '', newUrl);\n                return initialTradingState;\n            }\n        }\n        // Check if this is a new window/tab by checking if we have a current session\n        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 New window detected - starting with fresh state');\n            return initialTradingState;\n        }\n        // If we have a session, try to load saved state\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Enhanced Telegram notification function with error handling\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async function(message) {\n            let isError = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return false;\n                }\n                const controller = new AbortController();\n                const timeoutId = setTimeout({\n                    \"TradingProvider.useCallback[sendTelegramNotification].timeoutId\": ()=>controller.abort()\n                }[\"TradingProvider.useCallback[sendTelegramNotification].timeoutId\"], 10000); // 10 second timeout\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    }),\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (response.ok) {\n                    console.log('✅ Telegram notification sent successfully');\n                    return true;\n                } else {\n                    const errorText = await response.text();\n                    console.error('❌ Failed to send Telegram notification:', response.status, errorText);\n                    return false;\n                }\n            } catch (error) {\n                console.error('❌ Error sending Telegram notification:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Specific error notification functions\n    const sendTelegramErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramErrorNotification]\": async (errorType, errorMessage, context)=>{\n            const timestamp = new Date().toLocaleString();\n            const contextInfo = context ? \"\\n\\uD83D\\uDCCD Context: \".concat(context) : '';\n            const message = \"\\uD83D\\uDEA8 <b>TRADING BOT ERROR</b>\\n\\n\" + \"⚠️ Error Type: \".concat(errorType, \"\\n\") + \"\\uD83D\\uDCAC Message: \".concat(errorMessage).concat(contextInfo, \"\\n\") + \"\\uD83D\\uDD50 Time: \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 Bot Status: \".concat(state.botSystemStatus);\n            return await sendTelegramNotification(message, true);\n        }\n    }[\"TradingProvider.useCallback[sendTelegramErrorNotification]\"], [\n        sendTelegramNotification,\n        state.botSystemStatus\n    ]);\n    const sendTelegramBalanceWarning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramBalanceWarning]\": async (balanceType, currentBalance, requiredBalance)=>{\n            const timestamp = new Date().toLocaleString();\n            const pair = \"\".concat(state.config.crypto1, \"/\").concat(state.config.crypto2);\n            const message = \"⚠️ <b>LOW BALANCE WARNING</b>\\n\\n\" + \"\\uD83D\\uDCB0 Balance Type: \".concat(balanceType, \"\\n\") + \"\\uD83D\\uDCCA Current: \".concat(currentBalance.toFixed(6), \"\\n\") + \"\\uD83D\\uDCC8 Required: \".concat(requiredBalance.toFixed(6), \"\\n\") + \"\\uD83D\\uDCC9 Pair: \".concat(pair, \"\\n\") + \"\\uD83D\\uDD50 Time: \".concat(timestamp, \"\\n\") + \"\\uD83D\\uDED1 Trading may be paused until balance is sufficient\";\n            return await sendTelegramNotification(message, true);\n        }\n    }[\"TradingProvider.useCallback[sendTelegramBalanceWarning]\"], [\n        sendTelegramNotification,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    const sendTelegramNetworkError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNetworkError]\": async (isDisconnected)=>{\n            const timestamp = new Date().toLocaleString();\n            const status = isDisconnected ? 'DISCONNECTED' : 'RECONNECTED';\n            const emoji = isDisconnected ? '🔴' : '🟢';\n            const message = \"\".concat(emoji, \" <b>NETWORK \").concat(status, \"</b>\\n\\n\") + \"\\uD83D\\uDCE1 Status: \".concat(isDisconnected ? 'Connection lost' : 'Connection restored', \"\\n\") + \"\\uD83E\\uDD16 Bot Action: \".concat(isDisconnected ? 'Stopped and session saved' : 'Ready to resume', \"\\n\") + \"\\uD83D\\uDD50 Time: \".concat(timestamp);\n            return await sendTelegramNotification(message, isDisconnected);\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNetworkError]\"], [\n        sendTelegramNotification\n    ]);\n    // Initialize fetchMarketPrice after Telegram functions\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                const price = await getMarketPriceFromAPI(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n                // Send Telegram notification for price fetch failure\n                sendTelegramErrorNotification('Price Fetch Error', \"Failed to fetch market price for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2), \"Error: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n                // Use fallback price calculation\n                const fallbackPrice = calculateFallbackMarketPrice(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: fallbackPrice\n                });\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch,\n        sendTelegramErrorNotification\n    ]);\n    // Real-time market price fetching effect - gets actual prices from Binance\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Don't fetch if cryptos are not selected\n            if (!state.config.crypto1 || !state.config.crypto2) {\n                return;\n            }\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up real price fetching interval - hardcoded to 2 seconds\n            const priceUpdateInterval = setInterval({\n                \"TradingProvider.useEffect.priceUpdateInterval\": ()=>{\n                    // Only fetch real price if online and cryptos are selected\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline && state.config.crypto1 && state.config.crypto2) {\n                        fetchMarketPrice(); // Fetch real price from Binance API\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceUpdateInterval\"], 2000); // Fixed at 2 seconds as requested\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceUpdateInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2,\n        fetchMarketPrice\n    ]);\n    // Other effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            if (state.appSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && state.appSettings.alertOnOrderExecution) {\n                    soundPath = state.appSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && state.appSettings.alertOnError) {\n                    soundPath = state.appSettings.soundError;\n                }\n                if (soundPath) {\n                    audioRef.current.src = soundPath;\n                    audioRef.current.currentTime = 0; // Reset to beginning\n                    // Play the sound and limit duration to 2 seconds\n                    audioRef.current.play().then({\n                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                            // Set a timeout to pause the audio after 2 seconds\n                            setTimeout({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.pause();\n                                        audioRef.current.currentTime = 0; // Reset for next play\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                        }\n                    }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                        \"TradingProvider.useCallback[playSound]\": (err)=>console.error(\"Error playing sound:\", err)\n                    }[\"TradingProvider.useCallback[playSound]\"]);\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Session-based alarm playing function\n    const playSessionAlarm = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSessionAlarm]\": (alarmType)=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (!currentSessionId) return;\n            try {\n                const alarmSettings = localStorage.getItem(\"session_alarm_\".concat(currentSessionId));\n                if (!alarmSettings) return;\n                const settings = JSON.parse(alarmSettings);\n                const isEnabled = alarmType === 'success' ? settings.successAlarmsEnabled : settings.errorAlarmsEnabled;\n                if (!isEnabled) return;\n                const soundType = alarmType === 'success' ? settings.successSoundType : settings.errorSoundType;\n                let soundUrl = '';\n                // Use built-in sounds with correct file extensions\n                const soundExtensions = {\n                    'G_hades_curse': 'wav',\n                    'G_hades_demat': 'wav',\n                    'G_hades_mat': 'wav',\n                    'G_hades_sanctify': 'wav',\n                    'S_mon1': 'mp3',\n                    'S_mon2': 'mp3',\n                    'Satyr_atk4': 'wav',\n                    'bells': 'wav',\n                    'bird1': 'wav',\n                    'bird7': 'wav',\n                    'cheer': 'wav',\n                    'chest1': 'wav',\n                    'chime2': 'wav',\n                    'dark2': 'wav',\n                    'foundry2': 'wav',\n                    'goatherd1': 'wav',\n                    'marble1': 'wav',\n                    'sanctuary1': 'wav',\n                    'space_bells4a': 'wav',\n                    'sparrow1': 'wav',\n                    'tax3': 'wav',\n                    'wolf4': 'wav'\n                };\n                const extension = soundExtensions[soundType] || 'wav';\n                soundUrl = \"/sounds/\".concat(soundType, \".\").concat(extension);\n                if (audioRef.current && soundUrl) {\n                    audioRef.current.src = soundUrl;\n                    audioRef.current.currentTime = 0;\n                    audioRef.current.play().then({\n                        \"TradingProvider.useCallback[playSessionAlarm]\": ()=>{\n                            setTimeout({\n                                \"TradingProvider.useCallback[playSessionAlarm]\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.pause();\n                                        audioRef.current.currentTime = 0;\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSessionAlarm]\"], 2000);\n                        }\n                    }[\"TradingProvider.useCallback[playSessionAlarm]\"]).catch({\n                        \"TradingProvider.useCallback[playSessionAlarm]\": (error)=>{\n                            console.error('Failed to play session alarm:', error);\n                        }\n                    }[\"TradingProvider.useCallback[playSessionAlarm]\"]);\n                }\n            } catch (error) {\n                console.error('Error playing session alarm:', error);\n            }\n        }\n    }[\"TradingProvider.useCallback[playSessionAlarm]\"], []);\n    // Duplicate Telegram functions removed - using the ones defined above\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                playSessionAlarm('success');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram error notification\n                                console.warn(\"⚠️ Insufficient \".concat(config.crypto2, \" balance for BUY order. Required: \").concat(costCrypto2.toFixed(6), \", Available: \").concat(currentCrypto2Balance.toFixed(6)));\n                                sendTelegramBalanceWarning(\"\".concat(config.crypto2, \" (BUY Order)\"), currentCrypto2Balance, costCrypto2);\n                                // Play error alarm\n                                playSessionAlarm('error');\n                                // Also send general error notification\n                                sendTelegramErrorNotification('Insufficient Balance', \"Cannot execute BUY order for counter \".concat(activeRow.counter), \"Required: \".concat(costCrypto2.toFixed(6), \" \").concat(config.crypto2, \", Available: \").concat(currentCrypto2Balance.toFixed(6), \" \").concat(config.crypto2));\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            playSessionAlarm('success');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Get real market price for Crypto2/Stablecoin pair (synchronous for now)\n                                const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Get real market price for Crypto1/Stablecoin pair\n                                const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            const realizedProfitInCrypto2 = crypto2Reacquired - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 * config.incomeSplitCrypto1Percent / 100 / crypto1StablecoinPrice : 0;\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(config.preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            playSessionAlarm('success');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                // Send Telegram error notification\n                sendTelegramErrorNotification('Bot Start Failed', \"Failed to start trading bot on backend\", \"Config ID: \".concat(configId, \", Error: \").concat(error instanceof Error ? error.message : 'Unknown error'));\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast,\n        sendTelegramErrorNotification\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                // Send Telegram error notification\n                sendTelegramErrorNotification('Bot Stop Failed', \"Failed to stop trading bot on backend\", \"Config ID: \".concat(configId, \", Error: \").concat(error instanceof Error ? error.message : 'Unknown error'));\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast,\n        sendTelegramErrorNotification\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Session restoration effect - runs once on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const restoreSession = {\n                \"TradingProvider.useEffect.restoreSession\": async ()=>{\n                    try {\n                        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                        // Wait for session manager to initialize properly\n                        await new Promise({\n                            \"TradingProvider.useEffect.restoreSession\": (resolve)=>setTimeout(resolve, 1500)\n                        }[\"TradingProvider.useEffect.restoreSession\"]);\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            const session = sessionManager.loadSession(currentSessionId);\n                            if (session) {\n                                console.log('🔄 Restoring session from backend/storage:', currentSessionId, session.name);\n                                // Restore session data to context in the correct order\n                                dispatch({\n                                    type: 'SET_CONFIG',\n                                    payload: session.config\n                                });\n                                // Wait a moment for config to be set before setting other data\n                                await new Promise({\n                                    \"TradingProvider.useEffect.restoreSession\": (resolve)=>setTimeout(resolve, 100)\n                                }[\"TradingProvider.useEffect.restoreSession\"]);\n                                dispatch({\n                                    type: 'SET_TARGET_PRICE_ROWS',\n                                    payload: session.targetPriceRows\n                                });\n                                dispatch({\n                                    type: 'SET_MARKET_PRICE',\n                                    payload: session.currentMarketPrice\n                                });\n                                dispatch({\n                                    type: 'SET_BALANCES',\n                                    payload: {\n                                        crypto1: session.crypto1Balance,\n                                        crypto2: session.crypto2Balance\n                                    }\n                                });\n                                // Restore order history\n                                dispatch({\n                                    type: 'CLEAR_ORDER_HISTORY'\n                                });\n                                session.orderHistory.forEach({\n                                    \"TradingProvider.useEffect.restoreSession\": (entry)=>{\n                                        dispatch({\n                                            type: 'ADD_ORDER_HISTORY_ENTRY',\n                                            payload: entry\n                                        });\n                                    }\n                                }[\"TradingProvider.useEffect.restoreSession\"]);\n                                // If session was active (running), restore bot status after a delay\n                                if (session.isActive) {\n                                    console.log('🤖 Restoring active bot session - will start in 2 seconds');\n                                    setTimeout({\n                                        \"TradingProvider.useEffect.restoreSession\": ()=>{\n                                            dispatch({\n                                                type: 'SYSTEM_START_BOT_INITIATE'\n                                            });\n                                            console.log('🤖 Active bot session restored and started');\n                                        }\n                                    }[\"TradingProvider.useEffect.restoreSession\"], 2000);\n                                }\n                                console.log('✅ Session restored successfully');\n                                // Send Telegram notification about session restoration\n                                sendTelegramNotification(\"\\uD83D\\uDD04 <b>SESSION RESTORED</b>\\n\\n\" + \"\\uD83D\\uDCDD Session: \".concat(session.name, \"\\n\") + \"\\uD83D\\uDCCA Pair: \".concat(session.config.crypto1, \"/\").concat(session.config.crypto2, \"\\n\") + \"\\uD83D\\uDCB0 Balance: \".concat(session.crypto1Balance.toFixed(6), \" \").concat(session.config.crypto1, \", \").concat(session.crypto2Balance.toFixed(2), \" \").concat(session.config.crypto2, \"\\n\") + \"\\uD83E\\uDD16 Status: \".concat(session.isActive ? 'Will resume trading' : 'Stopped', \"\\n\") + \"\\uD83D\\uDD50 Time: \".concat(new Date().toLocaleString()));\n                            } else {\n                                console.log('No session found to restore');\n                            }\n                        } else {\n                            console.log('No current session ID found');\n                        }\n                    } catch (error) {\n                        console.error('Failed to restore session:', error);\n                        // Send Telegram error notification\n                        sendTelegramErrorNotification('Session Restoration Failed', 'Failed to restore session after page refresh', \"Error: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n                    }\n                }\n            }[\"TradingProvider.useEffect.restoreSession\"];\n            restoreSession();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        sendTelegramNotification,\n        sendTelegramErrorNotification\n    ]); // Run only once on mount\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Auto-save session periodically and on page unload\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Auto-save every 30 seconds if bot is running\n            const autoSaveInterval = setInterval({\n                \"TradingProvider.useEffect.autoSaveInterval\": ()=>{\n                    if (state.botSystemStatus === 'Running') {\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, true // isActive\n                            );\n                            console.log('💾 Auto-saved running session');\n                        }\n                    }\n                }\n            }[\"TradingProvider.useEffect.autoSaveInterval\"], 30000); // Every 30 seconds\n            // Save session before page unload\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": ()=>{\n                    const currentSessionId = sessionManager.getCurrentSessionId();\n                    if (currentSessionId) {\n                        sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        console.log('💾 Session saved before page unload');\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(autoSaveInterval);\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config,\n        state.targetPriceRows,\n        state.orderHistory,\n        state.currentMarketPrice,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot starts if none exists and handle runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Check if we need to create a session when bot starts\n            if (state.botSystemStatus === 'WarmingUp' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            stopBackendBot();\n                        }\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config).then({\n                            \"TradingProvider.useEffect\": (savedSessionId)=>{\n                                sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Create new session for the new crypto pair\n                    if (state.config.crypto1 && state.config.crypto2) {\n                        sessionManager.createNewSessionWithAutoName(state.config).then({\n                            \"TradingProvider.useEffect\": (newSessionId)=>{\n                                sessionManager.setCurrentSession(newSessionId);\n                                console.log('🆕 Created new session for crypto pair:', state.config.crypto1, '/', state.config.crypto2);\n                                toast({\n                                    title: \"Crypto Pair Changed\",\n                                    description: \"Previous session AutoSaved. New session created for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2),\n                                    duration: 5000\n                                });\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Send Telegram notification about network disconnection\n                            sendTelegramNetworkError(true);\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": (backupSessionId)=>{\n                                            sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"Bot stopped and session saved. Trading paused until connection restored.\",\n                            variant: \"destructive\",\n                            duration: 8000\n                        });\n                    } else if (isOnline && !isInitial) {\n                        // Send Telegram notification about network reconnection\n                        sendTelegramNetworkError(false);\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. You can resume trading.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, create one first\n                    if (state.config.crypto1 && state.config.crypto2) {\n                        sessionManager.createNewSessionWithAutoName(state.config).then({\n                            \"TradingProvider.useCallback[saveCurrentSession]\": (sessionId)=>{\n                                sessionManager.setCurrentSession(sessionId);\n                                sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                            }\n                        }[\"TradingProvider.useCallback[saveCurrentSession]\"]);\n                        return true;\n                    }\n                    return false;\n                }\n                // Save existing session\n                return sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1903,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"/766vIVhZGXVNjjANRCjB84ct4w=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});