import os
import sys
import logging
from flask import request
from app import create_app, socketio

# Configure more verbose logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Add route tracing for debugging
app = create_app()

@app.before_request
def log_request_info():
    """Log request headers for debugging connection issues."""
    logger.debug('Headers: %s', dict(request.headers))
    logger.debug('Body: %s', request.get_data())

@app.after_request
def after_request(response):
    """Log response for debugging."""
    logger.debug('Response: %s', response.status)
    return response

if __name__ == '__main__':
    logger.info("=== STARTING BACKEND SERVER IN DEBUG MODE ===")
    logger.info("Server will be available at http://localhost:5000")
    logger.info("API endpoints will be at http://localhost:5000/api/*")
    logger.info("CORS is configured to allow all origins")
    logger.info("===============================================")
    
    # Run with debug mode and host on all interfaces
    socketio.run(app, host='0.0.0.0', port=5000, debug=True, allow_unsafe_werkzeug=True) 