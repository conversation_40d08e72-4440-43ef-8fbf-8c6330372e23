"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/MarketPriceDisplay.tsx":
/*!*********************************************************!*\
  !*** ./src/components/dashboard/MarketPriceDisplay.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarketPriceDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _barrel_optimize_names_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingUp!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction MarketPriceDisplay() {\n    _s();\n    const { config, currentMarketPrice } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    // Format price with appropriate precision based on value\n    const formatPrice = (price)=>{\n        if (price === 0) return \"0\";\n        if (price < 0.001) return price.toFixed(8);\n        if (price < 1) return price.toFixed(6);\n        if (price < 100) return price.toFixed(4);\n        return price.toFixed(2);\n    };\n    // Check if both cryptos are selected\n    const hasBothCryptos = config.crypto1 && config.crypto2;\n    const displayPair = hasBothCryptos ? \"\".concat(config.crypto1, \"/\").concat(config.crypto2) : \"Crypto 1/Crypto 2\";\n    const displayPrice = hasBothCryptos && currentMarketPrice > 0 ? formatPrice(currentMarketPrice) : \"0\";\n    // Show trading mode context\n    const modeText = config.tradingMode === 'StablecoinSwap' ? 'Swap Mode' : 'Spot Mode';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4 p-3 bg-gradient-to-r from-green-500/10 to-primary/10 border border-border rounded-md\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"h-5 w-5 text-green-500\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm font-medium text-muted-foreground\",\n                            children: [\n                                \"Current Market Price (\",\n                                modeText,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg font-semibold text-foreground\",\n                            children: [\n                                displayPair,\n                                \" =\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-2xl font-bold text-primary\",\n                            children: displayPrice\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        hasBothCryptos && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-muted-foreground\",\n                            children: config.crypto2\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\dashboard\\\\MarketPriceDisplay.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n_s(MarketPriceDisplay, \"b4ROlSOR9OZ7niaJVf3M+kC2fh8=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext\n    ];\n});\n_c = MarketPriceDisplay;\nvar _c;\n$RefreshReg$(_c, \"MarketPriceDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/MarketPriceDisplay.tsx\n"));

/***/ })

});