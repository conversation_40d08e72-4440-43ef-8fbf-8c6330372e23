"use client";

import React from 'react';
import { useTradingContext } from '@/contexts/TradingContext';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ScrollArea, ScrollBar } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import type { DisplayOrderRow } from '@/lib/types';
import { cn } from '@/lib/utils';

export default function OrdersTable() {
  const { getDisplayOrders, config, currentMarketPrice } = useTradingContext();
  const displayOrders = getDisplayOrders();

  const formatNumber = (num?: number, forceSign = false) => {
    if (num === undefined || num === null || isNaN(num)) return "-";
    const fixedNum = num.toFixed(config.numDigits);
    if (forceSign && num > 0) return `+${fixedNum}`;
    return fixedNum;
  };
  
  const formatPercent = (num?: number) => {
    if (num === undefined || num === null || isNaN(num)) return "-";
    return `${num.toFixed(2)}%`;
  }

  const columns = [
    { key: "#", label: "#" },
    { key: "status", label: "Status" },
    { key: "orderLevel", label: "Level" },
    { key: "valueLevel", label: "Value" },
    { key: "crypto2Var", label: `${config.crypto2 || "Crypto 2"} Var.` },
    { key: "crypto1Var", label: `${config.crypto1 || "Crypto 1"} Var.` },
    { key: "targetPrice", label: "Target Price" },
    { key: "percentFromActualPrice", label: "% from Actual" },
    { key: "incomeCrypto1", label: `Income ${config.crypto1 || "Crypto 1"}` },
    { key: "incomeCrypto2", label: `Income ${config.crypto2 || "Crypto 2"}` },
    { key: "originalCostCrypto2", label: `Original Cost ${config.crypto2 || "Crypto 2"}` },
  ];

  return (
    <div className="border-2 border-border rounded-sm">
      <ScrollArea className="w-full whitespace-nowrap">
        <Table className="min-w-full">
          <TableHeader>
            <TableRow className="bg-card hover:bg-card">
              {columns.map((col) => (
                <TableHead key={col.key} className="font-bold text-foreground whitespace-nowrap px-3 py-2 text-sm">
                  {col.label}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {displayOrders.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center text-muted-foreground">
                  No target prices set. Use "Set Target Prices" in the sidebar.
                </TableCell>
              </TableRow>
            ) : (
              displayOrders.map((row: DisplayOrderRow) => (
                <TableRow key={row.id} className="hover:bg-card/80">
                  <TableCell className="px-3 py-2 text-xs">{row.counter}</TableCell>
                  <TableCell className="px-3 py-2 text-xs">
                    <Badge variant={row.status === "Full" ? "default" : "secondary"}
                           className={cn(row.status === "Full" ? "bg-green-600 text-white" : "bg-yellow-500 text-black", "font-bold")}>
                      {row.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="px-3 py-2 text-xs">{row.orderLevel}</TableCell>
                  <TableCell className="px-3 py-2 text-xs">{formatNumber(row.valueLevel)}</TableCell>
                  <TableCell className={cn("px-3 py-2 text-xs", row.crypto2Var && row.crypto2Var < 0 ? "text-destructive" : "text-green-400")}>
                    {formatNumber(row.crypto2Var, true)}
                  </TableCell>
                  <TableCell className={cn("px-3 py-2 text-xs", row.crypto1Var && row.crypto1Var < 0 ? "text-destructive" : "text-green-400")}>
                    {formatNumber(row.crypto1Var, true)}
                  </TableCell>
                  <TableCell className="px-3 py-2 text-xs font-semibold text-primary">{formatNumber(row.targetPrice)}</TableCell>
                  <TableCell className={cn("px-3 py-2 text-xs", row.percentFromActualPrice < 0 ? "text-destructive" : "text-green-400")}>
                    {formatPercent(row.percentFromActualPrice)}
                  </TableCell>
                   <TableCell className={cn("px-3 py-2 text-xs", row.incomeCrypto1 && row.incomeCrypto1 < 0 ? "text-destructive" : "text-green-400")}>
                    {formatNumber(row.incomeCrypto1)}
                  </TableCell>
                  <TableCell className={cn("px-3 py-2 text-xs", row.incomeCrypto2 && row.incomeCrypto2 < 0 ? "text-destructive" : "text-green-400")}>
                    {formatNumber(row.incomeCrypto2)}
                  </TableCell>
                  <TableCell className="px-3 py-2 text-xs">{formatNumber(row.originalCostCrypto2)}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        <ScrollBar orientation="horizontal" />
      </ScrollArea>
    </div>
  );
}
