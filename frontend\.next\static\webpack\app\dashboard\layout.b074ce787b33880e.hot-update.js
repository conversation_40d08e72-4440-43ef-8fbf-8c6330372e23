"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/contexts/TradingContext.tsx":
/*!*****************************************!*\
  !*** ./src/contexts/TradingContext.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TradingProvider: () => (/* binding */ TradingProvider),\n/* harmony export */   useTradingContext: () => (/* binding */ useTradingContext)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! uuid */ \"(app-pages-browser)/./node_modules/uuid/dist/esm-browser/v4.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/network-monitor */ \"(app-pages-browser)/./src/lib/network-monitor.ts\");\n/* __next_internal_client_entry_do_not_use__ TradingProvider,useTradingContext auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n\n\n\n\n// Add this function to calculate the initial market price\nconst calculateInitialMarketPrice = (config)=>{\n    // Return 0 if either crypto is not selected\n    if (!config.crypto1 || !config.crypto2) {\n        return 0;\n    }\n    // Default fallback value for valid pairs\n    return calculateFallbackMarketPrice(config);\n};\n// Enhanced API function to get market price for any trading pair\nconst getMarketPriceFromAPI = async (config)=>{\n    try {\n        // Return 0 if either crypto is not selected\n        if (!config.crypto1 || !config.crypto2) {\n            return 0;\n        }\n        // For both SimpleSpot and StablecoinSwap modes, calculate crypto1/crypto2 price\n        const symbol = \"\".concat(config.crypto1).concat(config.crypto2).toUpperCase();\n        // First try Binance API with proper error handling\n        try {\n            const controller = new AbortController();\n            const timeoutId = setTimeout(()=>controller.abort(), 5000); // 5 second timeout\n            const response = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(symbol), {\n                signal: controller.signal,\n                headers: {\n                    'Accept': 'application/json'\n                }\n            });\n            clearTimeout(timeoutId);\n            if (response.ok) {\n                const data = await response.json();\n                const price = parseFloat(data.price);\n                if (price > 0 && !isNaN(price)) {\n                    console.log(\"✅ Binance price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price.toFixed(6)));\n                    return price;\n                }\n            } else if (response.status === 400) {\n                // Symbol not found on Binance, try reverse pair\n                const reverseSymbol = \"\".concat(config.crypto2).concat(config.crypto1).toUpperCase();\n                const reverseResponse = await fetch(\"https://api.binance.com/api/v3/ticker/price?symbol=\".concat(reverseSymbol), {\n                    headers: {\n                        'Accept': 'application/json'\n                    }\n                });\n                if (reverseResponse.ok) {\n                    const reverseData = await reverseResponse.json();\n                    const reversePrice = parseFloat(reverseData.price);\n                    if (reversePrice > 0 && !isNaN(reversePrice)) {\n                        const price = 1 / reversePrice;\n                        console.log(\"✅ Binance reverse price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price.toFixed(6), \" (from \").concat(reverseSymbol, \")\"));\n                        return price;\n                    }\n                }\n            }\n        } catch (binanceError) {\n            console.warn('Binance API failed:', binanceError);\n        }\n        // Try CoinGecko API as fallback\n        try {\n            const crypto1Id = getCoinGeckoId(config.crypto1);\n            const crypto2Id = getCoinGeckoId(config.crypto2);\n            if (crypto1Id && crypto2Id) {\n                const controller = new AbortController();\n                const timeoutId = setTimeout(()=>controller.abort(), 5000);\n                const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(crypto1Id, \"&vs_currencies=\").concat(crypto2Id), {\n                    signal: controller.signal,\n                    headers: {\n                        'Accept': 'application/json'\n                    }\n                });\n                clearTimeout(timeoutId);\n                if (response.ok) {\n                    var _data_crypto1Id;\n                    const data = await response.json();\n                    const price = (_data_crypto1Id = data[crypto1Id]) === null || _data_crypto1Id === void 0 ? void 0 : _data_crypto1Id[crypto2Id];\n                    if (price > 0 && !isNaN(price)) {\n                        console.log(\"✅ CoinGecko price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(price.toFixed(6)));\n                        return price;\n                    }\n                }\n            }\n        } catch (geckoError) {\n            console.warn('CoinGecko API failed:', geckoError);\n        }\n        // Final fallback to calculated price using USD values\n        const mockPrice = calculateFallbackMarketPrice(config);\n        console.log(\"⚠️ Using calculated price: \".concat(config.crypto1, \"/\").concat(config.crypto2, \" = \").concat(mockPrice.toFixed(6)));\n        return mockPrice;\n    } catch (error) {\n        console.error('Error fetching market price:', error);\n        return calculateFallbackMarketPrice(config);\n    }\n};\n// Helper function to map crypto symbols to CoinGecko IDs\nconst getCoinGeckoId = (symbol)=>{\n    const mapping = {\n        'BTC': 'bitcoin',\n        'ETH': 'ethereum',\n        'SOL': 'solana',\n        'ADA': 'cardano',\n        'DOT': 'polkadot',\n        'MATIC': 'matic-network',\n        'AVAX': 'avalanche-2',\n        'LINK': 'chainlink',\n        'UNI': 'uniswap',\n        'USDT': 'tether',\n        'USDC': 'usd-coin',\n        'BUSD': 'binance-usd',\n        'DAI': 'dai'\n    };\n    return mapping[symbol.toUpperCase()] || null;\n};\n// Helper function to get stablecoin exchange rates for real market data\nconst getStablecoinExchangeRate = async (crypto, stablecoin)=>{\n    try {\n        // For stablecoin-to-stablecoin, assume 1:1 rate\n        if (getCoinGeckoId(crypto) && getCoinGeckoId(stablecoin)) {\n            const cryptoId = getCoinGeckoId(crypto);\n            const stablecoinId = getCoinGeckoId(stablecoin);\n            if (cryptoId === stablecoinId) return 1.0; // Same currency\n            // Get real exchange rate from CoinGecko\n            const response = await fetch(\"https://api.coingecko.com/api/v3/simple/price?ids=\".concat(cryptoId, \"&vs_currencies=\").concat(stablecoinId));\n            if (response.ok) {\n                var _data_cryptoId;\n                const data = await response.json();\n                const rate = cryptoId && stablecoinId ? (_data_cryptoId = data[cryptoId]) === null || _data_cryptoId === void 0 ? void 0 : _data_cryptoId[stablecoinId] : null;\n                if (rate > 0) {\n                    console.log(\"\\uD83D\\uDCCA Stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate));\n                    return rate;\n                }\n            }\n        }\n        // Fallback: calculate via USD prices\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        const rate = cryptoUSDPrice / stablecoinUSDPrice;\n        console.log(\"\\uD83D\\uDCCA Fallback stablecoin rate: \".concat(crypto, \"/\").concat(stablecoin, \" = \").concat(rate, \" (via USD)\"));\n        return rate;\n    } catch (error) {\n        console.error('Error fetching stablecoin exchange rate:', error);\n        // Final fallback\n        const cryptoUSDPrice = getUSDPrice(crypto);\n        const stablecoinUSDPrice = getUSDPrice(stablecoin);\n        return cryptoUSDPrice / stablecoinUSDPrice;\n    }\n};\n// Helper function to get USD price (extracted from calculateFallbackMarketPrice)\nconst getUSDPrice = (crypto)=>{\n    const usdPrices = {\n        // Major cryptocurrencies\n        'BTC': 109000,\n        'ETH': 4000,\n        'SOL': 240,\n        'ADA': 1.2,\n        'DOGE': 0.4,\n        'LINK': 25,\n        'MATIC': 0.5,\n        'DOT': 8,\n        'AVAX': 45,\n        'SHIB': 0.000030,\n        'XRP': 2.5,\n        'LTC': 110,\n        'BCH': 500,\n        // DeFi tokens\n        'UNI': 15,\n        'AAVE': 180,\n        'MKR': 1800,\n        'SNX': 3.5,\n        'COMP': 85,\n        'YFI': 8500,\n        'SUSHI': 2.1,\n        '1INCH': 0.65,\n        'CRV': 0.85,\n        'UMA': 3.2,\n        // Layer 1 blockchains\n        'ATOM': 12,\n        'NEAR': 6.5,\n        'ALGO': 0.35,\n        'ICP': 14,\n        'HBAR': 0.28,\n        'APT': 12.5,\n        'TON': 5.8,\n        'FTM': 0.95,\n        'ONE': 0.025,\n        // Other popular tokens\n        'FIL': 8.5,\n        'TRX': 0.25,\n        'ETC': 35,\n        'VET': 0.055,\n        'QNT': 125,\n        'LDO': 2.8,\n        'CRO': 0.18,\n        'LUNC': 0.00015,\n        // Gaming & Metaverse\n        'MANA': 0.85,\n        'SAND': 0.75,\n        'AXS': 8.5,\n        'ENJ': 0.45,\n        'CHZ': 0.12,\n        // Infrastructure & Utility\n        'THETA': 2.1,\n        'FLOW': 1.2,\n        'XTZ': 1.8,\n        'EOS': 1.1,\n        'GRT': 0.28,\n        'BAT': 0.35,\n        // Privacy coins\n        'ZEC': 45,\n        'DASH': 35,\n        // DEX tokens\n        'LRC': 0.45,\n        'ZRX': 0.65,\n        'KNC': 0.85,\n        // Other tokens\n        'REN': 0.15,\n        'BAND': 2.5,\n        'STORJ': 0.85,\n        'NMR': 25,\n        'ANT': 8.5,\n        'BNT': 0.95,\n        'MLN': 35,\n        'REP': 15,\n        // Smaller cap tokens\n        'IOTX': 0.065,\n        'ZIL': 0.045,\n        'ICX': 0.35,\n        'QTUM': 4.5,\n        'ONT': 0.45,\n        'WAVES': 3.2,\n        'LSK': 1.8,\n        'NANO': 1.5,\n        'SC': 0.008,\n        'DGB': 0.025,\n        'RVN': 0.035,\n        'BTT': 0.0000015,\n        'WIN': 0.00015,\n        'HOT': 0.0035,\n        'DENT': 0.0018,\n        'NPXS': 0.00085,\n        'FUN': 0.0085,\n        'CELR': 0.025,\n        // Stablecoins\n        'USDT': 1.0,\n        'USDC': 1.0,\n        'FDUSD': 1.0,\n        'BUSD': 1.0,\n        'DAI': 1.0\n    };\n    return usdPrices[crypto.toUpperCase()] || 100;\n};\n// Enhanced fallback function for market price calculation supporting all trading pairs\nconst calculateFallbackMarketPrice = (config)=>{\n    const crypto1USDPrice = getUSDPrice(config.crypto1);\n    const crypto2USDPrice = getUSDPrice(config.crypto2);\n    // Calculate the ratio: crypto1/crypto2 = (crypto1 USD price) / (crypto2 USD price)\n    // This gives us how many units of crypto2 equals 1 unit of crypto1\n    const basePrice = crypto1USDPrice / crypto2USDPrice;\n    // Add small random fluctuation for realistic simulation\n    const fluctuation = (Math.random() - 0.5) * 0.01; // ±0.5% for more stable prices\n    const finalPrice = basePrice * (1 + fluctuation);\n    // Ensure price is positive and reasonable\n    const validPrice = Math.max(finalPrice, 0.000001);\n    console.log(\"\\uD83D\\uDCCA Calculated price: \".concat(config.crypto1, \" ($\").concat(crypto1USDPrice.toFixed(2), \") / \").concat(config.crypto2, \" ($\").concat(crypto2USDPrice.toFixed(2), \") = \").concat(validPrice.toFixed(8)));\n    return validPrice;\n};\nconst initialBaseConfig = {\n    tradingMode: \"SimpleSpot\",\n    crypto1: \"\",\n    crypto2: \"\",\n    baseBid: 100,\n    multiplier: 1.005,\n    numDigits: 4,\n    slippagePercent: 0.2,\n    incomeSplitCrypto1Percent: 50,\n    incomeSplitCrypto2Percent: 50,\n    preferredStablecoin: _lib_types__WEBPACK_IMPORTED_MODULE_2__.AVAILABLE_STABLECOINS[0]\n};\nconst initialTradingState = {\n    config: initialBaseConfig,\n    targetPriceRows: [],\n    orderHistory: [],\n    appSettings: _lib_types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_APP_SETTINGS,\n    currentMarketPrice: calculateInitialMarketPrice(initialBaseConfig),\n    // isBotActive: false, // Remove\n    botSystemStatus: 'Stopped',\n    crypto1Balance: 10,\n    crypto2Balance: 100000,\n    stablecoinBalance: 0,\n    backendStatus: 'unknown'\n};\nconst lastActionTimestampPerCounter = new Map();\n// LocalStorage persistence functions\nconst STORAGE_KEY = 'pluto_trading_state';\nconst saveStateToLocalStorage = (state)=>{\n    try {\n        if (true) {\n            const stateToSave = {\n                config: state.config,\n                targetPriceRows: state.targetPriceRows,\n                orderHistory: state.orderHistory,\n                appSettings: state.appSettings,\n                currentMarketPrice: state.currentMarketPrice,\n                crypto1Balance: state.crypto1Balance,\n                crypto2Balance: state.crypto2Balance,\n                stablecoinBalance: state.stablecoinBalance,\n                botSystemStatus: state.botSystemStatus,\n                timestamp: Date.now()\n            };\n            localStorage.setItem(STORAGE_KEY, JSON.stringify(stateToSave));\n        }\n    } catch (error) {\n        console.error('Failed to save state to localStorage:', error);\n    }\n};\nconst loadStateFromLocalStorage = ()=>{\n    try {\n        if (true) {\n            const savedState = localStorage.getItem(STORAGE_KEY);\n            if (savedState) {\n                const parsed = JSON.parse(savedState);\n                // Check if state is not too old (24 hours)\n                if (parsed.timestamp && Date.now() - parsed.timestamp < 24 * 60 * 60 * 1000) {\n                    return parsed;\n                }\n            }\n        }\n    } catch (error) {\n        console.error('Failed to load state from localStorage:', error);\n    }\n    return null;\n};\nconst tradingReducer = (state, action)=>{\n    switch(action.type){\n        case 'SET_CONFIG':\n            const newConfig = {\n                ...state.config,\n                ...action.payload\n            };\n            // If trading pair changes, reset market price (it will be re-calculated by effect)\n            if (action.payload.crypto1 || action.payload.crypto2) {\n                return {\n                    ...state,\n                    config: newConfig,\n                    currentMarketPrice: calculateInitialMarketPrice(newConfig)\n                };\n            }\n            return {\n                ...state,\n                config: newConfig\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload.sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }))\n            };\n        case 'ADD_TARGET_PRICE_ROW':\n            {\n                const newRows = [\n                    ...state.targetPriceRows,\n                    action.payload\n                ].sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: newRows\n                };\n            }\n        case 'UPDATE_TARGET_PRICE_ROW':\n            {\n                const updatedRows = state.targetPriceRows.map((row)=>row.id === action.payload.id ? action.payload : row).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: updatedRows\n                };\n            }\n        case 'REMOVE_TARGET_PRICE_ROW':\n            {\n                const filteredRows = state.targetPriceRows.filter((row)=>row.id !== action.payload).sort((a, b)=>a.targetPrice - b.targetPrice).map((row, index)=>({\n                        ...row,\n                        counter: index + 1\n                    }));\n                return {\n                    ...state,\n                    targetPriceRows: filteredRows\n                };\n            }\n        case 'ADD_ORDER_HISTORY_ENTRY':\n            return {\n                ...state,\n                orderHistory: [\n                    action.payload,\n                    ...state.orderHistory\n                ]\n            };\n        case 'CLEAR_ORDER_HISTORY':\n            return {\n                ...state,\n                orderHistory: []\n            };\n        case 'SET_APP_SETTINGS':\n            return {\n                ...state,\n                appSettings: {\n                    ...state.appSettings,\n                    ...action.payload\n                }\n            };\n        case 'SET_MARKET_PRICE':\n            return {\n                ...state,\n                currentMarketPrice: action.payload\n            };\n        case 'FLUCTUATE_MARKET_PRICE':\n            {\n                if (state.currentMarketPrice <= 0) return state;\n                // More realistic fluctuation: smaller, more frequent changes\n                const fluctuationFactor = (Math.random() - 0.5) * 0.006; // Approx +/- 0.3% per update\n                const newPrice = state.currentMarketPrice * (1 + fluctuationFactor);\n                return {\n                    ...state,\n                    currentMarketPrice: newPrice > 0 ? newPrice : state.currentMarketPrice\n                };\n            }\n        // case 'SET_BOT_STATUS': // Removed\n        case 'UPDATE_BALANCES':\n            return {\n                ...state,\n                crypto1Balance: action.payload.crypto1 !== undefined ? action.payload.crypto1 : state.crypto1Balance,\n                crypto2Balance: action.payload.crypto2 !== undefined ? action.payload.crypto2 : state.crypto2Balance,\n                stablecoinBalance: action.payload.stablecoin !== undefined ? action.payload.stablecoin : state.stablecoinBalance\n            };\n        case 'UPDATE_STABLECOIN_BALANCE':\n            return {\n                ...state,\n                stablecoinBalance: action.payload\n            };\n        case 'RESET_SESSION':\n            const configForReset = {\n                ...state.config\n            };\n            return {\n                ...initialTradingState,\n                config: configForReset,\n                appSettings: {\n                    ...state.appSettings\n                },\n                currentMarketPrice: calculateInitialMarketPrice(configForReset)\n            };\n        case 'SET_BACKEND_STATUS':\n            return {\n                ...state,\n                backendStatus: action.payload\n            };\n        case 'SYSTEM_START_BOT_INITIATE':\n            // Continue from previous state instead of resetting\n            return {\n                ...state,\n                botSystemStatus: 'WarmingUp'\n            };\n        case 'SYSTEM_COMPLETE_WARMUP':\n            return {\n                ...state,\n                botSystemStatus: 'Running'\n            };\n        case 'SYSTEM_STOP_BOT':\n            return {\n                ...state,\n                botSystemStatus: 'Stopped'\n            };\n        case 'SYSTEM_RESET_BOT':\n            // Fresh Start: Clear all target price rows and order history completely\n            lastActionTimestampPerCounter.clear();\n            // Clear current session to force new session name generation\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            sessionManager.clearCurrentSession();\n            return {\n                ...state,\n                botSystemStatus: 'Stopped',\n                targetPriceRows: [],\n                orderHistory: []\n            };\n        case 'SET_TARGET_PRICE_ROWS':\n            return {\n                ...state,\n                targetPriceRows: action.payload\n            };\n        case 'RESET_FOR_NEW_CRYPTO':\n            return {\n                ...initialTradingState,\n                config: state.config,\n                backendStatus: state.backendStatus,\n                botSystemStatus: 'Stopped',\n                currentMarketPrice: 0\n            };\n        default:\n            return state;\n    }\n};\nconst TradingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// SIMPLIFIED LOGIC - NO COMPLEX COOLDOWNS\nconst TradingProvider = (param)=>{\n    let { children } = param;\n    _s();\n    // Initialize state with localStorage data if available\n    const initializeState = ()=>{\n        // Check if this is a new session from URL parameter\n        if (true) {\n            const urlParams = new URLSearchParams(window.location.search);\n            const isNewSession = urlParams.get('newSession') === 'true';\n            if (isNewSession) {\n                console.log('🆕 New session requested - starting with completely fresh state');\n                // Clear the URL parameter to avoid confusion\n                const newUrl = window.location.pathname;\n                window.history.replaceState({}, '', newUrl);\n                return initialTradingState;\n            }\n        }\n        // Check if this is a new window/tab by checking if we have a current session\n        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n        const currentSessionId = sessionManager.getCurrentSessionId();\n        // If no current session exists for this window, start fresh\n        if (!currentSessionId) {\n            console.log('🆕 New window detected - starting with fresh state');\n            return initialTradingState;\n        }\n        // If we have a session, try to load saved state\n        const savedState = loadStateFromLocalStorage();\n        if (savedState) {\n            return {\n                ...initialTradingState,\n                ...savedState\n            };\n        }\n        return initialTradingState;\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(tradingReducer, initializeState());\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Removed processing locks and cooldowns for continuous trading\n    // Initialize fetchMarketPrice first\n    const fetchMarketPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[fetchMarketPrice]\": async ()=>{\n            try {\n                // Don't fetch price if either crypto is not selected\n                if (!state.config.crypto1 || !state.config.crypto2) {\n                    dispatch({\n                        type: 'SET_MARKET_PRICE',\n                        payload: 0\n                    });\n                    return;\n                }\n                const price = await getMarketPriceFromAPI(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: price\n                });\n            } catch (error) {\n                console.error('Failed to fetch market price:', error);\n                // Send Telegram notification for price fetch failure\n                sendTelegramErrorNotification('Price Fetch Error', \"Failed to fetch market price for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2), \"Error: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n                // Use fallback price calculation\n                const fallbackPrice = calculateFallbackMarketPrice(state.config);\n                dispatch({\n                    type: 'SET_MARKET_PRICE',\n                    payload: fallbackPrice\n                });\n            }\n        }\n    }[\"TradingProvider.useCallback[fetchMarketPrice]\"], [\n        state.config,\n        dispatch,\n        sendTelegramErrorNotification\n    ]);\n    // Market price fluctuation effect - simulates real-time price changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial fetch\n            fetchMarketPrice();\n            // Set up price fluctuation interval for simulation - hardcoded to 2 seconds\n            const priceFluctuationInterval = setInterval({\n                \"TradingProvider.useEffect.priceFluctuationInterval\": ()=>{\n                    // Only fluctuate price if online\n                    const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n                    if (networkMonitor.getStatus().isOnline) {\n                        dispatch({\n                            type: 'FLUCTUATE_MARKET_PRICE'\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.priceFluctuationInterval\"], 2000); // Fixed at 2 seconds as requested\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    clearInterval(priceFluctuationInterval);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        fetchMarketPrice,\n        dispatch\n    ]);\n    // Other effects\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n            }\n        }\n    }[\"TradingProvider.useEffect\"], []);\n    const playSound = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[playSound]\": (soundKey)=>{\n            if (state.appSettings.soundAlertsEnabled && audioRef.current) {\n                let soundPath;\n                if (soundKey === 'soundOrderExecution' && state.appSettings.alertOnOrderExecution) {\n                    soundPath = state.appSettings.soundOrderExecution;\n                } else if (soundKey === 'soundError' && state.appSettings.alertOnError) {\n                    soundPath = state.appSettings.soundError;\n                }\n                if (soundPath) {\n                    audioRef.current.src = soundPath;\n                    audioRef.current.currentTime = 0; // Reset to beginning\n                    // Play the sound and limit duration to 2 seconds\n                    audioRef.current.play().then({\n                        \"TradingProvider.useCallback[playSound]\": ()=>{\n                            // Set a timeout to pause the audio after 2 seconds\n                            setTimeout({\n                                \"TradingProvider.useCallback[playSound]\": ()=>{\n                                    if (audioRef.current) {\n                                        audioRef.current.pause();\n                                        audioRef.current.currentTime = 0; // Reset for next play\n                                    }\n                                }\n                            }[\"TradingProvider.useCallback[playSound]\"], 2000); // 2 seconds\n                        }\n                    }[\"TradingProvider.useCallback[playSound]\"]).catch({\n                        \"TradingProvider.useCallback[playSound]\": (err)=>console.error(\"Error playing sound:\", err)\n                    }[\"TradingProvider.useCallback[playSound]\"]);\n                }\n            }\n        }\n    }[\"TradingProvider.useCallback[playSound]\"], [\n        state.appSettings\n    ]);\n    // Enhanced Telegram notification function with error handling\n    const sendTelegramNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNotification]\": async function(message) {\n            let isError = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n            try {\n                const telegramToken = localStorage.getItem('telegram_bot_token');\n                const telegramChatId = localStorage.getItem('telegram_chat_id');\n                if (!telegramToken || !telegramChatId) {\n                    console.log('Telegram not configured - skipping notification');\n                    return false;\n                }\n                const controller = new AbortController();\n                const timeoutId = setTimeout({\n                    \"TradingProvider.useCallback[sendTelegramNotification].timeoutId\": ()=>controller.abort()\n                }[\"TradingProvider.useCallback[sendTelegramNotification].timeoutId\"], 10000); // 10 second timeout\n                const response = await fetch(\"https://api.telegram.org/bot\".concat(telegramToken, \"/sendMessage\"), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        chat_id: telegramChatId,\n                        text: message,\n                        parse_mode: 'HTML'\n                    }),\n                    signal: controller.signal\n                });\n                clearTimeout(timeoutId);\n                if (response.ok) {\n                    console.log('✅ Telegram notification sent successfully');\n                    return true;\n                } else {\n                    const errorText = await response.text();\n                    console.error('❌ Failed to send Telegram notification:', response.status, errorText);\n                    return false;\n                }\n            } catch (error) {\n                console.error('❌ Error sending Telegram notification:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNotification]\"], []);\n    // Specific error notification functions\n    const sendTelegramErrorNotification = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramErrorNotification]\": async (errorType, errorMessage, context)=>{\n            const timestamp = new Date().toLocaleString();\n            const contextInfo = context ? \"\\n\\uD83D\\uDCCD Context: \".concat(context) : '';\n            const message = \"\\uD83D\\uDEA8 <b>TRADING BOT ERROR</b>\\n\\n\" + \"⚠️ Error Type: \".concat(errorType, \"\\n\") + \"\\uD83D\\uDCAC Message: \".concat(errorMessage).concat(contextInfo, \"\\n\") + \"\\uD83D\\uDD50 Time: \".concat(timestamp, \"\\n\") + \"\\uD83E\\uDD16 Bot Status: \".concat(state.botSystemStatus);\n            return await sendTelegramNotification(message, true);\n        }\n    }[\"TradingProvider.useCallback[sendTelegramErrorNotification]\"], [\n        sendTelegramNotification,\n        state.botSystemStatus\n    ]);\n    const sendTelegramBalanceWarning = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramBalanceWarning]\": async (balanceType, currentBalance, requiredBalance)=>{\n            const timestamp = new Date().toLocaleString();\n            const pair = \"\".concat(state.config.crypto1, \"/\").concat(state.config.crypto2);\n            const message = \"⚠️ <b>LOW BALANCE WARNING</b>\\n\\n\" + \"\\uD83D\\uDCB0 Balance Type: \".concat(balanceType, \"\\n\") + \"\\uD83D\\uDCCA Current: \".concat(currentBalance.toFixed(6), \"\\n\") + \"\\uD83D\\uDCC8 Required: \".concat(requiredBalance.toFixed(6), \"\\n\") + \"\\uD83D\\uDCC9 Pair: \".concat(pair, \"\\n\") + \"\\uD83D\\uDD50 Time: \".concat(timestamp, \"\\n\") + \"\\uD83D\\uDED1 Trading may be paused until balance is sufficient\";\n            return await sendTelegramNotification(message, true);\n        }\n    }[\"TradingProvider.useCallback[sendTelegramBalanceWarning]\"], [\n        sendTelegramNotification,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    const sendTelegramNetworkError = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[sendTelegramNetworkError]\": async (isDisconnected)=>{\n            const timestamp = new Date().toLocaleString();\n            const status = isDisconnected ? 'DISCONNECTED' : 'RECONNECTED';\n            const emoji = isDisconnected ? '🔴' : '🟢';\n            const message = \"\".concat(emoji, \" <b>NETWORK \").concat(status, \"</b>\\n\\n\") + \"\\uD83D\\uDCE1 Status: \".concat(isDisconnected ? 'Connection lost' : 'Connection restored', \"\\n\") + \"\\uD83E\\uDD16 Bot Action: \".concat(isDisconnected ? 'Stopped and session saved' : 'Ready to resume', \"\\n\") + \"\\uD83D\\uDD50 Time: \".concat(timestamp);\n            return await sendTelegramNotification(message, isDisconnected);\n        }\n    }[\"TradingProvider.useCallback[sendTelegramNetworkError]\"], [\n        sendTelegramNotification\n    ]);\n    // Effect to update market price when trading pair changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n        // When crypto1 or crypto2 (parts of state.config) change,\n        // the fetchMarketPrice useCallback gets a new reference.\n        // The useEffect above (which depends on fetchMarketPrice)\n        // will re-run, clear the old interval, make an initial fetch with the new config,\n        // and set up a new interval.\n        // The reducer for SET_CONFIG also sets an initial market price if crypto1/crypto2 changes.\n        // Thus, no explicit dispatch is needed here.\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]); // Dependencies ensure this reacts to pair changes\n    const setTargetPrices = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[setTargetPrices]\": (prices)=>{\n            if (!prices || !Array.isArray(prices)) return;\n            // Sort prices from lowest to highest for proper counter assignment\n            const sortedPrices = [\n                ...prices\n            ].filter({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (price)=>!isNaN(price) && price > 0\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]).sort({\n                \"TradingProvider.useCallback[setTargetPrices].sortedPrices\": (a, b)=>a - b\n            }[\"TradingProvider.useCallback[setTargetPrices].sortedPrices\"]);\n            const newRows = sortedPrices.map({\n                \"TradingProvider.useCallback[setTargetPrices].newRows\": (price, index)=>{\n                    const existingRow = state.targetPriceRows.find({\n                        \"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\": (r)=>r.targetPrice === price\n                    }[\"TradingProvider.useCallback[setTargetPrices].newRows.existingRow\"]);\n                    if (existingRow) {\n                        // Update counter for existing row based on sorted position\n                        return {\n                            ...existingRow,\n                            counter: index + 1\n                        };\n                    }\n                    return {\n                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                        counter: index + 1,\n                        status: 'Free',\n                        orderLevel: 0,\n                        valueLevel: state.config.baseBid,\n                        targetPrice: price\n                    };\n                }\n            }[\"TradingProvider.useCallback[setTargetPrices].newRows\"]);\n            dispatch({\n                type: 'SET_TARGET_PRICE_ROWS',\n                payload: newRows\n            });\n        }\n    }[\"TradingProvider.useCallback[setTargetPrices]\"], [\n        state.targetPriceRows,\n        state.config.baseBid,\n        dispatch\n    ]);\n    // Core Trading Logic (Simulated) - CONTINUOUS TRADING VERSION\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Check network status first\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const isOnline = networkMonitor.getStatus().isOnline;\n            // Only check essential conditions AND network status\n            if (state.botSystemStatus !== 'Running' || state.targetPriceRows.length === 0 || state.currentMarketPrice <= 0 || !isOnline) {\n                if (!isOnline && state.botSystemStatus === 'Running') {\n                    console.log('🔴 Trading paused - network offline');\n                }\n                return;\n            }\n            // Execute trading logic immediately - no locks, no cooldowns, no delays\n            const { config, currentMarketPrice, targetPriceRows, crypto1Balance, crypto2Balance } = state;\n            const sortedRowsForLogic = [\n                ...targetPriceRows\n            ].sort({\n                \"TradingProvider.useEffect.sortedRowsForLogic\": (a, b)=>a.targetPrice - b.targetPrice\n            }[\"TradingProvider.useEffect.sortedRowsForLogic\"]);\n            // Use mutable variables for balance tracking within this cycle\n            let currentCrypto1Balance = crypto1Balance;\n            let currentCrypto2Balance = crypto2Balance;\n            let actionsTaken = 0;\n            console.log(\"\\uD83D\\uDE80 CONTINUOUS TRADING: Price $\".concat(currentMarketPrice.toFixed(2), \" | Targets: \").concat(sortedRowsForLogic.length, \" | Balance: $\").concat(currentCrypto2Balance, \" \").concat(config.crypto2));\n            // Show which targets are in range\n            const targetsInRange = sortedRowsForLogic.filter({\n                \"TradingProvider.useEffect.targetsInRange\": (row)=>{\n                    const diffPercent = Math.abs(currentMarketPrice - row.targetPrice) / currentMarketPrice * 100;\n                    return diffPercent <= config.slippagePercent;\n                }\n            }[\"TradingProvider.useEffect.targetsInRange\"]);\n            if (targetsInRange.length > 0) {\n                console.log(\"\\uD83C\\uDFAF TARGETS IN RANGE (\\xb1\".concat(config.slippagePercent, \"%):\"), targetsInRange.map({\n                    \"TradingProvider.useEffect\": (row)=>\"Counter \".concat(row.counter, \" (\").concat(row.status, \")\")\n                }[\"TradingProvider.useEffect\"]));\n            }\n            // CONTINUOUS TRADING LOGIC: Process all targets immediately\n            for(let i = 0; i < sortedRowsForLogic.length; i++){\n                const activeRow = sortedRowsForLogic[i];\n                const priceDiffPercent = Math.abs(currentMarketPrice - activeRow.targetPrice) / currentMarketPrice * 100;\n                // STEP 1: Check if TargetRowN is triggered (within slippage range)\n                if (priceDiffPercent <= config.slippagePercent) {\n                    if (config.tradingMode === \"SimpleSpot\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute BUY on TargetRowN\n                            const costCrypto2 = activeRow.valueLevel;\n                            if (currentCrypto2Balance >= costCrypto2) {\n                                const amountCrypto1Bought = costCrypto2 / currentMarketPrice;\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: activeRow.orderLevel + 1,\n                                    valueLevel: config.baseBid * Math.pow(config.multiplier, activeRow.orderLevel + 1),\n                                    crypto1AmountHeld: amountCrypto1Bought,\n                                    originalCostCrypto2: costCrypto2,\n                                    crypto1Var: amountCrypto1Bought,\n                                    crypto2Var: -costCrypto2\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + amountCrypto1Bought,\n                                        crypto2: currentCrypto2Balance - costCrypto2\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: amountCrypto1Bought,\n                                        avgPrice: currentMarketPrice,\n                                        valueCrypto2: costCrypto2,\n                                        price1: currentMarketPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.crypto2 || ''\n                                    }\n                                });\n                                console.log(\"✅ BUY: Counter \".concat(activeRow.counter, \" bought \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" at $\").concat(currentMarketPrice.toFixed(2)));\n                                toast({\n                                    title: \"BUY Executed\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Cost: $\".concat(costCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= costCrypto2;\n                                currentCrypto1Balance += amountCrypto1Bought;\n                            } else {\n                                // Insufficient balance - send Telegram error notification\n                                console.warn(\"⚠️ Insufficient \".concat(config.crypto2, \" balance for BUY order. Required: \").concat(costCrypto2.toFixed(6), \", Available: \").concat(currentCrypto2Balance.toFixed(6)));\n                                sendTelegramBalanceWarning(\"\".concat(config.crypto2, \" (BUY Order)\"), currentCrypto2Balance, costCrypto2);\n                                // Also send general error notification\n                                sendTelegramErrorNotification('Insufficient Balance', \"Cannot execute BUY order for counter \".concat(activeRow.counter), \"Required: \".concat(costCrypto2.toFixed(6), \" \").concat(config.crypto2, \", Available: \").concat(currentCrypto2Balance.toFixed(6), \" \").concat(config.crypto2));\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            const crypto2Received = amountCrypto1ToSell * currentMarketPrice;\n                            const realizedProfit = crypto2Received - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = currentMarketPrice > 0 ? realizedProfit * config.incomeSplitCrypto1Percent / 100 / currentMarketPrice : 0;\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: -amountCrypto1ToSell,\n                                crypto2Var: crypto2Received\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Received\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.crypto2),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: currentMarketPrice,\n                                    valueCrypto2: crypto2Received,\n                                    price1: currentMarketPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.crypto2 || '',\n                                    realizedProfitLossCrypto2: realizedProfit,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ SELL: Counter \".concat(currentCounter - 1, \" sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \". Profit: $\").concat(realizedProfit.toFixed(2)));\n                            toast({\n                                title: \"SELL Executed\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit $\").concat(realizedProfit.toFixed(2)),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for SELL\n                            const profitEmoji = realizedProfit > 0 ? '📈' : realizedProfit < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDCB0 Amount: \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCB5 Price: $\".concat(currentMarketPrice.toFixed(2), \"\\n\") + \"\\uD83D\\uDCB8 Received: $\".concat(crypto2Received.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: $\").concat(realizedProfit.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Simple Spot\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Received;\n                        }\n                    } else if (config.tradingMode === \"StablecoinSwap\") {\n                        // STEP 2: Evaluate Triggered TargetRowN's Status (Stablecoin Swap Mode)\n                        if (activeRow.status === \"Free\") {\n                            // STEP 2a: Execute Two-Step \"Buy Crypto1 via Stablecoin\" on TargetRowN\n                            const amountCrypto2ToUse = activeRow.valueLevel; // Value = BaseBid * (Multiplier ^ Level)\n                            if (currentCrypto2Balance >= amountCrypto2ToUse) {\n                                // Step 1: Sell Crypto2 for PreferredStablecoin\n                                // Get real market price for Crypto2/Stablecoin pair (synchronous for now)\n                                const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const stablecoinObtained = amountCrypto2ToUse * crypto2StablecoinPrice;\n                                // Step 2: Buy Crypto1 with Stablecoin\n                                // Get real market price for Crypto1/Stablecoin pair\n                                const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                                const crypto1Bought = stablecoinObtained / crypto1StablecoinPrice;\n                                // Update Row N: Free → Full, Level++, Value recalculated\n                                const newLevel = activeRow.orderLevel + 1;\n                                const newValue = config.baseBid * Math.pow(config.multiplier, newLevel);\n                                const updatedRow = {\n                                    ...activeRow,\n                                    status: \"Full\",\n                                    orderLevel: newLevel,\n                                    valueLevel: newValue,\n                                    crypto1AmountHeld: crypto1Bought,\n                                    originalCostCrypto2: amountCrypto2ToUse,\n                                    crypto1Var: crypto1Bought,\n                                    crypto2Var: -amountCrypto2ToUse\n                                };\n                                dispatch({\n                                    type: 'UPDATE_TARGET_PRICE_ROW',\n                                    payload: updatedRow\n                                });\n                                dispatch({\n                                    type: 'UPDATE_BALANCES',\n                                    payload: {\n                                        crypto1: currentCrypto1Balance + crypto1Bought,\n                                        crypto2: currentCrypto2Balance - amountCrypto2ToUse\n                                    }\n                                });\n                                // Add history entries for both steps of the stablecoin swap\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto2,\n                                        orderType: \"SELL\",\n                                        amountCrypto1: amountCrypto2ToUse,\n                                        avgPrice: crypto2StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto2StablecoinPrice,\n                                        crypto1Symbol: config.crypto2 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                dispatch({\n                                    type: 'ADD_ORDER_HISTORY_ENTRY',\n                                    payload: {\n                                        id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                        timestamp: Date.now(),\n                                        pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                        crypto1: config.crypto1,\n                                        orderType: \"BUY\",\n                                        amountCrypto1: crypto1Bought,\n                                        avgPrice: crypto1StablecoinPrice,\n                                        valueCrypto2: stablecoinObtained,\n                                        price1: crypto1StablecoinPrice,\n                                        crypto1Symbol: config.crypto1 || '',\n                                        crypto2Symbol: config.preferredStablecoin || ''\n                                    }\n                                });\n                                console.log(\"✅ STABLECOIN BUY: Counter \".concat(activeRow.counter, \" | Step 1: Sold \").concat(amountCrypto2ToUse, \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step 2: Bought \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" | Level: \").concat(activeRow.orderLevel, \" → \").concat(newLevel));\n                                toast({\n                                    title: \"BUY Executed (Stablecoin)\",\n                                    description: \"Counter \".concat(activeRow.counter, \": \").concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \" via \").concat(config.preferredStablecoin),\n                                    duration: 2000\n                                });\n                                playSound('soundOrderExecution');\n                                // Send Telegram notification for Stablecoin BUY\n                                sendTelegramNotification(\"\\uD83D\\uDFE2 <b>BUY EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(activeRow.counter, \"\\n\") + \"\\uD83D\\uDD04 Step 1: Sold \".concat(amountCrypto2ToUse.toFixed(2), \" \").concat(config.crypto2, \" → \").concat(stablecoinObtained.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step 2: Bought \".concat(crypto1Bought.toFixed(6), \" \").concat(config.crypto1, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(activeRow.orderLevel, \" → \").concat(newLevel, \"\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                                actionsTaken++;\n                                // Update local balance for subsequent checks in this cycle\n                                currentCrypto2Balance -= amountCrypto2ToUse;\n                                currentCrypto1Balance += crypto1Bought;\n                            }\n                        }\n                        // STEP 3: Check and Process Inferior TargetRow (TargetRowN_minus_1) - ALWAYS, regardless of BUY\n                        const currentCounter = activeRow.counter;\n                        const inferiorRow = sortedRowsForLogic.find({\n                            \"TradingProvider.useEffect.inferiorRow\": (row)=>row.counter === currentCounter - 1\n                        }[\"TradingProvider.useEffect.inferiorRow\"]);\n                        if (inferiorRow && inferiorRow.status === \"Full\" && inferiorRow.crypto1AmountHeld && inferiorRow.originalCostCrypto2) {\n                            // Execute Two-Step \"Sell Crypto1 & Reacquire Crypto2 via Stablecoin\" for TargetRowN_minus_1\n                            const amountCrypto1ToSell = inferiorRow.crypto1AmountHeld;\n                            // Step A: Sell Crypto1 for PreferredStablecoin\n                            const crypto1StablecoinPrice = getUSDPrice(config.crypto1 || 'BTC') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const stablecoinFromC1Sell = amountCrypto1ToSell * crypto1StablecoinPrice;\n                            // Step B: Buy Crypto2 with Stablecoin\n                            const crypto2StablecoinPrice = getUSDPrice(config.crypto2 || 'USDT') / getUSDPrice(config.preferredStablecoin || 'USDT');\n                            const crypto2Reacquired = stablecoinFromC1Sell / crypto2StablecoinPrice;\n                            // Calculate realized profit in Crypto2\n                            const realizedProfitInCrypto2 = crypto2Reacquired - inferiorRow.originalCostCrypto2;\n                            // Calculate Crypto1 profit/loss based on income split percentage\n                            const realizedProfitCrypto1 = crypto1StablecoinPrice > 0 ? realizedProfitInCrypto2 * config.incomeSplitCrypto1Percent / 100 / crypto1StablecoinPrice : 0;\n                            // Update Row N-1: Full → Free, Level UNCHANGED, Vars cleared\n                            const updatedInferiorRow = {\n                                ...inferiorRow,\n                                status: \"Free\",\n                                // orderLevel: REMAINS UNCHANGED per specification\n                                crypto1AmountHeld: undefined,\n                                originalCostCrypto2: undefined,\n                                valueLevel: config.baseBid * Math.pow(config.multiplier, inferiorRow.orderLevel),\n                                crypto1Var: 0,\n                                crypto2Var: 0\n                            };\n                            dispatch({\n                                type: 'UPDATE_TARGET_PRICE_ROW',\n                                payload: updatedInferiorRow\n                            });\n                            dispatch({\n                                type: 'UPDATE_BALANCES',\n                                payload: {\n                                    crypto1: currentCrypto1Balance - amountCrypto1ToSell,\n                                    crypto2: currentCrypto2Balance + crypto2Reacquired\n                                }\n                            });\n                            // Add history entries for both steps of the N-1 stablecoin swap\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto1, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto1,\n                                    orderType: \"SELL\",\n                                    amountCrypto1: amountCrypto1ToSell,\n                                    avgPrice: crypto1StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto1StablecoinPrice,\n                                    crypto1Symbol: config.crypto1 || '',\n                                    crypto2Symbol: config.preferredStablecoin || ''\n                                }\n                            });\n                            dispatch({\n                                type: 'ADD_ORDER_HISTORY_ENTRY',\n                                payload: {\n                                    id: (0,uuid__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(),\n                                    timestamp: Date.now(),\n                                    pair: \"\".concat(config.crypto2, \"/\").concat(config.preferredStablecoin),\n                                    crypto1: config.crypto2,\n                                    orderType: \"BUY\",\n                                    amountCrypto1: crypto2Reacquired,\n                                    avgPrice: crypto2StablecoinPrice,\n                                    valueCrypto2: stablecoinFromC1Sell,\n                                    price1: crypto2StablecoinPrice,\n                                    crypto1Symbol: config.crypto2 || '',\n                                    crypto2Symbol: config.preferredStablecoin || '',\n                                    realizedProfitLossCrypto2: realizedProfitInCrypto2,\n                                    realizedProfitLossCrypto1: realizedProfitCrypto1\n                                }\n                            });\n                            console.log(\"✅ STABLECOIN SELL: Counter \".concat(currentCounter - 1, \" | Step A: Sold \").concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \" | Step B: Bought \").concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \" | Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" | Level: \").concat(inferiorRow.orderLevel, \" (unchanged)\"));\n                            toast({\n                                title: \"SELL Executed (Stablecoin)\",\n                                description: \"Counter \".concat(currentCounter - 1, \": Profit \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \" via \").concat(config.preferredStablecoin),\n                                duration: 2000\n                            });\n                            playSound('soundOrderExecution');\n                            // Send Telegram notification for Stablecoin SELL\n                            const profitEmoji = realizedProfitInCrypto2 > 0 ? '📈' : realizedProfitInCrypto2 < 0 ? '📉' : '➖';\n                            sendTelegramNotification(\"\\uD83D\\uDD34 <b>SELL EXECUTED (Stablecoin Swap)</b>\\n\" + \"\\uD83D\\uDCCA Counter: \".concat(currentCounter - 1, \"\\n\") + \"\\uD83D\\uDD04 Step A: Sold \".concat(amountCrypto1ToSell.toFixed(6), \" \").concat(config.crypto1, \" → \").concat(stablecoinFromC1Sell.toFixed(2), \" \").concat(config.preferredStablecoin, \"\\n\") + \"\\uD83D\\uDD04 Step B: Bought \".concat(crypto2Reacquired.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\".concat(profitEmoji, \" Profit: \").concat(realizedProfitInCrypto2.toFixed(2), \" \").concat(config.crypto2, \"\\n\") + \"\\uD83D\\uDCCA Level: \".concat(inferiorRow.orderLevel, \" (unchanged)\\n\") + \"\\uD83D\\uDCC8 Mode: Stablecoin Swap\");\n                            actionsTaken++;\n                            // Update local balance for subsequent checks in this cycle\n                            currentCrypto1Balance -= amountCrypto1ToSell;\n                            currentCrypto2Balance += crypto2Reacquired;\n                        }\n                    }\n                }\n            }\n            if (actionsTaken > 0) {\n                console.log(\"\\uD83C\\uDFAF CYCLE COMPLETE: \".concat(actionsTaken, \" actions taken at price $\").concat(currentMarketPrice.toFixed(2)));\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.currentMarketPrice,\n        state.targetPriceRows,\n        state.config,\n        state.crypto1Balance,\n        state.crypto2Balance,\n        state.stablecoinBalance,\n        dispatch,\n        toast,\n        playSound,\n        sendTelegramNotification\n    ]);\n    const getDisplayOrders = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[getDisplayOrders]\": ()=>{\n            if (!state.targetPriceRows || !Array.isArray(state.targetPriceRows)) {\n                return [];\n            }\n            return state.targetPriceRows.map({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (row)=>{\n                    const currentPrice = state.currentMarketPrice || 0;\n                    const targetPrice = row.targetPrice || 0;\n                    const percentFromActualPrice = currentPrice && targetPrice ? (currentPrice / targetPrice - 1) * 100 : 0;\n                    let incomeCrypto1;\n                    let incomeCrypto2;\n                    if (row.status === \"Full\" && row.crypto1AmountHeld && row.originalCostCrypto2) {\n                        const totalUnrealizedProfitInCrypto2 = currentPrice * row.crypto1AmountHeld - row.originalCostCrypto2;\n                        incomeCrypto2 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto2Percent / 100;\n                        if (currentPrice > 0) {\n                            incomeCrypto1 = totalUnrealizedProfitInCrypto2 * state.config.incomeSplitCrypto1Percent / 100 / currentPrice;\n                        }\n                    }\n                    return {\n                        ...row,\n                        currentPrice,\n                        priceDifference: targetPrice - currentPrice,\n                        priceDifferencePercent: currentPrice > 0 ? (targetPrice - currentPrice) / currentPrice * 100 : 0,\n                        potentialProfitCrypto1: state.config.incomeSplitCrypto1Percent / 100 * row.valueLevel / (targetPrice || 1),\n                        potentialProfitCrypto2: state.config.incomeSplitCrypto2Percent / 100 * row.valueLevel,\n                        percentFromActualPrice,\n                        incomeCrypto1,\n                        incomeCrypto2\n                    };\n                }\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]).sort({\n                \"TradingProvider.useCallback[getDisplayOrders]\": (a, b)=>b.targetPrice - a.targetPrice\n            }[\"TradingProvider.useCallback[getDisplayOrders]\"]);\n        }\n    }[\"TradingProvider.useCallback[getDisplayOrders]\"], [\n        state.targetPriceRows,\n        state.currentMarketPrice,\n        state.config.incomeSplitCrypto1Percent,\n        state.config.incomeSplitCrypto2Percent,\n        state.config.baseBid,\n        state.config.multiplier\n    ]);\n    // Backend Integration Functions\n    const saveConfigToBackend = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveConfigToBackend]\": async (config)=>{\n            try {\n                var _response_config;\n                const configData = {\n                    name: \"\".concat(config.crypto1, \"/\").concat(config.crypto2, \" \").concat(config.tradingMode),\n                    tradingMode: config.tradingMode,\n                    crypto1: config.crypto1,\n                    crypto2: config.crypto2,\n                    baseBid: config.baseBid,\n                    multiplier: config.multiplier,\n                    numDigits: config.numDigits,\n                    slippagePercent: config.slippagePercent,\n                    incomeSplitCrypto1Percent: config.incomeSplitCrypto1Percent,\n                    incomeSplitCrypto2Percent: config.incomeSplitCrypto2Percent,\n                    preferredStablecoin: config.preferredStablecoin,\n                    targetPrices: state.targetPriceRows.map({\n                        \"TradingProvider.useCallback[saveConfigToBackend]\": (row)=>row.targetPrice\n                    }[\"TradingProvider.useCallback[saveConfigToBackend]\"])\n                };\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.saveConfig(configData);\n                console.log('✅ Config saved to backend:', response);\n                return ((_response_config = response.config) === null || _response_config === void 0 ? void 0 : _response_config.id) || null;\n            } catch (error) {\n                console.error('❌ Failed to save config to backend:', error);\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to save configuration to backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return null;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveConfigToBackend]\"], [\n        state.targetPriceRows,\n        toast\n    ]);\n    const startBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[startBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.startBot(configId);\n                console.log('✅ Bot started on backend:', response);\n                toast({\n                    title: \"Bot Started\",\n                    description: \"Trading bot started successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to start bot on backend:', error);\n                // Send Telegram error notification\n                sendTelegramErrorNotification('Bot Start Failed', \"Failed to start trading bot on backend\", \"Config ID: \".concat(configId, \", Error: \").concat(error instanceof Error ? error.message : 'Unknown error'));\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to start bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[startBackendBot]\"], [\n        toast,\n        sendTelegramErrorNotification\n    ]);\n    const stopBackendBot = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[stopBackendBot]\": async (configId)=>{\n            try {\n                const response = await _lib_api__WEBPACK_IMPORTED_MODULE_4__.tradingApi.stopBot(configId);\n                console.log('✅ Bot stopped on backend:', response);\n                toast({\n                    title: \"Bot Stopped\",\n                    description: \"Trading bot stopped successfully on backend\",\n                    duration: 3000\n                });\n                return true;\n            } catch (error) {\n                console.error('❌ Failed to stop bot on backend:', error);\n                // Send Telegram error notification\n                sendTelegramErrorNotification('Bot Stop Failed', \"Failed to stop trading bot on backend\", \"Config ID: \".concat(configId, \", Error: \").concat(error instanceof Error ? error.message : 'Unknown error'));\n                toast({\n                    title: \"Backend Error\",\n                    description: \"Failed to stop bot on backend\",\n                    variant: \"destructive\",\n                    duration: 3000\n                });\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[stopBackendBot]\"], [\n        toast,\n        sendTelegramErrorNotification\n    ]);\n    const checkBackendStatus = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[checkBackendStatus]\": async ()=>{\n            const apiUrl = \"http://localhost:5000\";\n            if (!apiUrl) {\n                console.error('Error: NEXT_PUBLIC_API_URL is not defined. Backend connectivity check cannot be performed.');\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                return;\n            }\n            try {\n                const healthResponse = await fetch(\"\".concat(apiUrl, \"/health/\"));\n                if (!healthResponse.ok) {\n                    // Log more details if the response was received but not OK\n                    console.error(\"Backend health check failed with status: \".concat(healthResponse.status, \" \").concat(healthResponse.statusText));\n                    const responseText = await healthResponse.text().catch({\n                        \"TradingProvider.useCallback[checkBackendStatus]\": ()=>'Could not read response text.'\n                    }[\"TradingProvider.useCallback[checkBackendStatus]\"]);\n                    console.error('Backend health check response body:', responseText);\n                }\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: healthResponse.ok ? 'online' : 'offline'\n                });\n            } catch (error) {\n                dispatch({\n                    type: 'SET_BACKEND_STATUS',\n                    payload: 'offline'\n                });\n                console.error('Backend connectivity check failed. Error details:', error);\n                if (error.cause) {\n                    console.error('Fetch error cause:', error.cause);\n                }\n                // It's also useful to log the apiUrl to ensure it's what we expect\n                console.error('Attempted to fetch API URL:', \"\".concat(apiUrl, \"/health/\"));\n            }\n        }\n    }[\"TradingProvider.useCallback[checkBackendStatus]\"], [\n        dispatch\n    ]);\n    // Initialize backend status check (one-time only)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            // Initial check for backend status only\n            checkBackendStatus();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        checkBackendStatus\n    ]);\n    // Session restoration effect - runs once on component mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const restoreSession = {\n                \"TradingProvider.useEffect.restoreSession\": async ()=>{\n                    try {\n                        const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                        // Wait a bit for session manager to initialize\n                        await new Promise({\n                            \"TradingProvider.useEffect.restoreSession\": (resolve)=>setTimeout(resolve, 1000)\n                        }[\"TradingProvider.useEffect.restoreSession\"]);\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            const session = sessionManager.loadSession(currentSessionId);\n                            if (session) {\n                                console.log('🔄 Restoring session from backend/storage:', currentSessionId);\n                                // Restore session data to context\n                                dispatch({\n                                    type: 'SET_CONFIG',\n                                    payload: session.config\n                                });\n                                dispatch({\n                                    type: 'SET_TARGET_PRICE_ROWS',\n                                    payload: session.targetPriceRows\n                                });\n                                dispatch({\n                                    type: 'CLEAR_ORDER_HISTORY'\n                                });\n                                session.orderHistory.forEach({\n                                    \"TradingProvider.useEffect.restoreSession\": (entry)=>{\n                                        dispatch({\n                                            type: 'ADD_ORDER_HISTORY_ENTRY',\n                                            payload: entry\n                                        });\n                                    }\n                                }[\"TradingProvider.useEffect.restoreSession\"]);\n                                dispatch({\n                                    type: 'SET_MARKET_PRICE',\n                                    payload: session.currentMarketPrice\n                                });\n                                dispatch({\n                                    type: 'SET_BALANCES',\n                                    payload: {\n                                        crypto1: session.crypto1Balance,\n                                        crypto2: session.crypto2Balance\n                                    }\n                                });\n                                // If session was active (running), restore bot status\n                                if (session.isActive) {\n                                    console.log('🤖 Restoring active bot session');\n                                    dispatch({\n                                        type: 'SYSTEM_START_BOT_INITIATE'\n                                    });\n                                }\n                                console.log('✅ Session restored successfully');\n                            }\n                        }\n                    } catch (error) {\n                        console.error('Failed to restore session:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.restoreSession\"];\n            restoreSession();\n        }\n    }[\"TradingProvider.useEffect\"], []); // Run only once on mount\n    // Save state to localStorage whenever it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            saveStateToLocalStorage(state);\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state\n    ]);\n    // Effect to handle bot warm-up period (immediate execution)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            if (state.botSystemStatus === 'WarmingUp') {\n                console.log(\"Bot is Warming Up... Immediate execution enabled.\");\n                // Immediate transition to Running state - no delays\n                dispatch({\n                    type: 'SYSTEM_COMPLETE_WARMUP'\n                });\n                console.log(\"Bot is now Running immediately.\");\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        dispatch\n    ]);\n    // Effect to handle session runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Bot started running, start runtime tracking\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('✅ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Bot stopped, stop runtime tracking\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Auto-create session when bot starts if none exists and handle runtime tracking\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Check if we need to create a session when bot starts\n            if (state.botSystemStatus === 'WarmingUp' && !sessionManager.getCurrentSessionId()) {\n                // Only create if we have valid crypto configuration AND target prices set\n                // This prevents auto-creation for fresh windows\n                if (state.config.crypto1 && state.config.crypto2 && state.targetPriceRows.length > 0) {\n                    sessionManager.createNewSessionWithAutoName(state.config).then({\n                        \"TradingProvider.useEffect\": (sessionId)=>{\n                            sessionManager.setCurrentSession(sessionId);\n                            console.log('✅ Auto-created session:', sessionId);\n                        }\n                    }[\"TradingProvider.useEffect\"]).catch({\n                        \"TradingProvider.useEffect\": (error)=>{\n                            console.error('❌ Failed to auto-create session:', error);\n                        }\n                    }[\"TradingProvider.useEffect\"]);\n                }\n            }\n            // Handle runtime tracking based on bot status\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                if (state.botSystemStatus === 'Running') {\n                    // Start runtime tracking when bot becomes active\n                    sessionManager.startSessionRuntime(currentSessionId);\n                    console.log('⏱️ Started runtime tracking for session:', currentSessionId);\n                } else if (state.botSystemStatus === 'Stopped') {\n                    // Stop runtime tracking when bot stops\n                    sessionManager.stopSessionRuntime(currentSessionId);\n                    console.log('⏹️ Stopped runtime tracking for session:', currentSessionId);\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus,\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Handle crypto pair changes during active trading\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            const currentSessionId = sessionManager.getCurrentSessionId();\n            if (currentSessionId) {\n                const currentSession = sessionManager.loadSession(currentSessionId);\n                // Check if crypto pair has changed from the current session\n                if (currentSession && (currentSession.config.crypto1 !== state.config.crypto1 || currentSession.config.crypto2 !== state.config.crypto2)) {\n                    console.log('🔄 Crypto pair changed during session, auto-saving and resetting...');\n                    // Auto-save current session if bot was running or has data\n                    if (state.botSystemStatus === 'Running' || state.targetPriceRows.length > 0 || state.orderHistory.length > 0) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            stopBackendBot();\n                        }\n                        // Save current session with timestamp\n                        const timestamp = new Date().toLocaleString('en-US', {\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit',\n                            hour12: false\n                        });\n                        const savedName = \"\".concat(currentSession.name, \" (AutoSaved \").concat(timestamp, \")\");\n                        sessionManager.createNewSession(savedName, currentSession.config).then({\n                            \"TradingProvider.useEffect\": (savedSessionId)=>{\n                                sessionManager.saveSession(savedSessionId, currentSession.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                console.log('💾 AutoSaved session:', savedName);\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                    // Reset for new crypto pair\n                    dispatch({\n                        type: 'RESET_FOR_NEW_CRYPTO'\n                    });\n                    // Create new session for the new crypto pair\n                    if (state.config.crypto1 && state.config.crypto2) {\n                        sessionManager.createNewSessionWithAutoName(state.config).then({\n                            \"TradingProvider.useEffect\": (newSessionId)=>{\n                                sessionManager.setCurrentSession(newSessionId);\n                                console.log('🆕 Created new session for crypto pair:', state.config.crypto1, '/', state.config.crypto2);\n                                toast({\n                                    title: \"Crypto Pair Changed\",\n                                    description: \"Previous session AutoSaved. New session created for \".concat(state.config.crypto1, \"/\").concat(state.config.crypto2),\n                                    duration: 5000\n                                });\n                            }\n                        }[\"TradingProvider.useEffect\"]);\n                    }\n                }\n            }\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.config.crypto1,\n        state.config.crypto2\n    ]);\n    // Initialize network monitoring and auto-save\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const networkMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.NetworkMonitor.getInstance();\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            const memoryMonitor = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.MemoryMonitor.getInstance();\n            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n            // Set up network status listener\n            const unsubscribeNetwork = networkMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeNetwork\": (isOnline, isInitial)=>{\n                    console.log(\"\\uD83C\\uDF10 Network status changed: \".concat(isOnline ? 'Online' : 'Offline'));\n                    // Handle offline state - stop bot and save session\n                    if (!isOnline && !isInitial) {\n                        // Stop the bot if it's running\n                        if (state.botSystemStatus === 'Running') {\n                            console.log('🔴 Internet lost - stopping bot and saving session');\n                            dispatch({\n                                type: 'SYSTEM_STOP_BOT'\n                            });\n                            // Send Telegram notification about network disconnection\n                            sendTelegramNetworkError(true);\n                            // Auto-save current session\n                            const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                            const currentSessionId = sessionManager.getCurrentSessionId();\n                            if (currentSessionId) {\n                                const currentSession = sessionManager.loadSession(currentSessionId);\n                                if (currentSession) {\n                                    // Create offline backup session\n                                    const timestamp = new Date().toLocaleString('en-US', {\n                                        month: 'short',\n                                        day: 'numeric',\n                                        hour: '2-digit',\n                                        minute: '2-digit',\n                                        hour12: false\n                                    });\n                                    const offlineName = \"\".concat(currentSession.name, \" (Offline Backup \").concat(timestamp, \")\");\n                                    sessionManager.createNewSession(offlineName, currentSession.config).then({\n                                        \"TradingProvider.useEffect.unsubscribeNetwork\": (backupSessionId)=>{\n                                            sessionManager.saveSession(backupSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, false);\n                                            console.log('💾 Created offline backup session:', offlineName);\n                                        }\n                                    }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n                                }\n                            }\n                        }\n                        toast({\n                            title: \"Network Disconnected\",\n                            description: \"Bot stopped and session saved. Trading paused until connection restored.\",\n                            variant: \"destructive\",\n                            duration: 8000\n                        });\n                    } else if (isOnline && !isInitial) {\n                        // Send Telegram notification about network reconnection\n                        sendTelegramNetworkError(false);\n                        toast({\n                            title: \"Network Reconnected\",\n                            description: \"Connection restored. You can resume trading.\",\n                            duration: 3000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeNetwork\"]);\n            // Set up memory monitoring\n            const unsubscribeMemory = memoryMonitor.addListener({\n                \"TradingProvider.useEffect.unsubscribeMemory\": (memory)=>{\n                    const usedMB = memory.usedJSHeapSize / 1024 / 1024;\n                    if (usedMB > 150) {\n                        console.warn(\"\\uD83E\\uDDE0 High memory usage: \".concat(usedMB.toFixed(2), \"MB\"));\n                        toast({\n                            title: \"High Memory Usage\",\n                            description: \"Memory usage is high (\".concat(usedMB.toFixed(0), \"MB). Consider refreshing the page.\"),\n                            variant: \"destructive\",\n                            duration: 5000\n                        });\n                    }\n                }\n            }[\"TradingProvider.useEffect.unsubscribeMemory\"]);\n            // Set up auto-save\n            const saveFunction = {\n                \"TradingProvider.useEffect.saveFunction\": ()=>{\n                    try {\n                        // Save to session manager if we have a current session\n                        const currentSessionId = sessionManager.getCurrentSessionId();\n                        if (currentSessionId) {\n                            sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                        }\n                        // Also save to localStorage as backup\n                        saveStateToLocalStorage(state);\n                    } catch (error) {\n                        console.error('Auto-save failed:', error);\n                    }\n                }\n            }[\"TradingProvider.useEffect.saveFunction\"];\n            autoSaveManager.enable(saveFunction, 30000); // Auto-save every 30 seconds\n            // Add beforeunload listener to save session on browser close/refresh\n            const handleBeforeUnload = {\n                \"TradingProvider.useEffect.handleBeforeUnload\": (event)=>{\n                    // Save immediately before unload\n                    saveFunction();\n                    // If bot is running, show warning\n                    if (state.botSystemStatus === 'Running') {\n                        const message = 'Trading bot is currently running. Are you sure you want to leave?';\n                        event.returnValue = message;\n                        return message;\n                    }\n                }\n            }[\"TradingProvider.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            // Cleanup function\n            return ({\n                \"TradingProvider.useEffect\": ()=>{\n                    unsubscribeNetwork();\n                    unsubscribeMemory();\n                    autoSaveManager.disable();\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"TradingProvider.useEffect\"];\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state,\n        toast\n    ]);\n    // Force save when bot status changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TradingProvider.useEffect\": ()=>{\n            const autoSaveManager = _lib_network_monitor__WEBPACK_IMPORTED_MODULE_6__.AutoSaveManager.getInstance();\n            autoSaveManager.saveNow();\n        }\n    }[\"TradingProvider.useEffect\"], [\n        state.botSystemStatus\n    ]);\n    // Manual save session function\n    const saveCurrentSession = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"TradingProvider.useCallback[saveCurrentSession]\": ()=>{\n            try {\n                const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_5__.SessionManager.getInstance();\n                const currentSessionId = sessionManager.getCurrentSessionId();\n                if (!currentSessionId) {\n                    // No current session, create one first\n                    if (state.config.crypto1 && state.config.crypto2) {\n                        sessionManager.createNewSessionWithAutoName(state.config).then({\n                            \"TradingProvider.useCallback[saveCurrentSession]\": (sessionId)=>{\n                                sessionManager.setCurrentSession(sessionId);\n                                sessionManager.saveSession(sessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n                            }\n                        }[\"TradingProvider.useCallback[saveCurrentSession]\"]);\n                        return true;\n                    }\n                    return false;\n                }\n                // Save existing session\n                return sessionManager.saveSession(currentSessionId, state.config, state.targetPriceRows, state.orderHistory, state.currentMarketPrice, state.crypto1Balance, state.crypto2Balance, state.stablecoinBalance, state.botSystemStatus === 'Running');\n            } catch (error) {\n                console.error('Failed to save current session:', error);\n                return false;\n            }\n        }\n    }[\"TradingProvider.useCallback[saveCurrentSession]\"], [\n        state\n    ]);\n    // Context value\n    const contextValue = {\n        ...state,\n        dispatch,\n        setTargetPrices,\n        getDisplayOrders,\n        checkBackendStatus,\n        fetchMarketPrice,\n        startBackendBot,\n        stopBackendBot,\n        saveConfigToBackend,\n        saveCurrentSession,\n        backendStatus: state.backendStatus,\n        botSystemStatus: state.botSystemStatus,\n        isBotActive: state.botSystemStatus === 'Running'\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TradingContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\contexts\\\\TradingContext.tsx\",\n        lineNumber: 1750,\n        columnNumber: 5\n    }, undefined);\n};\n_s(TradingProvider, \"JH5SZqqBTmqkE6zsPrrXa+xEjv0=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = TradingProvider;\n// Custom hook to use the trading context\nconst useTradingContext = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(TradingContext);\n    if (context === undefined) {\n        throw new Error('useTradingContext must be used within a TradingProvider');\n    }\n    return context;\n};\n_s1(useTradingContext, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"TradingProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/TradingContext.tsx\n"));

/***/ })

});