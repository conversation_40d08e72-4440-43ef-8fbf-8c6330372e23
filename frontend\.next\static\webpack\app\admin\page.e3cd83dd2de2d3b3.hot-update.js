"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/modals/SessionAlarmModal.tsx":
/*!*****************************************************!*\
  !*** ./src/components/modals/SessionAlarmModal.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionAlarmModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _barrel_optimize_names_Play_Square_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Square,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Square_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Square,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Square_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Square,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Play_Square_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Play,Square,Upload,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst BUILT_IN_SOUNDS = [\n    {\n        value: 'G_hades_curse',\n        label: 'Hades Curse'\n    },\n    {\n        value: 'G_hades_demat',\n        label: 'Hades Demat'\n    },\n    {\n        value: 'G_hades_mat',\n        label: 'Hades Mat'\n    },\n    {\n        value: 'G_hades_sanctify',\n        label: 'Hades Sanctify'\n    },\n    {\n        value: 'S_mon1',\n        label: 'Monster 1'\n    },\n    {\n        value: 'S_mon2',\n        label: 'Monster 2'\n    },\n    {\n        value: 'Satyr_atk4',\n        label: 'Satyr Attack'\n    },\n    {\n        value: 'bells',\n        label: 'Bells'\n    },\n    {\n        value: 'bird1',\n        label: 'Bird 1'\n    },\n    {\n        value: 'bird7',\n        label: 'Bird 7'\n    },\n    {\n        value: 'cheer',\n        label: 'Cheer'\n    },\n    {\n        value: 'chest1',\n        label: 'Chest'\n    },\n    {\n        value: 'chime2',\n        label: 'Chime'\n    },\n    {\n        value: 'dark2',\n        label: 'Dark'\n    },\n    {\n        value: 'foundry2',\n        label: 'Foundry'\n    },\n    {\n        value: 'goatherd1',\n        label: 'Goatherd'\n    },\n    {\n        value: 'marble1',\n        label: 'Marble'\n    },\n    {\n        value: 'sanctuary1',\n        label: 'Sanctuary'\n    },\n    {\n        value: 'space_bells4a',\n        label: 'Space Bells'\n    },\n    {\n        value: 'sparrow1',\n        label: 'Sparrow'\n    },\n    {\n        value: 'tax3',\n        label: 'Tax'\n    },\n    {\n        value: 'wolf4',\n        label: 'Wolf'\n    }\n];\nconst CUSTOM_SOUND_SUCCESS_VALUE = \"custom_success\";\nconst CUSTOM_SOUND_ERROR_VALUE = \"custom_error\";\nfunction SessionAlarmModal(param) {\n    let { isOpen, onClose, sessionId, sessionName } = param;\n    _s();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [settings, setSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        successAlarmsEnabled: true,\n        errorAlarmsEnabled: true,\n        successSoundType: 'notification',\n        errorSoundType: 'error'\n    });\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const audioRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Load session-specific alarm settings\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionAlarmModal.useEffect\": ()=>{\n            if (isOpen && sessionId) {\n                const savedSettings = localStorage.getItem(\"session_alarm_\".concat(sessionId));\n                if (savedSettings) {\n                    try {\n                        const parsed = JSON.parse(savedSettings);\n                        setSettings(parsed);\n                    } catch (error) {\n                        console.error('Failed to parse saved alarm settings:', error);\n                    }\n                }\n            }\n        }\n    }[\"SessionAlarmModal.useEffect\"], [\n        isOpen,\n        sessionId\n    ]);\n    const handleSwitchChange = (key, checked)=>{\n        setSettings((prev)=>({\n                ...prev,\n                [key]: checked\n            }));\n    };\n    const handleSelectChange = (key, value)=>{\n        setSettings((prev)=>({\n                ...prev,\n                [key]: value\n            }));\n    };\n    const handleFileUpload = (type, event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (!file) return;\n        if (!file.type.startsWith('audio/')) {\n            toast({\n                title: \"Invalid File\",\n                description: \"Please select an audio file (MP3, WAV, etc.)\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (file.size > 5 * 1024 * 1024) {\n            toast({\n                title: \"File Too Large\",\n                description: \"Audio file must be smaller than 5MB\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const reader = new FileReader();\n        reader.onload = (e)=>{\n            var _e_target;\n            const dataUri = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n            if (type === 'success') {\n                setSettings((prev)=>({\n                        ...prev,\n                        successSoundType: CUSTOM_SOUND_SUCCESS_VALUE,\n                        customSuccessSoundDataUri: dataUri\n                    }));\n            } else {\n                setSettings((prev)=>({\n                        ...prev,\n                        errorSoundType: CUSTOM_SOUND_ERROR_VALUE,\n                        customErrorSoundDataUri: dataUri\n                    }));\n            }\n            toast({\n                title: \"Sound Uploaded\",\n                description: \"Custom \".concat(type, \" sound has been uploaded successfully\")\n            });\n        };\n        reader.readAsDataURL(file);\n    };\n    const playTestSound = (soundType, isSuccess)=>{\n        if (isPlaying) {\n            stopTestSound();\n            return;\n        }\n        let soundUrl = '';\n        if (soundType === CUSTOM_SOUND_SUCCESS_VALUE && settings.customSuccessSoundDataUri) {\n            soundUrl = settings.customSuccessSoundDataUri;\n        } else if (soundType === CUSTOM_SOUND_ERROR_VALUE && settings.customErrorSoundDataUri) {\n            soundUrl = settings.customErrorSoundDataUri;\n        } else {\n            // Use built-in sounds\n            soundUrl = \"/sounds/\".concat(soundType, \".mp3\");\n        }\n        if (audioRef.current) {\n            audioRef.current.src = soundUrl;\n            audioRef.current.play().then(()=>{\n                setIsPlaying(soundType);\n            }).catch((error)=>{\n                console.error('Failed to play sound:', error);\n                toast({\n                    title: \"Playback Error\",\n                    description: \"Failed to play the selected sound\",\n                    variant: \"destructive\"\n                });\n            });\n        }\n    };\n    const stopTestSound = ()=>{\n        if (audioRef.current) {\n            audioRef.current.pause();\n            audioRef.current.currentTime = 0;\n        }\n        setIsPlaying(null);\n    };\n    const handleSave = ()=>{\n        // Save settings to localStorage with session-specific key\n        localStorage.setItem(\"session_alarm_\".concat(sessionId), JSON.stringify(settings));\n        toast({\n            title: \"Alarm Settings Saved\",\n            description: 'Alarm preferences for \"'.concat(sessionName, '\" have been updated.')\n        });\n        onClose();\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionAlarmModal.useEffect\": ()=>{\n            if (true) {\n                audioRef.current = new Audio();\n                audioRef.current.onended = ({\n                    \"SessionAlarmModal.useEffect\": ()=>setIsPlaying(null)\n                })[\"SessionAlarmModal.useEffect\"];\n            }\n            return ({\n                \"SessionAlarmModal.useEffect\": ()=>{\n                    if (audioRef.current) {\n                        audioRef.current.pause();\n                        audioRef.current = null;\n                    }\n                }\n            })[\"SessionAlarmModal.useEffect\"];\n        }\n    }[\"SessionAlarmModal.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"sm:max-w-[500px]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Square_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                    lineNumber: 216,\n                                    columnNumber: 13\n                                }, this),\n                                \"Session Alarm Settings\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                            children: [\n                                \"Configure success and error alarms for session: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: sessionName\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 61\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"success-alarms\",\n                                                    className: \"text-base font-medium\",\n                                                    children: \"Success Alarms\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Play sound when trades execute successfully\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_5__.Switch, {\n                                            id: \"success-alarms\",\n                                            checked: settings.successAlarmsEnabled,\n                                            onCheckedChange: (checked)=>handleSwitchChange('successAlarmsEnabled', checked)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this),\n                                settings.successAlarmsEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 pl-4 border-l-2 border-green-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"success-sound\",\n                                                    children: \"Success Sound\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                            value: settings.successSoundType,\n                                                            onValueChange: (value)=>handleSelectChange('successSoundType', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                    className: \"flex-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                        placeholder: \"Select sound\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                                        lineNumber: 249,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                    children: BUILT_IN_SOUNDS.map((sound)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: sound.value,\n                                                                            children: sound.label\n                                                                        }, sound.value, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                                            lineNumber: 253,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                                    lineNumber: 251,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>playTestSound(settings.successSoundType, true),\n                                                            disabled: isPlaying !== null,\n                                                            children: isPlaying === settings.successSoundType ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Square_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 66\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Square_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 99\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        settings.successSoundType === 'custom' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"success-upload\",\n                                                    children: \"Upload Custom Success Sound\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"success-upload\",\n                                                            type: \"file\",\n                                                            accept: \"audio/*\",\n                                                            onChange: (e)=>handleFileUpload('success', e),\n                                                            className: \"hidden\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>{\n                                                                var _document_getElementById;\n                                                                return (_document_getElementById = document.getElementById('success-upload')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                            },\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Square_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                                    lineNumber: 287,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                settings.customSuccessSoundDataUri ? 'Change Sound' : 'Upload Sound'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"error-alarms\",\n                                                    className: \"text-base font-medium\",\n                                                    children: \"Error Alarms\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-muted-foreground\",\n                                                    children: \"Play sound when errors occur\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_5__.Switch, {\n                                            id: \"error-alarms\",\n                                            checked: settings.errorAlarmsEnabled,\n                                            onCheckedChange: (checked)=>handleSwitchChange('errorAlarmsEnabled', checked)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                settings.errorAlarmsEnabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3 pl-4 border-l-2 border-red-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"error-sound\",\n                                                    children: \"Error Sound\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                            value: settings.errorSoundType,\n                                                            onValueChange: (value)=>handleSelectChange('errorSoundType', value),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                    className: \"flex-1\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                        placeholder: \"Select sound\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                                        lineNumber: 321,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                                    lineNumber: 320,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                    children: BUILT_IN_SOUNDS.map((sound)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: sound.value,\n                                                                            children: sound.label\n                                                                        }, sound.value, false, {\n                                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                                            lineNumber: 325,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                                    lineNumber: 323,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>playTestSound(settings.errorSoundType, false),\n                                                            disabled: isPlaying !== null,\n                                                            children: isPlaying === settings.errorSoundType ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Square_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 64\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Square_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 97\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this),\n                                        settings.errorSoundType === 'custom' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                    htmlFor: \"error-upload\",\n                                                    children: \"Upload Custom Error Sound\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"error-upload\",\n                                                            type: \"file\",\n                                                            accept: \"audio/*\",\n                                                            onChange: (e)=>handleFileUpload('error', e),\n                                                            className: \"hidden\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>{\n                                                                var _document_getElementById;\n                                                                return (_document_getElementById = document.getElementById('error-upload')) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.click();\n                                                            },\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Play_Square_Upload_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                                    lineNumber: 359,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                settings.customErrorSoundDataUri ? 'Change Sound' : 'Upload Sound'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogFooter, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogClose, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: handleSave,\n                            className: \"btn-neo\",\n                            children: \"Save Alarm Settings\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                            lineNumber: 374,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n            lineNumber: 213,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\modals\\\\SessionAlarmModal.tsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionAlarmModal, \"fluQXkhhmuuFOdI8qao8X1/xLRA=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = SessionAlarmModal;\nvar _c;\n$RefreshReg$(_c, \"SessionAlarmModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/modals/SessionAlarmModal.tsx\n"));

/***/ })

});