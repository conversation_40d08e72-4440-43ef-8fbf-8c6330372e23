"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/volume-2.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Volume2)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z\",\n            key: \"uqj9uw\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 9a5 5 0 0 1 0 6\",\n            key: \"1q6k2b\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M19.364 18.364a9 9 0 0 0 0-12.728\",\n            key: \"ijwkga\"\n        }\n    ]\n];\nconst Volume2 = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Volume2\", __iconNode);\n //# sourceMappingURL=volume-2.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/admin/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    _s();\n    const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSessionId, setEditingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingName, setEditingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentRuntime, setCurrentRuntime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [alarmModalOpen, setAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAlarmSession, setSelectedAlarmSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            loadSessions();\n            const sessionId = sessionManager.getCurrentSessionId();\n            setCurrentSessionId(sessionId);\n            // Initialize current runtime\n            if (sessionId) {\n                const runtime = sessionManager.getCurrentRuntime(sessionId);\n                setCurrentRuntime(runtime);\n            }\n            // Listen for storage changes to sync sessions across windows\n            const handleStorageChange = {\n                \"SessionManager.useEffect.handleStorageChange\": (event)=>{\n                    if (event.key === 'trading_sessions' && event.newValue) {\n                        loadSessions();\n                        console.log('🔄 Sessions synced from another window');\n                    }\n                }\n            }[\"SessionManager.useEffect.handleStorageChange\"];\n            window.addEventListener('storage', handleStorageChange);\n            return ({\n                \"SessionManager.useEffect\": ()=>{\n                    window.removeEventListener('storage', handleStorageChange);\n                }\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], []);\n    // Update runtime display every second for active sessions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            const interval = setInterval({\n                \"SessionManager.useEffect.interval\": ()=>{\n                    if (currentSessionId) {\n                        const runtime = sessionManager.getCurrentRuntime(currentSessionId);\n                        setCurrentRuntime(runtime);\n                        console.log(\"⏱️ Runtime update: \".concat(formatRuntime(runtime), \" for session \").concat(currentSessionId));\n                    }\n                }\n            }[\"SessionManager.useEffect.interval\"], 5000); // Update every 5 seconds instead of 1 second\n            return ({\n                \"SessionManager.useEffect\": ()=>clearInterval(interval)\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], [\n        currentSessionId,\n        sessionManager\n    ]);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        setSessions(allSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const handleSaveCurrentSession = async ()=>{\n        if (!currentSessionId) {\n            toast({\n                title: \"Error\",\n                description: \"No active session to save\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            // Check if there's already a saved version of this session\n            const currentSession = sessionManager.loadSession(currentSessionId);\n            if (!currentSession) {\n                toast({\n                    title: \"Error\",\n                    description: \"Current session not found\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Look for existing saved session with same base name (only manually saved ones)\n            const allSessions = sessionManager.getAllSessions();\n            const baseName = currentSession.name.replace(/ \\((Saved|AutoSaved).*\\)$/, ''); // Remove existing timestamp\n            const existingSavedSession = allSessions.find((s)=>s.id !== currentSessionId && s.name.startsWith(baseName) && s.name.includes('(Saved') && // Only look for manually saved sessions\n                !s.isActive // Only look in inactive sessions\n            );\n            let targetSessionId;\n            let savedName;\n            if (existingSavedSession) {\n                // Update existing saved session - update the timestamp to show latest save\n                targetSessionId = existingSavedSession.id;\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                console.log(\"\\uD83D\\uDCDD Updating existing saved session: \".concat(savedName));\n            } else {\n                // Create new saved session with timestamp\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                targetSessionId = await sessionManager.createNewSession(savedName, config);\n                console.log(\"\\uD83D\\uDCBE Creating new saved session: \".concat(savedName));\n            }\n            // Get current runtime from the active session\n            const currentRuntime = sessionManager.getCurrentRuntime(currentSessionId);\n            // Update the session name if it's an existing session\n            if (existingSavedSession) {\n                sessionManager.renameSession(targetSessionId, savedName);\n            }\n            // Save/update the session with current data and runtime\n            const success = sessionManager.saveSession(targetSessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, false, currentRuntime // Pass the current runtime to the saved session\n            );\n            // DO NOT deactivate the current session - keep it running!\n            // The current session should remain active for continued trading\n            if (success) {\n                loadSessions();\n                toast({\n                    title: \"Session Saved\",\n                    description: existingSavedSession ? \"Save checkpoint updated (Runtime: \".concat(formatRuntime(currentRuntime), \")\") : \"Session saved as checkpoint (Runtime: \".concat(formatRuntime(currentRuntime), \")\")\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to save session\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error saving session:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save session\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleLoadSession = (sessionId)=>{\n        const session = sessionManager.loadSession(sessionId);\n        if (!session) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to load session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Load session data into context\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: session.config\n        });\n        dispatch({\n            type: 'SET_TARGET_PRICE_ROWS',\n            payload: session.targetPriceRows\n        });\n        dispatch({\n            type: 'CLEAR_ORDER_HISTORY'\n        });\n        session.orderHistory.forEach((entry)=>{\n            dispatch({\n                type: 'ADD_ORDER_HISTORY_ENTRY',\n                payload: entry\n            });\n        });\n        dispatch({\n            type: 'SET_MARKET_PRICE',\n            payload: session.currentMarketPrice\n        });\n        dispatch({\n            type: 'SET_BALANCES',\n            payload: {\n                crypto1: session.crypto1Balance,\n                crypto2: session.crypto2Balance\n            }\n        });\n        sessionManager.setCurrentSession(sessionId);\n        setCurrentSessionId(sessionId);\n        loadSessions();\n        toast({\n            title: \"Session Loaded\",\n            description: 'Session \"'.concat(session.name, '\" has been loaded')\n        });\n    };\n    const handleDeleteSession = (sessionId)=>{\n        const success = sessionManager.deleteSession(sessionId);\n        if (success) {\n            if (currentSessionId === sessionId) {\n                setCurrentSessionId(null);\n            }\n            loadSessions();\n            toast({\n                title: \"Session Deleted\",\n                description: \"Session has been deleted successfully\"\n            });\n        }\n    };\n    const handleRenameSession = (sessionId)=>{\n        if (!editingName.trim()) return;\n        const success = sessionManager.renameSession(sessionId, editingName.trim());\n        if (success) {\n            setEditingSessionId(null);\n            setEditingName('');\n            loadSessions();\n            toast({\n                title: \"Session Renamed\",\n                description: \"Session has been renamed successfully\"\n            });\n        }\n    };\n    const handleExportSession = (sessionId)=>{\n        const csvContent = sessionManager.exportSessionToCSV(sessionId);\n        if (!csvContent) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to export session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const session = sessionManager.loadSession(sessionId);\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat((session === null || session === void 0 ? void 0 : session.name) || 'session', \"_\").concat(new Date().toISOString().split('T')[0], \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        toast({\n            title: \"Export Complete\",\n            description: \"Session data has been exported to CSV\"\n        });\n    };\n    const handleOpenAlarmModal = (sessionId, sessionName)=>{\n        setSelectedAlarmSession({\n            id: sessionId,\n            name: sessionName\n        });\n        setAlarmModalOpen(true);\n    };\n    const handleCloseAlarmModal = ()=>{\n        setAlarmModalOpen(false);\n        setSelectedAlarmSession(null);\n    };\n    const formatRuntime = (runtime)=>{\n        if (!runtime || runtime < 0) return '0s';\n        const totalSeconds = Math.floor(runtime / 1000);\n        const hours = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        if (hours > 0) {\n            return \"\".concat(hours, \"h \").concat(minutes, \"m \").concat(seconds, \"s\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"m \").concat(seconds, \"s\");\n        } else {\n            return \"\".concat(seconds, \"s\");\n        }\n    };\n    const getCurrentSession = ()=>{\n        return sessions.find((s)=>s.id === currentSessionId);\n    };\n    const getActiveSessions = ()=>{\n        return sessions.filter((s)=>s.isActive);\n    };\n    const getInactiveSessions = ()=>{\n        return sessions.filter((s)=>!s.isActive);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                \"Current Sessions\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: getActiveSessions().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Active Status\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Runtime\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: getActiveSessions().map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: editingName,\n                                                                onChange: (e)=>setEditingName(e.target.value),\n                                                                onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                className: \"text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleRenameSession(session.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: session.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"ghost\",\n                                                                onClick: ()=>{\n                                                                    setEditingSessionId(session.id);\n                                                                    setEditingName(session.name);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"default\",\n                                                        children: \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: session.id === currentSessionId ? formatRuntime(currentRuntime) : formatRuntime(session.runtime)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: session.id === currentSessionId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: handleSaveCurrentSession,\n                                                                size: \"sm\",\n                                                                className: \"btn-neo\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"mr-2 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 399,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Save\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenAlarmModal(session.id, session.name),\n                                                                title: \"Configure Alarms\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 402,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 417,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenAlarmModal(session.id, session.name),\n                                                                title: \"Configure Alarms\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 425,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, session.id, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No active session\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 437,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs\",\n                                    children: \"Start trading to create a session automatically\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Past Sessions (\",\n                                    getInactiveSessions().length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: [\n                                    \"Auto-saved: \",\n                                    getInactiveSessions().length,\n                                    \" | Manual: 0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 448,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                            className: \"h-[400px]\",\n                            children: getInactiveSessions().length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-muted-foreground py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No saved sessions yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: \"Save your current session to get started.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 460,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Session Name\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Active Status\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Total Runtime\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: getInactiveSessions().map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    value: editingName,\n                                                                    onChange: (e)=>setEditingName(e.target.value),\n                                                                    onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                    className: \"text-sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 482,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleRenameSession(session.id),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 489,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 488,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: session.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>{\n                                                                        setEditingSessionId(session.id);\n                                                                        setEditingName(session.name);\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 503,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 495,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            children: \"Inactive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 510,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: formatRuntime(sessionManager.getCurrentRuntime(session.id))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 521,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 523,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenAlarmModal(session.id, session.name),\n                                                                title: \"Configure Alarms\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 526,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteSession(session.id),\n                                                                title: \"Delete Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 534,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, session.id, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 457,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 447,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n        lineNumber: 331,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"L6FUFDQbKwrSFucr8UkAOpKzGbs=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SessionManager.tsx\n"));

/***/ })

});