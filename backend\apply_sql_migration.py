#!/usr/bin/env python3
"""
Apply SQL migration to add cooldown tracking columns.
"""

import sqlite3
import os

def apply_migration():
    """Apply the SQL migration to add cooldown columns."""
    
    db_path = 'instance/pluto.db'
    
    if not os.path.exists(db_path):
        print(f"❌ Database not found at: {db_path}")
        return False
    
    print(f"📁 Found database: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Checking current schema...")
        cursor.execute("PRAGMA table_info(bot_states)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"Current columns: {columns}")
        
        # Add last_global_buy_timestamp if missing
        if 'last_global_buy_timestamp' not in columns:
            print("➕ Adding last_global_buy_timestamp column...")
            cursor.execute("ALTER TABLE bot_states ADD COLUMN last_global_buy_timestamp REAL DEFAULT 0.0")
            print("✅ Added last_global_buy_timestamp")
        else:
            print("✅ last_global_buy_timestamp already exists")
        
        # Add last_sell_timestamp_per_counter if missing  
        if 'last_sell_timestamp_per_counter' not in columns:
            print("➕ Adding last_sell_timestamp_per_counter column...")
            cursor.execute("ALTER TABLE bot_states ADD COLUMN last_sell_timestamp_per_counter TEXT DEFAULT '{}'")
            print("✅ Added last_sell_timestamp_per_counter")
        else:
            print("✅ last_sell_timestamp_per_counter already exists")
        
        # Initialize existing records
        print("🔄 Initializing existing records...")
        cursor.execute("UPDATE bot_states SET last_global_buy_timestamp = 0.0 WHERE last_global_buy_timestamp IS NULL")
        updated_global = cursor.rowcount
        
        cursor.execute("UPDATE bot_states SET last_sell_timestamp_per_counter = '{}' WHERE last_sell_timestamp_per_counter IS NULL OR last_sell_timestamp_per_counter = ''")
        updated_counter = cursor.rowcount
        
        print(f"📊 Updated {updated_global} records with global timestamp")
        print(f"📊 Updated {updated_counter} records with counter timestamps")
        
        conn.commit()
        
        # Verify the changes
        print("🔍 Verifying changes...")
        cursor.execute("PRAGMA table_info(bot_states)")
        new_columns = [row[1] for row in cursor.fetchall()]
        print(f"Updated columns: {new_columns}")
        
        # Check if both new columns exist
        has_global = 'last_global_buy_timestamp' in new_columns
        has_counter = 'last_sell_timestamp_per_counter' in new_columns
        
        if has_global and has_counter:
            print("✅ Migration completed successfully!")
            print("🎯 Crystal clear cooldown tracking is now active!")
            print("\nThe bot will now support:")
            print("  - ⏰ Global buy cooldown (5 seconds between any buys)")
            print("  - 📊 Per-counter sell cooldown (60 seconds per counter)")
            print("  - 🎯 Sequential buying when multiple targets are in range")
            print("\n🚀 Restart the backend to activate the new features!")
        else:
            print("❌ Migration incomplete - some columns missing")
            return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Applying cooldown tracking migration...")
    success = apply_migration()
    if not success:
        print("\n❌ Migration failed. The backend will still work with backward compatibility.")
        print("💡 You can continue using the bot - it will work but without optimal cooldown tracking.")
