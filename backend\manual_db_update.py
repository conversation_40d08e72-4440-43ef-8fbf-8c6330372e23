#!/usr/bin/env python3
"""
Manual database update to add cooldown tracking fields.
This script directly modifies the database schema.
"""

import sqlite3
import os

def update_database():
    """Manually update the database to add cooldown tracking columns."""
    
    # Find the database file
    db_path = None
    possible_paths = [
        'instance/trading_bot.db',
        'trading_bot.db',
        '../trading_bot.db',
        'instance/app.db',
        'app.db'
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            db_path = path
            break
    
    if not db_path:
        print("❌ Database file not found. Checked paths:")
        for path in possible_paths:
            print(f"   - {path}")
        return False
    
    print(f"📁 Found database at: {db_path}")
    
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current schema
        cursor.execute("PRAGMA table_info(bot_states)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"📊 Current bot_states columns: {columns}")
        
        # Add last_global_buy_timestamp column if it doesn't exist
        if 'last_global_buy_timestamp' not in columns:
            print("➕ Adding last_global_buy_timestamp column...")
            cursor.execute("""
                ALTER TABLE bot_states 
                ADD COLUMN last_global_buy_timestamp REAL DEFAULT 0.0
            """)
            print("✅ Added last_global_buy_timestamp column")
        else:
            print("✅ last_global_buy_timestamp column already exists")
        
        # Add last_sell_timestamp_per_counter column if it doesn't exist
        if 'last_sell_timestamp_per_counter' not in columns:
            print("➕ Adding last_sell_timestamp_per_counter column...")
            cursor.execute("""
                ALTER TABLE bot_states 
                ADD COLUMN last_sell_timestamp_per_counter TEXT DEFAULT '{}'
            """)
            print("✅ Added last_sell_timestamp_per_counter column")
        else:
            print("✅ last_sell_timestamp_per_counter column already exists")
        
        # Initialize existing records with default values
        print("🔄 Initializing existing records with default cooldown values...")
        cursor.execute("""
            UPDATE bot_states 
            SET last_global_buy_timestamp = 0.0 
            WHERE last_global_buy_timestamp IS NULL
        """)
        
        cursor.execute("""
            UPDATE bot_states 
            SET last_sell_timestamp_per_counter = '{}' 
            WHERE last_sell_timestamp_per_counter IS NULL OR last_sell_timestamp_per_counter = ''
        """)
        
        # Commit changes
        conn.commit()
        
        # Verify the changes
        cursor.execute("PRAGMA table_info(bot_states)")
        updated_columns = [row[1] for row in cursor.fetchall()]
        print(f"📊 Updated bot_states columns: {updated_columns}")
        
        # Close connection
        conn.close()
        
        print("✅ Database migration completed successfully!")
        print("🎯 Crystal clear cooldown tracking is now properly implemented!")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Running manual database migration for cooldown tracking...")
    success = update_database()
    if success:
        print("\n🚀 You can now restart the backend and the cooldown logic will work properly!")
    else:
        print("\n❌ Migration failed. Please check the error messages above.")
