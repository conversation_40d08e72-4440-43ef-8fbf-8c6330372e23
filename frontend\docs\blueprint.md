# **App Name**: Pluto Trading Bot

## Core Features:

- Neo Brutalist UI: Neo Brutalist themed user interface for intuitive trading configuration and monitoring.
- Trading Simulation: Simulate trades based on user-defined target prices and trading modes.
- Simple Spot Mode Logic: Implement custom buy/sell logic for Simple Spot Mode.
- Stablecoin Swap Mode Logic: Implement custom buy/sell logic for Stablecoin Swap Mode.
- Trading Mode AI: The Trading Mode Selection AI Tool assists users in selecting optimal modes.

## Style Guidelines:

- Primary Background: Very dark grey or near-black (#1A1A1A).
- Secondary Backgrounds: Slightly lighter dark grey (#2C2C2C).
- Primary Text: Off-white or very light grey (#F0F0F0).
- Accent color: A bold, vibrant, slightly desaturated yellow or gold (e.g., #FFD700 but perhaps with less saturation, or a strong mustard like #FFDB58).
- Bold, utilitarian, sans-serif fonts (e.g., Montserrat, Bebas Neue, Inter with heavier weights). Emphasis on legibility and hierarchy through size and weight.
- Clear, functional, grid-based layouts. Sharp, visible borders.
- Simple, monochrome, line-art style icons if used.