/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/long";
exports.ids = ["vendor-chunks/long"];
exports.modules = {

/***/ "(action-browser)/./node_modules/long/umd/index.js":
/*!****************************************!*\
  !*** ./node_modules/long/umd/index.js ***!
  \****************************************/
/***/ ((module, exports) => {

eval("var __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;// GENERATED FILE. DO NOT EDIT.\nvar Long = (function(exports) {\n  \"use strict\";\n  \n  Object.defineProperty(exports, \"__esModule\", {\n    value: true\n  });\n  exports.default = void 0;\n  /**\n   * @license\n   * Copyright 2009 The Closure Library Authors\n   * Copyright 2020 Daniel Wirtz / The long.js Authors.\n   *\n   * Licensed under the Apache License, Version 2.0 (the \"License\");\n   * you may not use this file except in compliance with the License.\n   * You may obtain a copy of the License at\n   *\n   *     http://www.apache.org/licenses/LICENSE-2.0\n   *\n   * Unless required by applicable law or agreed to in writing, software\n   * distributed under the License is distributed on an \"AS IS\" BASIS,\n   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n   * See the License for the specific language governing permissions and\n   * limitations under the License.\n   *\n   * SPDX-License-Identifier: Apache-2.0\n   */\n  \n  // WebAssembly optimizations to do native i64 multiplication and divide\n  var wasm = null;\n  try {\n    wasm = new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([\n    // \\0asm\n    0, 97, 115, 109,\n    // version 1\n    1, 0, 0, 0,\n    // section \"type\"\n    1, 13, 2,\n    // 0, () => i32\n    96, 0, 1, 127,\n    // 1, (i32, i32, i32, i32) => i32\n    96, 4, 127, 127, 127, 127, 1, 127,\n    // section \"function\"\n    3, 7, 6,\n    // 0, type 0\n    0,\n    // 1, type 1\n    1,\n    // 2, type 1\n    1,\n    // 3, type 1\n    1,\n    // 4, type 1\n    1,\n    // 5, type 1\n    1,\n    // section \"global\"\n    6, 6, 1,\n    // 0, \"high\", mutable i32\n    127, 1, 65, 0, 11,\n    // section \"export\"\n    7, 50, 6,\n    // 0, \"mul\"\n    3, 109, 117, 108, 0, 1,\n    // 1, \"div_s\"\n    5, 100, 105, 118, 95, 115, 0, 2,\n    // 2, \"div_u\"\n    5, 100, 105, 118, 95, 117, 0, 3,\n    // 3, \"rem_s\"\n    5, 114, 101, 109, 95, 115, 0, 4,\n    // 4, \"rem_u\"\n    5, 114, 101, 109, 95, 117, 0, 5,\n    // 5, \"get_high\"\n    8, 103, 101, 116, 95, 104, 105, 103, 104, 0, 0,\n    // section \"code\"\n    10, 191, 1, 6,\n    // 0, \"get_high\"\n    4, 0, 35, 0, 11,\n    // 1, \"mul\"\n    36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 126, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11,\n    // 2, \"div_s\"\n    36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 127, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11,\n    // 3, \"div_u\"\n    36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 128, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11,\n    // 4, \"rem_s\"\n    36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 129, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11,\n    // 5, \"rem_u\"\n    36, 1, 1, 126, 32, 0, 173, 32, 1, 173, 66, 32, 134, 132, 32, 2, 173, 32, 3, 173, 66, 32, 134, 132, 130, 34, 4, 66, 32, 135, 167, 36, 0, 32, 4, 167, 11])), {}).exports;\n  } catch {\n    // no wasm support :(\n  }\n  \n  /**\n   * Constructs a 64 bit two's-complement integer, given its low and high 32 bit values as *signed* integers.\n   *  See the from* functions below for more convenient ways of constructing Longs.\n   * @exports Long\n   * @class A Long class for representing a 64 bit two's-complement integer value.\n   * @param {number} low The low (signed) 32 bits of the long\n   * @param {number} high The high (signed) 32 bits of the long\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @constructor\n   */\n  function Long(low, high, unsigned) {\n    /**\n     * The low 32 bits as a signed value.\n     * @type {number}\n     */\n    this.low = low | 0;\n  \n    /**\n     * The high 32 bits as a signed value.\n     * @type {number}\n     */\n    this.high = high | 0;\n  \n    /**\n     * Whether unsigned or not.\n     * @type {boolean}\n     */\n    this.unsigned = !!unsigned;\n  }\n  \n  // The internal representation of a long is the two given signed, 32-bit values.\n  // We use 32-bit pieces because these are the size of integers on which\n  // Javascript performs bit-operations.  For operations like addition and\n  // multiplication, we split each number into 16 bit pieces, which can easily be\n  // multiplied within Javascript's floating-point representation without overflow\n  // or change in sign.\n  //\n  // In the algorithms below, we frequently reduce the negative case to the\n  // positive case by negating the input(s) and then post-processing the result.\n  // Note that we must ALWAYS check specially whether those values are MIN_VALUE\n  // (-2^63) because -MIN_VALUE == MIN_VALUE (since 2^63 cannot be represented as\n  // a positive number, it overflows back into a negative).  Not handling this\n  // case would often result in infinite recursion.\n  //\n  // Common constant values ZERO, ONE, NEG_ONE, etc. are defined below the from*\n  // methods on which they depend.\n  \n  /**\n   * An indicator used to reliably determine if an object is a Long or not.\n   * @type {boolean}\n   * @const\n   * @private\n   */\n  Long.prototype.__isLong__;\n  Object.defineProperty(Long.prototype, \"__isLong__\", {\n    value: true\n  });\n  \n  /**\n   * @function\n   * @param {*} obj Object\n   * @returns {boolean}\n   * @inner\n   */\n  function isLong(obj) {\n    return (obj && obj[\"__isLong__\"]) === true;\n  }\n  \n  /**\n   * @function\n   * @param {*} value number\n   * @returns {number}\n   * @inner\n   */\n  function ctz32(value) {\n    var c = Math.clz32(value & -value);\n    return value ? 31 - c : c;\n  }\n  \n  /**\n   * Tests if the specified object is a Long.\n   * @function\n   * @param {*} obj Object\n   * @returns {boolean}\n   */\n  Long.isLong = isLong;\n  \n  /**\n   * A cache of the Long representations of small integer values.\n   * @type {!Object}\n   * @inner\n   */\n  var INT_CACHE = {};\n  \n  /**\n   * A cache of the Long representations of small unsigned integer values.\n   * @type {!Object}\n   * @inner\n   */\n  var UINT_CACHE = {};\n  \n  /**\n   * @param {number} value\n   * @param {boolean=} unsigned\n   * @returns {!Long}\n   * @inner\n   */\n  function fromInt(value, unsigned) {\n    var obj, cachedObj, cache;\n    if (unsigned) {\n      value >>>= 0;\n      if (cache = 0 <= value && value < 256) {\n        cachedObj = UINT_CACHE[value];\n        if (cachedObj) return cachedObj;\n      }\n      obj = fromBits(value, 0, true);\n      if (cache) UINT_CACHE[value] = obj;\n      return obj;\n    } else {\n      value |= 0;\n      if (cache = -128 <= value && value < 128) {\n        cachedObj = INT_CACHE[value];\n        if (cachedObj) return cachedObj;\n      }\n      obj = fromBits(value, value < 0 ? -1 : 0, false);\n      if (cache) INT_CACHE[value] = obj;\n      return obj;\n    }\n  }\n  \n  /**\n   * Returns a Long representing the given 32 bit integer value.\n   * @function\n   * @param {number} value The 32 bit integer in question\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @returns {!Long} The corresponding Long value\n   */\n  Long.fromInt = fromInt;\n  \n  /**\n   * @param {number} value\n   * @param {boolean=} unsigned\n   * @returns {!Long}\n   * @inner\n   */\n  function fromNumber(value, unsigned) {\n    if (isNaN(value)) return unsigned ? UZERO : ZERO;\n    if (unsigned) {\n      if (value < 0) return UZERO;\n      if (value >= TWO_PWR_64_DBL) return MAX_UNSIGNED_VALUE;\n    } else {\n      if (value <= -TWO_PWR_63_DBL) return MIN_VALUE;\n      if (value + 1 >= TWO_PWR_63_DBL) return MAX_VALUE;\n    }\n    if (value < 0) return fromNumber(-value, unsigned).neg();\n    return fromBits(value % TWO_PWR_32_DBL | 0, value / TWO_PWR_32_DBL | 0, unsigned);\n  }\n  \n  /**\n   * Returns a Long representing the given value, provided that it is a finite number. Otherwise, zero is returned.\n   * @function\n   * @param {number} value The number in question\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @returns {!Long} The corresponding Long value\n   */\n  Long.fromNumber = fromNumber;\n  \n  /**\n   * @param {number} lowBits\n   * @param {number} highBits\n   * @param {boolean=} unsigned\n   * @returns {!Long}\n   * @inner\n   */\n  function fromBits(lowBits, highBits, unsigned) {\n    return new Long(lowBits, highBits, unsigned);\n  }\n  \n  /**\n   * Returns a Long representing the 64 bit integer that comes by concatenating the given low and high bits. Each is\n   *  assumed to use 32 bits.\n   * @function\n   * @param {number} lowBits The low 32 bits\n   * @param {number} highBits The high 32 bits\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @returns {!Long} The corresponding Long value\n   */\n  Long.fromBits = fromBits;\n  \n  /**\n   * @function\n   * @param {number} base\n   * @param {number} exponent\n   * @returns {number}\n   * @inner\n   */\n  var pow_dbl = Math.pow; // Used 4 times (4*8 to 15+4)\n  \n  /**\n   * @param {string} str\n   * @param {(boolean|number)=} unsigned\n   * @param {number=} radix\n   * @returns {!Long}\n   * @inner\n   */\n  function fromString(str, unsigned, radix) {\n    if (str.length === 0) throw Error('empty string');\n    if (typeof unsigned === 'number') {\n      // For goog.math.long compatibility\n      radix = unsigned;\n      unsigned = false;\n    } else {\n      unsigned = !!unsigned;\n    }\n    if (str === \"NaN\" || str === \"Infinity\" || str === \"+Infinity\" || str === \"-Infinity\") return unsigned ? UZERO : ZERO;\n    radix = radix || 10;\n    if (radix < 2 || 36 < radix) throw RangeError('radix');\n    var p;\n    if ((p = str.indexOf('-')) > 0) throw Error('interior hyphen');else if (p === 0) {\n      return fromString(str.substring(1), unsigned, radix).neg();\n    }\n  \n    // Do several (8) digits each time through the loop, so as to\n    // minimize the calls to the very expensive emulated div.\n    var radixToPower = fromNumber(pow_dbl(radix, 8));\n    var result = ZERO;\n    for (var i = 0; i < str.length; i += 8) {\n      var size = Math.min(8, str.length - i),\n        value = parseInt(str.substring(i, i + size), radix);\n      if (size < 8) {\n        var power = fromNumber(pow_dbl(radix, size));\n        result = result.mul(power).add(fromNumber(value));\n      } else {\n        result = result.mul(radixToPower);\n        result = result.add(fromNumber(value));\n      }\n    }\n    result.unsigned = unsigned;\n    return result;\n  }\n  \n  /**\n   * Returns a Long representation of the given string, written using the specified radix.\n   * @function\n   * @param {string} str The textual representation of the Long\n   * @param {(boolean|number)=} unsigned Whether unsigned or not, defaults to signed\n   * @param {number=} radix The radix in which the text is written (2-36), defaults to 10\n   * @returns {!Long} The corresponding Long value\n   */\n  Long.fromString = fromString;\n  \n  /**\n   * @function\n   * @param {!Long|number|string|!{low: number, high: number, unsigned: boolean}} val\n   * @param {boolean=} unsigned\n   * @returns {!Long}\n   * @inner\n   */\n  function fromValue(val, unsigned) {\n    if (typeof val === 'number') return fromNumber(val, unsigned);\n    if (typeof val === 'string') return fromString(val, unsigned);\n    // Throws for non-objects, converts non-instanceof Long:\n    return fromBits(val.low, val.high, typeof unsigned === 'boolean' ? unsigned : val.unsigned);\n  }\n  \n  /**\n   * Converts the specified value to a Long using the appropriate from* function for its type.\n   * @function\n   * @param {!Long|number|bigint|string|!{low: number, high: number, unsigned: boolean}} val Value\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @returns {!Long}\n   */\n  Long.fromValue = fromValue;\n  \n  // NOTE: the compiler should inline these constant values below and then remove these variables, so there should be\n  // no runtime penalty for these.\n  \n  /**\n   * @type {number}\n   * @const\n   * @inner\n   */\n  var TWO_PWR_16_DBL = 1 << 16;\n  \n  /**\n   * @type {number}\n   * @const\n   * @inner\n   */\n  var TWO_PWR_24_DBL = 1 << 24;\n  \n  /**\n   * @type {number}\n   * @const\n   * @inner\n   */\n  var TWO_PWR_32_DBL = TWO_PWR_16_DBL * TWO_PWR_16_DBL;\n  \n  /**\n   * @type {number}\n   * @const\n   * @inner\n   */\n  var TWO_PWR_64_DBL = TWO_PWR_32_DBL * TWO_PWR_32_DBL;\n  \n  /**\n   * @type {number}\n   * @const\n   * @inner\n   */\n  var TWO_PWR_63_DBL = TWO_PWR_64_DBL / 2;\n  \n  /**\n   * @type {!Long}\n   * @const\n   * @inner\n   */\n  var TWO_PWR_24 = fromInt(TWO_PWR_24_DBL);\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var ZERO = fromInt(0);\n  \n  /**\n   * Signed zero.\n   * @type {!Long}\n   */\n  Long.ZERO = ZERO;\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var UZERO = fromInt(0, true);\n  \n  /**\n   * Unsigned zero.\n   * @type {!Long}\n   */\n  Long.UZERO = UZERO;\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var ONE = fromInt(1);\n  \n  /**\n   * Signed one.\n   * @type {!Long}\n   */\n  Long.ONE = ONE;\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var UONE = fromInt(1, true);\n  \n  /**\n   * Unsigned one.\n   * @type {!Long}\n   */\n  Long.UONE = UONE;\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var NEG_ONE = fromInt(-1);\n  \n  /**\n   * Signed negative one.\n   * @type {!Long}\n   */\n  Long.NEG_ONE = NEG_ONE;\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var MAX_VALUE = fromBits(0xFFFFFFFF | 0, 0x7FFFFFFF | 0, false);\n  \n  /**\n   * Maximum signed value.\n   * @type {!Long}\n   */\n  Long.MAX_VALUE = MAX_VALUE;\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var MAX_UNSIGNED_VALUE = fromBits(0xFFFFFFFF | 0, 0xFFFFFFFF | 0, true);\n  \n  /**\n   * Maximum unsigned value.\n   * @type {!Long}\n   */\n  Long.MAX_UNSIGNED_VALUE = MAX_UNSIGNED_VALUE;\n  \n  /**\n   * @type {!Long}\n   * @inner\n   */\n  var MIN_VALUE = fromBits(0, 0x80000000 | 0, false);\n  \n  /**\n   * Minimum signed value.\n   * @type {!Long}\n   */\n  Long.MIN_VALUE = MIN_VALUE;\n  \n  /**\n   * @alias Long.prototype\n   * @inner\n   */\n  var LongPrototype = Long.prototype;\n  \n  /**\n   * Converts the Long to a 32 bit integer, assuming it is a 32 bit integer.\n   * @this {!Long}\n   * @returns {number}\n   */\n  LongPrototype.toInt = function toInt() {\n    return this.unsigned ? this.low >>> 0 : this.low;\n  };\n  \n  /**\n   * Converts the Long to a the nearest floating-point representation of this value (double, 53 bit mantissa).\n   * @this {!Long}\n   * @returns {number}\n   */\n  LongPrototype.toNumber = function toNumber() {\n    if (this.unsigned) return (this.high >>> 0) * TWO_PWR_32_DBL + (this.low >>> 0);\n    return this.high * TWO_PWR_32_DBL + (this.low >>> 0);\n  };\n  \n  /**\n   * Converts the Long to a string written in the specified radix.\n   * @this {!Long}\n   * @param {number=} radix Radix (2-36), defaults to 10\n   * @returns {string}\n   * @override\n   * @throws {RangeError} If `radix` is out of range\n   */\n  LongPrototype.toString = function toString(radix) {\n    radix = radix || 10;\n    if (radix < 2 || 36 < radix) throw RangeError('radix');\n    if (this.isZero()) return '0';\n    if (this.isNegative()) {\n      // Unsigned Longs are never negative\n      if (this.eq(MIN_VALUE)) {\n        // We need to change the Long value before it can be negated, so we remove\n        // the bottom-most digit in this base and then recurse to do the rest.\n        var radixLong = fromNumber(radix),\n          div = this.div(radixLong),\n          rem1 = div.mul(radixLong).sub(this);\n        return div.toString(radix) + rem1.toInt().toString(radix);\n      } else return '-' + this.neg().toString(radix);\n    }\n  \n    // Do several (6) digits each time through the loop, so as to\n    // minimize the calls to the very expensive emulated div.\n    var radixToPower = fromNumber(pow_dbl(radix, 6), this.unsigned),\n      rem = this;\n    var result = '';\n    while (true) {\n      var remDiv = rem.div(radixToPower),\n        intval = rem.sub(remDiv.mul(radixToPower)).toInt() >>> 0,\n        digits = intval.toString(radix);\n      rem = remDiv;\n      if (rem.isZero()) return digits + result;else {\n        while (digits.length < 6) digits = '0' + digits;\n        result = '' + digits + result;\n      }\n    }\n  };\n  \n  /**\n   * Gets the high 32 bits as a signed integer.\n   * @this {!Long}\n   * @returns {number} Signed high bits\n   */\n  LongPrototype.getHighBits = function getHighBits() {\n    return this.high;\n  };\n  \n  /**\n   * Gets the high 32 bits as an unsigned integer.\n   * @this {!Long}\n   * @returns {number} Unsigned high bits\n   */\n  LongPrototype.getHighBitsUnsigned = function getHighBitsUnsigned() {\n    return this.high >>> 0;\n  };\n  \n  /**\n   * Gets the low 32 bits as a signed integer.\n   * @this {!Long}\n   * @returns {number} Signed low bits\n   */\n  LongPrototype.getLowBits = function getLowBits() {\n    return this.low;\n  };\n  \n  /**\n   * Gets the low 32 bits as an unsigned integer.\n   * @this {!Long}\n   * @returns {number} Unsigned low bits\n   */\n  LongPrototype.getLowBitsUnsigned = function getLowBitsUnsigned() {\n    return this.low >>> 0;\n  };\n  \n  /**\n   * Gets the number of bits needed to represent the absolute value of this Long.\n   * @this {!Long}\n   * @returns {number}\n   */\n  LongPrototype.getNumBitsAbs = function getNumBitsAbs() {\n    if (this.isNegative())\n      // Unsigned Longs are never negative\n      return this.eq(MIN_VALUE) ? 64 : this.neg().getNumBitsAbs();\n    var val = this.high != 0 ? this.high : this.low;\n    for (var bit = 31; bit > 0; bit--) if ((val & 1 << bit) != 0) break;\n    return this.high != 0 ? bit + 33 : bit + 1;\n  };\n  \n  /**\n   * Tests if this Long can be safely represented as a JavaScript number.\n   * @this {!Long}\n   * @returns {boolean}\n   */\n  LongPrototype.isSafeInteger = function isSafeInteger() {\n    // 2^53-1 is the maximum safe value\n    var top11Bits = this.high >> 21;\n    // [0, 2^53-1]\n    if (!top11Bits) return true;\n    // > 2^53-1\n    if (this.unsigned) return false;\n    // [-2^53, -1] except -2^53\n    return top11Bits === -1 && !(this.low === 0 && this.high === -0x200000);\n  };\n  \n  /**\n   * Tests if this Long's value equals zero.\n   * @this {!Long}\n   * @returns {boolean}\n   */\n  LongPrototype.isZero = function isZero() {\n    return this.high === 0 && this.low === 0;\n  };\n  \n  /**\n   * Tests if this Long's value equals zero. This is an alias of {@link Long#isZero}.\n   * @returns {boolean}\n   */\n  LongPrototype.eqz = LongPrototype.isZero;\n  \n  /**\n   * Tests if this Long's value is negative.\n   * @this {!Long}\n   * @returns {boolean}\n   */\n  LongPrototype.isNegative = function isNegative() {\n    return !this.unsigned && this.high < 0;\n  };\n  \n  /**\n   * Tests if this Long's value is positive or zero.\n   * @this {!Long}\n   * @returns {boolean}\n   */\n  LongPrototype.isPositive = function isPositive() {\n    return this.unsigned || this.high >= 0;\n  };\n  \n  /**\n   * Tests if this Long's value is odd.\n   * @this {!Long}\n   * @returns {boolean}\n   */\n  LongPrototype.isOdd = function isOdd() {\n    return (this.low & 1) === 1;\n  };\n  \n  /**\n   * Tests if this Long's value is even.\n   * @this {!Long}\n   * @returns {boolean}\n   */\n  LongPrototype.isEven = function isEven() {\n    return (this.low & 1) === 0;\n  };\n  \n  /**\n   * Tests if this Long's value equals the specified's.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.equals = function equals(other) {\n    if (!isLong(other)) other = fromValue(other);\n    if (this.unsigned !== other.unsigned && this.high >>> 31 === 1 && other.high >>> 31 === 1) return false;\n    return this.high === other.high && this.low === other.low;\n  };\n  \n  /**\n   * Tests if this Long's value equals the specified's. This is an alias of {@link Long#equals}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.eq = LongPrototype.equals;\n  \n  /**\n   * Tests if this Long's value differs from the specified's.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.notEquals = function notEquals(other) {\n    return !this.eq(/* validates */other);\n  };\n  \n  /**\n   * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.neq = LongPrototype.notEquals;\n  \n  /**\n   * Tests if this Long's value differs from the specified's. This is an alias of {@link Long#notEquals}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.ne = LongPrototype.notEquals;\n  \n  /**\n   * Tests if this Long's value is less than the specified's.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.lessThan = function lessThan(other) {\n    return this.comp(/* validates */other) < 0;\n  };\n  \n  /**\n   * Tests if this Long's value is less than the specified's. This is an alias of {@link Long#lessThan}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.lt = LongPrototype.lessThan;\n  \n  /**\n   * Tests if this Long's value is less than or equal the specified's.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.lessThanOrEqual = function lessThanOrEqual(other) {\n    return this.comp(/* validates */other) <= 0;\n  };\n  \n  /**\n   * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.lte = LongPrototype.lessThanOrEqual;\n  \n  /**\n   * Tests if this Long's value is less than or equal the specified's. This is an alias of {@link Long#lessThanOrEqual}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.le = LongPrototype.lessThanOrEqual;\n  \n  /**\n   * Tests if this Long's value is greater than the specified's.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.greaterThan = function greaterThan(other) {\n    return this.comp(/* validates */other) > 0;\n  };\n  \n  /**\n   * Tests if this Long's value is greater than the specified's. This is an alias of {@link Long#greaterThan}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.gt = LongPrototype.greaterThan;\n  \n  /**\n   * Tests if this Long's value is greater than or equal the specified's.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.greaterThanOrEqual = function greaterThanOrEqual(other) {\n    return this.comp(/* validates */other) >= 0;\n  };\n  \n  /**\n   * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.gte = LongPrototype.greaterThanOrEqual;\n  \n  /**\n   * Tests if this Long's value is greater than or equal the specified's. This is an alias of {@link Long#greaterThanOrEqual}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {boolean}\n   */\n  LongPrototype.ge = LongPrototype.greaterThanOrEqual;\n  \n  /**\n   * Compares this Long's value with the specified's.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n   *  if the given one is greater\n   */\n  LongPrototype.compare = function compare(other) {\n    if (!isLong(other)) other = fromValue(other);\n    if (this.eq(other)) return 0;\n    var thisNeg = this.isNegative(),\n      otherNeg = other.isNegative();\n    if (thisNeg && !otherNeg) return -1;\n    if (!thisNeg && otherNeg) return 1;\n    // At this point the sign bits are the same\n    if (!this.unsigned) return this.sub(other).isNegative() ? -1 : 1;\n    // Both are positive if at least one is unsigned\n    return other.high >>> 0 > this.high >>> 0 || other.high === this.high && other.low >>> 0 > this.low >>> 0 ? -1 : 1;\n  };\n  \n  /**\n   * Compares this Long's value with the specified's. This is an alias of {@link Long#compare}.\n   * @function\n   * @param {!Long|number|bigint|string} other Other value\n   * @returns {number} 0 if they are the same, 1 if the this is greater and -1\n   *  if the given one is greater\n   */\n  LongPrototype.comp = LongPrototype.compare;\n  \n  /**\n   * Negates this Long's value.\n   * @this {!Long}\n   * @returns {!Long} Negated Long\n   */\n  LongPrototype.negate = function negate() {\n    if (!this.unsigned && this.eq(MIN_VALUE)) return MIN_VALUE;\n    return this.not().add(ONE);\n  };\n  \n  /**\n   * Negates this Long's value. This is an alias of {@link Long#negate}.\n   * @function\n   * @returns {!Long} Negated Long\n   */\n  LongPrototype.neg = LongPrototype.negate;\n  \n  /**\n   * Returns the sum of this and the specified Long.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} addend Addend\n   * @returns {!Long} Sum\n   */\n  LongPrototype.add = function add(addend) {\n    if (!isLong(addend)) addend = fromValue(addend);\n  \n    // Divide each number into 4 chunks of 16 bits, and then sum the chunks.\n  \n    var a48 = this.high >>> 16;\n    var a32 = this.high & 0xFFFF;\n    var a16 = this.low >>> 16;\n    var a00 = this.low & 0xFFFF;\n    var b48 = addend.high >>> 16;\n    var b32 = addend.high & 0xFFFF;\n    var b16 = addend.low >>> 16;\n    var b00 = addend.low & 0xFFFF;\n    var c48 = 0,\n      c32 = 0,\n      c16 = 0,\n      c00 = 0;\n    c00 += a00 + b00;\n    c16 += c00 >>> 16;\n    c00 &= 0xFFFF;\n    c16 += a16 + b16;\n    c32 += c16 >>> 16;\n    c16 &= 0xFFFF;\n    c32 += a32 + b32;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c48 += a48 + b48;\n    c48 &= 0xFFFF;\n    return fromBits(c16 << 16 | c00, c48 << 16 | c32, this.unsigned);\n  };\n  \n  /**\n   * Returns the difference of this and the specified Long.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} subtrahend Subtrahend\n   * @returns {!Long} Difference\n   */\n  LongPrototype.subtract = function subtract(subtrahend) {\n    if (!isLong(subtrahend)) subtrahend = fromValue(subtrahend);\n    return this.add(subtrahend.neg());\n  };\n  \n  /**\n   * Returns the difference of this and the specified Long. This is an alias of {@link Long#subtract}.\n   * @function\n   * @param {!Long|number|bigint|string} subtrahend Subtrahend\n   * @returns {!Long} Difference\n   */\n  LongPrototype.sub = LongPrototype.subtract;\n  \n  /**\n   * Returns the product of this and the specified Long.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} multiplier Multiplier\n   * @returns {!Long} Product\n   */\n  LongPrototype.multiply = function multiply(multiplier) {\n    if (this.isZero()) return this;\n    if (!isLong(multiplier)) multiplier = fromValue(multiplier);\n  \n    // use wasm support if present\n    if (wasm) {\n      var low = wasm[\"mul\"](this.low, this.high, multiplier.low, multiplier.high);\n      return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n    }\n    if (multiplier.isZero()) return this.unsigned ? UZERO : ZERO;\n    if (this.eq(MIN_VALUE)) return multiplier.isOdd() ? MIN_VALUE : ZERO;\n    if (multiplier.eq(MIN_VALUE)) return this.isOdd() ? MIN_VALUE : ZERO;\n    if (this.isNegative()) {\n      if (multiplier.isNegative()) return this.neg().mul(multiplier.neg());else return this.neg().mul(multiplier).neg();\n    } else if (multiplier.isNegative()) return this.mul(multiplier.neg()).neg();\n  \n    // If both longs are small, use float multiplication\n    if (this.lt(TWO_PWR_24) && multiplier.lt(TWO_PWR_24)) return fromNumber(this.toNumber() * multiplier.toNumber(), this.unsigned);\n  \n    // Divide each long into 4 chunks of 16 bits, and then add up 4x4 products.\n    // We can skip products that would overflow.\n  \n    var a48 = this.high >>> 16;\n    var a32 = this.high & 0xFFFF;\n    var a16 = this.low >>> 16;\n    var a00 = this.low & 0xFFFF;\n    var b48 = multiplier.high >>> 16;\n    var b32 = multiplier.high & 0xFFFF;\n    var b16 = multiplier.low >>> 16;\n    var b00 = multiplier.low & 0xFFFF;\n    var c48 = 0,\n      c32 = 0,\n      c16 = 0,\n      c00 = 0;\n    c00 += a00 * b00;\n    c16 += c00 >>> 16;\n    c00 &= 0xFFFF;\n    c16 += a16 * b00;\n    c32 += c16 >>> 16;\n    c16 &= 0xFFFF;\n    c16 += a00 * b16;\n    c32 += c16 >>> 16;\n    c16 &= 0xFFFF;\n    c32 += a32 * b00;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c32 += a16 * b16;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c32 += a00 * b32;\n    c48 += c32 >>> 16;\n    c32 &= 0xFFFF;\n    c48 += a48 * b00 + a32 * b16 + a16 * b32 + a00 * b48;\n    c48 &= 0xFFFF;\n    return fromBits(c16 << 16 | c00, c48 << 16 | c32, this.unsigned);\n  };\n  \n  /**\n   * Returns the product of this and the specified Long. This is an alias of {@link Long#multiply}.\n   * @function\n   * @param {!Long|number|bigint|string} multiplier Multiplier\n   * @returns {!Long} Product\n   */\n  LongPrototype.mul = LongPrototype.multiply;\n  \n  /**\n   * Returns this Long divided by the specified. The result is signed if this Long is signed or\n   *  unsigned if this Long is unsigned.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} divisor Divisor\n   * @returns {!Long} Quotient\n   */\n  LongPrototype.divide = function divide(divisor) {\n    if (!isLong(divisor)) divisor = fromValue(divisor);\n    if (divisor.isZero()) throw Error('division by zero');\n  \n    // use wasm support if present\n    if (wasm) {\n      // guard against signed division overflow: the largest\n      // negative number / -1 would be 1 larger than the largest\n      // positive number, due to two's complement.\n      if (!this.unsigned && this.high === -0x80000000 && divisor.low === -1 && divisor.high === -1) {\n        // be consistent with non-wasm code path\n        return this;\n      }\n      var low = (this.unsigned ? wasm[\"div_u\"] : wasm[\"div_s\"])(this.low, this.high, divisor.low, divisor.high);\n      return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n    }\n    if (this.isZero()) return this.unsigned ? UZERO : ZERO;\n    var approx, rem, res;\n    if (!this.unsigned) {\n      // This section is only relevant for signed longs and is derived from the\n      // closure library as a whole.\n      if (this.eq(MIN_VALUE)) {\n        if (divisor.eq(ONE) || divisor.eq(NEG_ONE)) return MIN_VALUE; // recall that -MIN_VALUE == MIN_VALUE\n        else if (divisor.eq(MIN_VALUE)) return ONE;else {\n          // At this point, we have |other| >= 2, so |this/other| < |MIN_VALUE|.\n          var halfThis = this.shr(1);\n          approx = halfThis.div(divisor).shl(1);\n          if (approx.eq(ZERO)) {\n            return divisor.isNegative() ? ONE : NEG_ONE;\n          } else {\n            rem = this.sub(divisor.mul(approx));\n            res = approx.add(rem.div(divisor));\n            return res;\n          }\n        }\n      } else if (divisor.eq(MIN_VALUE)) return this.unsigned ? UZERO : ZERO;\n      if (this.isNegative()) {\n        if (divisor.isNegative()) return this.neg().div(divisor.neg());\n        return this.neg().div(divisor).neg();\n      } else if (divisor.isNegative()) return this.div(divisor.neg()).neg();\n      res = ZERO;\n    } else {\n      // The algorithm below has not been made for unsigned longs. It's therefore\n      // required to take special care of the MSB prior to running it.\n      if (!divisor.unsigned) divisor = divisor.toUnsigned();\n      if (divisor.gt(this)) return UZERO;\n      if (divisor.gt(this.shru(1)))\n        // 15 >>> 1 = 7 ; with divisor = 8 ; true\n        return UONE;\n      res = UZERO;\n    }\n  \n    // Repeat the following until the remainder is less than other:  find a\n    // floating-point that approximates remainder / other *from below*, add this\n    // into the result, and subtract it from the remainder.  It is critical that\n    // the approximate value is less than or equal to the real value so that the\n    // remainder never becomes negative.\n    rem = this;\n    while (rem.gte(divisor)) {\n      // Approximate the result of division. This may be a little greater or\n      // smaller than the actual value.\n      approx = Math.max(1, Math.floor(rem.toNumber() / divisor.toNumber()));\n  \n      // We will tweak the approximate result by changing it in the 48-th digit or\n      // the smallest non-fractional digit, whichever is larger.\n      var log2 = Math.ceil(Math.log(approx) / Math.LN2),\n        delta = log2 <= 48 ? 1 : pow_dbl(2, log2 - 48),\n        // Decrease the approximation until it is smaller than the remainder.  Note\n        // that if it is too large, the product overflows and is negative.\n        approxRes = fromNumber(approx),\n        approxRem = approxRes.mul(divisor);\n      while (approxRem.isNegative() || approxRem.gt(rem)) {\n        approx -= delta;\n        approxRes = fromNumber(approx, this.unsigned);\n        approxRem = approxRes.mul(divisor);\n      }\n  \n      // We know the answer can't be zero... and actually, zero would cause\n      // infinite recursion since we would make no progress.\n      if (approxRes.isZero()) approxRes = ONE;\n      res = res.add(approxRes);\n      rem = rem.sub(approxRem);\n    }\n    return res;\n  };\n  \n  /**\n   * Returns this Long divided by the specified. This is an alias of {@link Long#divide}.\n   * @function\n   * @param {!Long|number|bigint|string} divisor Divisor\n   * @returns {!Long} Quotient\n   */\n  LongPrototype.div = LongPrototype.divide;\n  \n  /**\n   * Returns this Long modulo the specified.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} divisor Divisor\n   * @returns {!Long} Remainder\n   */\n  LongPrototype.modulo = function modulo(divisor) {\n    if (!isLong(divisor)) divisor = fromValue(divisor);\n  \n    // use wasm support if present\n    if (wasm) {\n      var low = (this.unsigned ? wasm[\"rem_u\"] : wasm[\"rem_s\"])(this.low, this.high, divisor.low, divisor.high);\n      return fromBits(low, wasm[\"get_high\"](), this.unsigned);\n    }\n    return this.sub(this.div(divisor).mul(divisor));\n  };\n  \n  /**\n   * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n   * @function\n   * @param {!Long|number|bigint|string} divisor Divisor\n   * @returns {!Long} Remainder\n   */\n  LongPrototype.mod = LongPrototype.modulo;\n  \n  /**\n   * Returns this Long modulo the specified. This is an alias of {@link Long#modulo}.\n   * @function\n   * @param {!Long|number|bigint|string} divisor Divisor\n   * @returns {!Long} Remainder\n   */\n  LongPrototype.rem = LongPrototype.modulo;\n  \n  /**\n   * Returns the bitwise NOT of this Long.\n   * @this {!Long}\n   * @returns {!Long}\n   */\n  LongPrototype.not = function not() {\n    return fromBits(~this.low, ~this.high, this.unsigned);\n  };\n  \n  /**\n   * Returns count leading zeros of this Long.\n   * @this {!Long}\n   * @returns {!number}\n   */\n  LongPrototype.countLeadingZeros = function countLeadingZeros() {\n    return this.high ? Math.clz32(this.high) : Math.clz32(this.low) + 32;\n  };\n  \n  /**\n   * Returns count leading zeros. This is an alias of {@link Long#countLeadingZeros}.\n   * @function\n   * @param {!Long}\n   * @returns {!number}\n   */\n  LongPrototype.clz = LongPrototype.countLeadingZeros;\n  \n  /**\n   * Returns count trailing zeros of this Long.\n   * @this {!Long}\n   * @returns {!number}\n   */\n  LongPrototype.countTrailingZeros = function countTrailingZeros() {\n    return this.low ? ctz32(this.low) : ctz32(this.high) + 32;\n  };\n  \n  /**\n   * Returns count trailing zeros. This is an alias of {@link Long#countTrailingZeros}.\n   * @function\n   * @param {!Long}\n   * @returns {!number}\n   */\n  LongPrototype.ctz = LongPrototype.countTrailingZeros;\n  \n  /**\n   * Returns the bitwise AND of this Long and the specified.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other Long\n   * @returns {!Long}\n   */\n  LongPrototype.and = function and(other) {\n    if (!isLong(other)) other = fromValue(other);\n    return fromBits(this.low & other.low, this.high & other.high, this.unsigned);\n  };\n  \n  /**\n   * Returns the bitwise OR of this Long and the specified.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other Long\n   * @returns {!Long}\n   */\n  LongPrototype.or = function or(other) {\n    if (!isLong(other)) other = fromValue(other);\n    return fromBits(this.low | other.low, this.high | other.high, this.unsigned);\n  };\n  \n  /**\n   * Returns the bitwise XOR of this Long and the given one.\n   * @this {!Long}\n   * @param {!Long|number|bigint|string} other Other Long\n   * @returns {!Long}\n   */\n  LongPrototype.xor = function xor(other) {\n    if (!isLong(other)) other = fromValue(other);\n    return fromBits(this.low ^ other.low, this.high ^ other.high, this.unsigned);\n  };\n  \n  /**\n   * Returns this Long with bits shifted to the left by the given amount.\n   * @this {!Long}\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Shifted Long\n   */\n  LongPrototype.shiftLeft = function shiftLeft(numBits) {\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;else if (numBits < 32) return fromBits(this.low << numBits, this.high << numBits | this.low >>> 32 - numBits, this.unsigned);else return fromBits(0, this.low << numBits - 32, this.unsigned);\n  };\n  \n  /**\n   * Returns this Long with bits shifted to the left by the given amount. This is an alias of {@link Long#shiftLeft}.\n   * @function\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Shifted Long\n   */\n  LongPrototype.shl = LongPrototype.shiftLeft;\n  \n  /**\n   * Returns this Long with bits arithmetically shifted to the right by the given amount.\n   * @this {!Long}\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Shifted Long\n   */\n  LongPrototype.shiftRight = function shiftRight(numBits) {\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;else if (numBits < 32) return fromBits(this.low >>> numBits | this.high << 32 - numBits, this.high >> numBits, this.unsigned);else return fromBits(this.high >> numBits - 32, this.high >= 0 ? 0 : -1, this.unsigned);\n  };\n  \n  /**\n   * Returns this Long with bits arithmetically shifted to the right by the given amount. This is an alias of {@link Long#shiftRight}.\n   * @function\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Shifted Long\n   */\n  LongPrototype.shr = LongPrototype.shiftRight;\n  \n  /**\n   * Returns this Long with bits logically shifted to the right by the given amount.\n   * @this {!Long}\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Shifted Long\n   */\n  LongPrototype.shiftRightUnsigned = function shiftRightUnsigned(numBits) {\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;\n    if (numBits < 32) return fromBits(this.low >>> numBits | this.high << 32 - numBits, this.high >>> numBits, this.unsigned);\n    if (numBits === 32) return fromBits(this.high, 0, this.unsigned);\n    return fromBits(this.high >>> numBits - 32, 0, this.unsigned);\n  };\n  \n  /**\n   * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n   * @function\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Shifted Long\n   */\n  LongPrototype.shru = LongPrototype.shiftRightUnsigned;\n  \n  /**\n   * Returns this Long with bits logically shifted to the right by the given amount. This is an alias of {@link Long#shiftRightUnsigned}.\n   * @function\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Shifted Long\n   */\n  LongPrototype.shr_u = LongPrototype.shiftRightUnsigned;\n  \n  /**\n   * Returns this Long with bits rotated to the left by the given amount.\n   * @this {!Long}\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Rotated Long\n   */\n  LongPrototype.rotateLeft = function rotateLeft(numBits) {\n    var b;\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;\n    if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n    if (numBits < 32) {\n      b = 32 - numBits;\n      return fromBits(this.low << numBits | this.high >>> b, this.high << numBits | this.low >>> b, this.unsigned);\n    }\n    numBits -= 32;\n    b = 32 - numBits;\n    return fromBits(this.high << numBits | this.low >>> b, this.low << numBits | this.high >>> b, this.unsigned);\n  };\n  /**\n   * Returns this Long with bits rotated to the left by the given amount. This is an alias of {@link Long#rotateLeft}.\n   * @function\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Rotated Long\n   */\n  LongPrototype.rotl = LongPrototype.rotateLeft;\n  \n  /**\n   * Returns this Long with bits rotated to the right by the given amount.\n   * @this {!Long}\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Rotated Long\n   */\n  LongPrototype.rotateRight = function rotateRight(numBits) {\n    var b;\n    if (isLong(numBits)) numBits = numBits.toInt();\n    if ((numBits &= 63) === 0) return this;\n    if (numBits === 32) return fromBits(this.high, this.low, this.unsigned);\n    if (numBits < 32) {\n      b = 32 - numBits;\n      return fromBits(this.high << b | this.low >>> numBits, this.low << b | this.high >>> numBits, this.unsigned);\n    }\n    numBits -= 32;\n    b = 32 - numBits;\n    return fromBits(this.low << b | this.high >>> numBits, this.high << b | this.low >>> numBits, this.unsigned);\n  };\n  /**\n   * Returns this Long with bits rotated to the right by the given amount. This is an alias of {@link Long#rotateRight}.\n   * @function\n   * @param {number|!Long} numBits Number of bits\n   * @returns {!Long} Rotated Long\n   */\n  LongPrototype.rotr = LongPrototype.rotateRight;\n  \n  /**\n   * Converts this Long to signed.\n   * @this {!Long}\n   * @returns {!Long} Signed long\n   */\n  LongPrototype.toSigned = function toSigned() {\n    if (!this.unsigned) return this;\n    return fromBits(this.low, this.high, false);\n  };\n  \n  /**\n   * Converts this Long to unsigned.\n   * @this {!Long}\n   * @returns {!Long} Unsigned long\n   */\n  LongPrototype.toUnsigned = function toUnsigned() {\n    if (this.unsigned) return this;\n    return fromBits(this.low, this.high, true);\n  };\n  \n  /**\n   * Converts this Long to its byte representation.\n   * @param {boolean=} le Whether little or big endian, defaults to big endian\n   * @this {!Long}\n   * @returns {!Array.<number>} Byte representation\n   */\n  LongPrototype.toBytes = function toBytes(le) {\n    return le ? this.toBytesLE() : this.toBytesBE();\n  };\n  \n  /**\n   * Converts this Long to its little endian byte representation.\n   * @this {!Long}\n   * @returns {!Array.<number>} Little endian byte representation\n   */\n  LongPrototype.toBytesLE = function toBytesLE() {\n    var hi = this.high,\n      lo = this.low;\n    return [lo & 0xff, lo >>> 8 & 0xff, lo >>> 16 & 0xff, lo >>> 24, hi & 0xff, hi >>> 8 & 0xff, hi >>> 16 & 0xff, hi >>> 24];\n  };\n  \n  /**\n   * Converts this Long to its big endian byte representation.\n   * @this {!Long}\n   * @returns {!Array.<number>} Big endian byte representation\n   */\n  LongPrototype.toBytesBE = function toBytesBE() {\n    var hi = this.high,\n      lo = this.low;\n    return [hi >>> 24, hi >>> 16 & 0xff, hi >>> 8 & 0xff, hi & 0xff, lo >>> 24, lo >>> 16 & 0xff, lo >>> 8 & 0xff, lo & 0xff];\n  };\n  \n  /**\n   * Creates a Long from its byte representation.\n   * @param {!Array.<number>} bytes Byte representation\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @param {boolean=} le Whether little or big endian, defaults to big endian\n   * @returns {Long} The corresponding Long value\n   */\n  Long.fromBytes = function fromBytes(bytes, unsigned, le) {\n    return le ? Long.fromBytesLE(bytes, unsigned) : Long.fromBytesBE(bytes, unsigned);\n  };\n  \n  /**\n   * Creates a Long from its little endian byte representation.\n   * @param {!Array.<number>} bytes Little endian byte representation\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @returns {Long} The corresponding Long value\n   */\n  Long.fromBytesLE = function fromBytesLE(bytes, unsigned) {\n    return new Long(bytes[0] | bytes[1] << 8 | bytes[2] << 16 | bytes[3] << 24, bytes[4] | bytes[5] << 8 | bytes[6] << 16 | bytes[7] << 24, unsigned);\n  };\n  \n  /**\n   * Creates a Long from its big endian byte representation.\n   * @param {!Array.<number>} bytes Big endian byte representation\n   * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n   * @returns {Long} The corresponding Long value\n   */\n  Long.fromBytesBE = function fromBytesBE(bytes, unsigned) {\n    return new Long(bytes[4] << 24 | bytes[5] << 16 | bytes[6] << 8 | bytes[7], bytes[0] << 24 | bytes[1] << 16 | bytes[2] << 8 | bytes[3], unsigned);\n  };\n  \n  // Support conversion to/from BigInt where available\n  if (typeof BigInt === \"function\") {\n    /**\n     * Returns a Long representing the given big integer.\n     * @function\n     * @param {number} value The big integer value\n     * @param {boolean=} unsigned Whether unsigned or not, defaults to signed\n     * @returns {!Long} The corresponding Long value\n     */\n    Long.fromBigInt = function fromBigInt(value, unsigned) {\n      var lowBits = Number(BigInt.asIntN(32, value));\n      var highBits = Number(BigInt.asIntN(32, value >> BigInt(32)));\n      return fromBits(lowBits, highBits, unsigned);\n    };\n  \n    // Override\n    Long.fromValue = function fromValueWithBigInt(value, unsigned) {\n      if (typeof value === \"bigint\") return fromBigInt(value, unsigned);\n      return fromValue(value, unsigned);\n    };\n  \n    /**\n     * Converts the Long to its big integer representation.\n     * @this {!Long}\n     * @returns {bigint}\n     */\n    LongPrototype.toBigInt = function toBigInt() {\n      var lowBigInt = BigInt(this.low >>> 0);\n      var highBigInt = BigInt(this.unsigned ? this.high >>> 0 : this.high);\n      return highBigInt << BigInt(32) | lowBigInt;\n    };\n  }\n  var _default = exports.default = Long;\n  return \"default\" in exports ? exports.default : exports;\n})({});\nif (true) !(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_RESULT__ = (function() { return Long; }).apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\nelse {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sb25nL3VtZC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlLQUFpSztBQUNqSyxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsUUFBUTtBQUNyQixhQUFhLFFBQVE7QUFDckIsYUFBYSxVQUFVO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBLGFBQWEsR0FBRztBQUNoQixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsR0FBRztBQUNoQixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLEdBQUc7QUFDaEIsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLFFBQVE7QUFDckIsYUFBYSxVQUFVO0FBQ3ZCLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsVUFBVTtBQUN2QixlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLFFBQVE7QUFDckIsYUFBYSxVQUFVO0FBQ3ZCLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsUUFBUTtBQUNyQixhQUFhLFVBQVU7QUFDdkIsZUFBZSxPQUFPO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsUUFBUTtBQUNyQixhQUFhLFVBQVU7QUFDdkIsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsUUFBUTtBQUNyQixhQUFhLFVBQVU7QUFDdkIsZUFBZSxPQUFPO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLFFBQVE7QUFDckIsYUFBYSxRQUFRO0FBQ3JCLGVBQWU7QUFDZjtBQUNBO0FBQ0EsMEJBQTBCO0FBQzFCO0FBQ0E7QUFDQSxhQUFhLFFBQVE7QUFDckIsYUFBYSxtQkFBbUI7QUFDaEMsYUFBYSxTQUFTO0FBQ3RCLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1FQUFtRTtBQUNuRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixnQkFBZ0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsbUJBQW1CO0FBQ2hDLGFBQWEsU0FBUztBQUN0QixlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsc0JBQXNCLCtDQUErQztBQUNsRixhQUFhLFVBQVU7QUFDdkIsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsNkJBQTZCLCtDQUErQztBQUN6RixhQUFhLFVBQVU7QUFDdkIsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1osZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1osYUFBYSxTQUFTO0FBQ3RCLGVBQWU7QUFDZjtBQUNBLGNBQWMsWUFBWTtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtDQUErQztBQUMvQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLGVBQWUsUUFBUTtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixlQUFlLFFBQVE7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1osZUFBZSxRQUFRO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLGVBQWUsUUFBUTtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLFNBQVM7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0VBQWtFLGtCQUFrQjtBQUNwRixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1osZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1osYUFBYSw0QkFBNEI7QUFDekMsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2RUFBNkUsa0JBQWtCO0FBQy9GO0FBQ0EsYUFBYSw0QkFBNEI7QUFDekMsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1osYUFBYSw0QkFBNEI7QUFDekMsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1GQUFtRixxQkFBcUI7QUFDeEc7QUFDQSxhQUFhLDRCQUE0QjtBQUN6QyxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtRkFBbUYscUJBQXFCO0FBQ3hHO0FBQ0EsYUFBYSw0QkFBNEI7QUFDekMsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1osYUFBYSw0QkFBNEI7QUFDekMsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1GQUFtRixvQkFBb0I7QUFDdkc7QUFDQSxhQUFhLDRCQUE0QjtBQUN6QyxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixhQUFhLDRCQUE0QjtBQUN6QyxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEZBQTRGLDJCQUEyQjtBQUN2SDtBQUNBLGFBQWEsNEJBQTRCO0FBQ3pDLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRGQUE0RiwyQkFBMkI7QUFDdkg7QUFDQSxhQUFhLDRCQUE0QjtBQUN6QyxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixhQUFhLDRCQUE0QjtBQUN6QyxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0ZBQXNGLHVCQUF1QjtBQUM3RztBQUNBLGFBQWEsNEJBQTRCO0FBQ3pDLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLGFBQWEsNEJBQTRCO0FBQ3pDLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrRkFBK0YsOEJBQThCO0FBQzdIO0FBQ0EsYUFBYSw0QkFBNEI7QUFDekMsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0ZBQStGLDhCQUE4QjtBQUM3SDtBQUNBLGFBQWEsNEJBQTRCO0FBQ3pDLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLGFBQWEsNEJBQTRCO0FBQ3pDLGVBQWUsUUFBUTtBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJFQUEyRSxtQkFBbUI7QUFDOUY7QUFDQSxhQUFhLDRCQUE0QjtBQUN6QyxlQUFlLFFBQVE7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLGVBQWUsT0FBTztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFEQUFxRCxrQkFBa0I7QUFDdkU7QUFDQSxlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixhQUFhLDRCQUE0QjtBQUN6QyxlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1osYUFBYSw0QkFBNEI7QUFDekMsZUFBZSxPQUFPO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUZBQWlGLG9CQUFvQjtBQUNyRztBQUNBLGFBQWEsNEJBQTRCO0FBQ3pDLGVBQWUsT0FBTztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLGFBQWEsNEJBQTRCO0FBQ3pDLGVBQWUsT0FBTztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkVBQTJFO0FBQzNFLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDhFQUE4RSxvQkFBb0I7QUFDbEc7QUFDQSxhQUFhLDRCQUE0QjtBQUN6QyxlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLGFBQWEsNEJBQTRCO0FBQ3pDLGVBQWUsT0FBTztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0VBQXNFO0FBQ3RFLG1EQUFtRDtBQUNuRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIsbUJBQW1CO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNFQUFzRSxrQkFBa0I7QUFDeEY7QUFDQSxhQUFhLDRCQUE0QjtBQUN6QyxlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixhQUFhLDRCQUE0QjtBQUN6QyxlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrRUFBa0Usa0JBQWtCO0FBQ3BGO0FBQ0EsYUFBYSw0QkFBNEI7QUFDekMsZUFBZSxPQUFPO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0VBQWtFLGtCQUFrQjtBQUNwRjtBQUNBLGFBQWEsNEJBQTRCO0FBQ3pDLGVBQWUsT0FBTztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdURBQXVELDZCQUE2QjtBQUNwRjtBQUNBLGFBQWE7QUFDYixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0RBQXdELDhCQUE4QjtBQUN0RjtBQUNBLGFBQWE7QUFDYixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixhQUFhLDRCQUE0QjtBQUN6QyxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixhQUFhLDRCQUE0QjtBQUN6QyxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixhQUFhLDRCQUE0QjtBQUN6QyxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixhQUFhLGNBQWM7QUFDM0IsZUFBZSxPQUFPO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyw2SEFBNkg7QUFDeEs7QUFDQTtBQUNBO0FBQ0EsK0ZBQStGLHFCQUFxQjtBQUNwSDtBQUNBLGFBQWEsY0FBYztBQUMzQixlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixhQUFhLGNBQWM7QUFDM0IsZUFBZSxPQUFPO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBLDJDQUEyQyw4SEFBOEg7QUFDeks7QUFDQTtBQUNBO0FBQ0EsK0dBQStHLHNCQUFzQjtBQUNySTtBQUNBLGFBQWEsY0FBYztBQUMzQixlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixhQUFhLGNBQWM7QUFDM0IsZUFBZSxPQUFPO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEdBQTBHLDhCQUE4QjtBQUN4STtBQUNBLGFBQWEsY0FBYztBQUMzQixlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwR0FBMEcsOEJBQThCO0FBQ3hJO0FBQ0EsYUFBYSxjQUFjO0FBQzNCLGVBQWUsT0FBTztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLGFBQWEsY0FBYztBQUMzQixlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0ZBQStGLHNCQUFzQjtBQUNySDtBQUNBLGFBQWEsY0FBYztBQUMzQixlQUFlLE9BQU87QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixhQUFhLGNBQWM7QUFDM0IsZUFBZSxPQUFPO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdHQUFnRyx1QkFBdUI7QUFDdkg7QUFDQSxhQUFhLGNBQWM7QUFDM0IsZUFBZSxPQUFPO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1osZUFBZSxPQUFPO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZO0FBQ1osZUFBZSxPQUFPO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLFVBQVU7QUFDdkIsWUFBWTtBQUNaLGVBQWUsaUJBQWlCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsWUFBWTtBQUNaLGVBQWUsaUJBQWlCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWixlQUFlLGlCQUFpQjtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGlCQUFpQjtBQUM5QixhQUFhLFVBQVU7QUFDdkIsYUFBYSxVQUFVO0FBQ3ZCLGVBQWUsTUFBTTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsaUJBQWlCO0FBQzlCLGFBQWEsVUFBVTtBQUN2QixlQUFlLE1BQU07QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLGlCQUFpQjtBQUM5QixhQUFhLFVBQVU7QUFDdkIsZUFBZSxNQUFNO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCLGVBQWUsVUFBVTtBQUN6QixpQkFBaUIsT0FBTztBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2QsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsSUFBSTtBQUNMLElBQUksSUFBMEMsRUFBRSxpQ0FBTyxFQUFFLG1DQUFFLGFBQWEsY0FBYztBQUFBLGtHQUFDO0FBQ3ZGLEtBQUssRUFBcUYiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGxvbmdcXHVtZFxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gR0VORVJBVEVEIEZJTEUuIERPIE5PVCBFRElULlxudmFyIExvbmcgPSAoZnVuY3Rpb24oZXhwb3J0cykge1xuICBcInVzZSBzdHJpY3RcIjtcbiAgXG4gIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG4gIH0pO1xuICBleHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7XG4gIC8qKlxuICAgKiBAbGljZW5zZVxuICAgKiBDb3B5cmlnaHQgMjAwOSBUaGUgQ2xvc3VyZSBMaWJyYXJ5IEF1dGhvcnNcbiAgICogQ29weXJpZ2h0IDIwMjAgRGFuaWVsIFdpcnR6IC8gVGhlIGxvbmcuanMgQXV0aG9ycy5cbiAgICpcbiAgICogTGljZW5zZWQgdW5kZXIgdGhlIEFwYWNoZSBMaWNlbnNlLCBWZXJzaW9uIDIuMCAodGhlIFwiTGljZW5zZVwiKTtcbiAgICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLlxuICAgKiBZb3UgbWF5IG9idGFpbiBhIGNvcHkgb2YgdGhlIExpY2Vuc2UgYXRcbiAgICpcbiAgICogICAgIGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICAgKlxuICAgKiBVbmxlc3MgcmVxdWlyZWQgYnkgYXBwbGljYWJsZSBsYXcgb3IgYWdyZWVkIHRvIGluIHdyaXRpbmcsIHNvZnR3YXJlXG4gICAqIGRpc3RyaWJ1dGVkIHVuZGVyIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUyxcbiAgICogV0lUSE9VVCBXQVJSQU5USUVTIE9SIENPTkRJVElPTlMgT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuXG4gICAqIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmRcbiAgICogbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gICAqXG4gICAqIFNQRFgtTGljZW5zZS1JZGVudGlmaWVyOiBBcGFjaGUtMi4wXG4gICAqL1xuICBcbiAgLy8gV2ViQXNzZW1ibHkgb3B0aW1pemF0aW9ucyB0byBkbyBuYXRpdmUgaTY0IG11bHRpcGxpY2F0aW9uIGFuZCBkaXZpZGVcbiAgdmFyIHdhc20gPSBudWxsO1xuICB0cnkge1xuICAgIHdhc20gPSBuZXcgV2ViQXNzZW1ibHkuSW5zdGFuY2UobmV3IFdlYkFzc2VtYmx5Lk1vZHVsZShuZXcgVWludDhBcnJheShbXG4gICAgLy8gXFwwYXNtXG4gICAgMCwgOTcsIDExNSwgMTA5LFxuICAgIC8vIHZlcnNpb24gMVxuICAgIDEsIDAsIDAsIDAsXG4gICAgLy8gc2VjdGlvbiBcInR5cGVcIlxuICAgIDEsIDEzLCAyLFxuICAgIC8vIDAsICgpID0+IGkzMlxuICAgIDk2LCAwLCAxLCAxMjcsXG4gICAgLy8gMSwgKGkzMiwgaTMyLCBpMzIsIGkzMikgPT4gaTMyXG4gICAgOTYsIDQsIDEyNywgMTI3LCAxMjcsIDEyNywgMSwgMTI3LFxuICAgIC8vIHNlY3Rpb24gXCJmdW5jdGlvblwiXG4gICAgMywgNywgNixcbiAgICAvLyAwLCB0eXBlIDBcbiAgICAwLFxuICAgIC8vIDEsIHR5cGUgMVxuICAgIDEsXG4gICAgLy8gMiwgdHlwZSAxXG4gICAgMSxcbiAgICAvLyAzLCB0eXBlIDFcbiAgICAxLFxuICAgIC8vIDQsIHR5cGUgMVxuICAgIDEsXG4gICAgLy8gNSwgdHlwZSAxXG4gICAgMSxcbiAgICAvLyBzZWN0aW9uIFwiZ2xvYmFsXCJcbiAgICA2LCA2LCAxLFxuICAgIC8vIDAsIFwiaGlnaFwiLCBtdXRhYmxlIGkzMlxuICAgIDEyNywgMSwgNjUsIDAsIDExLFxuICAgIC8vIHNlY3Rpb24gXCJleHBvcnRcIlxuICAgIDcsIDUwLCA2LFxuICAgIC8vIDAsIFwibXVsXCJcbiAgICAzLCAxMDksIDExNywgMTA4LCAwLCAxLFxuICAgIC8vIDEsIFwiZGl2X3NcIlxuICAgIDUsIDEwMCwgMTA1LCAxMTgsIDk1LCAxMTUsIDAsIDIsXG4gICAgLy8gMiwgXCJkaXZfdVwiXG4gICAgNSwgMTAwLCAxMDUsIDExOCwgOTUsIDExNywgMCwgMyxcbiAgICAvLyAzLCBcInJlbV9zXCJcbiAgICA1LCAxMTQsIDEwMSwgMTA5LCA5NSwgMTE1LCAwLCA0LFxuICAgIC8vIDQsIFwicmVtX3VcIlxuICAgIDUsIDExNCwgMTAxLCAxMDksIDk1LCAxMTcsIDAsIDUsXG4gICAgLy8gNSwgXCJnZXRfaGlnaFwiXG4gICAgOCwgMTAzLCAxMDEsIDExNiwgOTUsIDEwNCwgMTA1LCAxMDMsIDEwNCwgMCwgMCxcbiAgICAvLyBzZWN0aW9uIFwiY29kZVwiXG4gICAgMTAsIDE5MSwgMSwgNixcbiAgICAvLyAwLCBcImdldF9oaWdoXCJcbiAgICA0LCAwLCAzNSwgMCwgMTEsXG4gICAgLy8gMSwgXCJtdWxcIlxuICAgIDM2LCAxLCAxLCAxMjYsIDMyLCAwLCAxNzMsIDMyLCAxLCAxNzMsIDY2LCAzMiwgMTM0LCAxMzIsIDMyLCAyLCAxNzMsIDMyLCAzLCAxNzMsIDY2LCAzMiwgMTM0LCAxMzIsIDEyNiwgMzQsIDQsIDY2LCAzMiwgMTM1LCAxNjcsIDM2LCAwLCAzMiwgNCwgMTY3LCAxMSxcbiAgICAvLyAyLCBcImRpdl9zXCJcbiAgICAzNiwgMSwgMSwgMTI2LCAzMiwgMCwgMTczLCAzMiwgMSwgMTczLCA2NiwgMzIsIDEzNCwgMTMyLCAzMiwgMiwgMTczLCAzMiwgMywgMTczLCA2NiwgMzIsIDEzNCwgMTMyLCAxMjcsIDM0LCA0LCA2NiwgMzIsIDEzNSwgMTY3LCAzNiwgMCwgMzIsIDQsIDE2NywgMTEsXG4gICAgLy8gMywgXCJkaXZfdVwiXG4gICAgMzYsIDEsIDEsIDEyNiwgMzIsIDAsIDE3MywgMzIsIDEsIDE3MywgNjYsIDMyLCAxMzQsIDEzMiwgMzIsIDIsIDE3MywgMzIsIDMsIDE3MywgNjYsIDMyLCAxMzQsIDEzMiwgMTI4LCAzNCwgNCwgNjYsIDMyLCAxMzUsIDE2NywgMzYsIDAsIDMyLCA0LCAxNjcsIDExLFxuICAgIC8vIDQsIFwicmVtX3NcIlxuICAgIDM2LCAxLCAxLCAxMjYsIDMyLCAwLCAxNzMsIDMyLCAxLCAxNzMsIDY2LCAzMiwgMTM0LCAxMzIsIDMyLCAyLCAxNzMsIDMyLCAzLCAxNzMsIDY2LCAzMiwgMTM0LCAxMzIsIDEyOSwgMzQsIDQsIDY2LCAzMiwgMTM1LCAxNjcsIDM2LCAwLCAzMiwgNCwgMTY3LCAxMSxcbiAgICAvLyA1LCBcInJlbV91XCJcbiAgICAzNiwgMSwgMSwgMTI2LCAzMiwgMCwgMTczLCAzMiwgMSwgMTczLCA2NiwgMzIsIDEzNCwgMTMyLCAzMiwgMiwgMTczLCAzMiwgMywgMTczLCA2NiwgMzIsIDEzNCwgMTMyLCAxMzAsIDM0LCA0LCA2NiwgMzIsIDEzNSwgMTY3LCAzNiwgMCwgMzIsIDQsIDE2NywgMTFdKSksIHt9KS5leHBvcnRzO1xuICB9IGNhdGNoIHtcbiAgICAvLyBubyB3YXNtIHN1cHBvcnQgOihcbiAgfVxuICBcbiAgLyoqXG4gICAqIENvbnN0cnVjdHMgYSA2NCBiaXQgdHdvJ3MtY29tcGxlbWVudCBpbnRlZ2VyLCBnaXZlbiBpdHMgbG93IGFuZCBoaWdoIDMyIGJpdCB2YWx1ZXMgYXMgKnNpZ25lZCogaW50ZWdlcnMuXG4gICAqICBTZWUgdGhlIGZyb20qIGZ1bmN0aW9ucyBiZWxvdyBmb3IgbW9yZSBjb252ZW5pZW50IHdheXMgb2YgY29uc3RydWN0aW5nIExvbmdzLlxuICAgKiBAZXhwb3J0cyBMb25nXG4gICAqIEBjbGFzcyBBIExvbmcgY2xhc3MgZm9yIHJlcHJlc2VudGluZyBhIDY0IGJpdCB0d28ncy1jb21wbGVtZW50IGludGVnZXIgdmFsdWUuXG4gICAqIEBwYXJhbSB7bnVtYmVyfSBsb3cgVGhlIGxvdyAoc2lnbmVkKSAzMiBiaXRzIG9mIHRoZSBsb25nXG4gICAqIEBwYXJhbSB7bnVtYmVyfSBoaWdoIFRoZSBoaWdoIChzaWduZWQpIDMyIGJpdHMgb2YgdGhlIGxvbmdcbiAgICogQHBhcmFtIHtib29sZWFuPX0gdW5zaWduZWQgV2hldGhlciB1bnNpZ25lZCBvciBub3QsIGRlZmF1bHRzIHRvIHNpZ25lZFxuICAgKiBAY29uc3RydWN0b3JcbiAgICovXG4gIGZ1bmN0aW9uIExvbmcobG93LCBoaWdoLCB1bnNpZ25lZCkge1xuICAgIC8qKlxuICAgICAqIFRoZSBsb3cgMzIgYml0cyBhcyBhIHNpZ25lZCB2YWx1ZS5cbiAgICAgKiBAdHlwZSB7bnVtYmVyfVxuICAgICAqL1xuICAgIHRoaXMubG93ID0gbG93IHwgMDtcbiAgXG4gICAgLyoqXG4gICAgICogVGhlIGhpZ2ggMzIgYml0cyBhcyBhIHNpZ25lZCB2YWx1ZS5cbiAgICAgKiBAdHlwZSB7bnVtYmVyfVxuICAgICAqL1xuICAgIHRoaXMuaGlnaCA9IGhpZ2ggfCAwO1xuICBcbiAgICAvKipcbiAgICAgKiBXaGV0aGVyIHVuc2lnbmVkIG9yIG5vdC5cbiAgICAgKiBAdHlwZSB7Ym9vbGVhbn1cbiAgICAgKi9cbiAgICB0aGlzLnVuc2lnbmVkID0gISF1bnNpZ25lZDtcbiAgfVxuICBcbiAgLy8gVGhlIGludGVybmFsIHJlcHJlc2VudGF0aW9uIG9mIGEgbG9uZyBpcyB0aGUgdHdvIGdpdmVuIHNpZ25lZCwgMzItYml0IHZhbHVlcy5cbiAgLy8gV2UgdXNlIDMyLWJpdCBwaWVjZXMgYmVjYXVzZSB0aGVzZSBhcmUgdGhlIHNpemUgb2YgaW50ZWdlcnMgb24gd2hpY2hcbiAgLy8gSmF2YXNjcmlwdCBwZXJmb3JtcyBiaXQtb3BlcmF0aW9ucy4gIEZvciBvcGVyYXRpb25zIGxpa2UgYWRkaXRpb24gYW5kXG4gIC8vIG11bHRpcGxpY2F0aW9uLCB3ZSBzcGxpdCBlYWNoIG51bWJlciBpbnRvIDE2IGJpdCBwaWVjZXMsIHdoaWNoIGNhbiBlYXNpbHkgYmVcbiAgLy8gbXVsdGlwbGllZCB3aXRoaW4gSmF2YXNjcmlwdCdzIGZsb2F0aW5nLXBvaW50IHJlcHJlc2VudGF0aW9uIHdpdGhvdXQgb3ZlcmZsb3dcbiAgLy8gb3IgY2hhbmdlIGluIHNpZ24uXG4gIC8vXG4gIC8vIEluIHRoZSBhbGdvcml0aG1zIGJlbG93LCB3ZSBmcmVxdWVudGx5IHJlZHVjZSB0aGUgbmVnYXRpdmUgY2FzZSB0byB0aGVcbiAgLy8gcG9zaXRpdmUgY2FzZSBieSBuZWdhdGluZyB0aGUgaW5wdXQocykgYW5kIHRoZW4gcG9zdC1wcm9jZXNzaW5nIHRoZSByZXN1bHQuXG4gIC8vIE5vdGUgdGhhdCB3ZSBtdXN0IEFMV0FZUyBjaGVjayBzcGVjaWFsbHkgd2hldGhlciB0aG9zZSB2YWx1ZXMgYXJlIE1JTl9WQUxVRVxuICAvLyAoLTJeNjMpIGJlY2F1c2UgLU1JTl9WQUxVRSA9PSBNSU5fVkFMVUUgKHNpbmNlIDJeNjMgY2Fubm90IGJlIHJlcHJlc2VudGVkIGFzXG4gIC8vIGEgcG9zaXRpdmUgbnVtYmVyLCBpdCBvdmVyZmxvd3MgYmFjayBpbnRvIGEgbmVnYXRpdmUpLiAgTm90IGhhbmRsaW5nIHRoaXNcbiAgLy8gY2FzZSB3b3VsZCBvZnRlbiByZXN1bHQgaW4gaW5maW5pdGUgcmVjdXJzaW9uLlxuICAvL1xuICAvLyBDb21tb24gY29uc3RhbnQgdmFsdWVzIFpFUk8sIE9ORSwgTkVHX09ORSwgZXRjLiBhcmUgZGVmaW5lZCBiZWxvdyB0aGUgZnJvbSpcbiAgLy8gbWV0aG9kcyBvbiB3aGljaCB0aGV5IGRlcGVuZC5cbiAgXG4gIC8qKlxuICAgKiBBbiBpbmRpY2F0b3IgdXNlZCB0byByZWxpYWJseSBkZXRlcm1pbmUgaWYgYW4gb2JqZWN0IGlzIGEgTG9uZyBvciBub3QuXG4gICAqIEB0eXBlIHtib29sZWFufVxuICAgKiBAY29uc3RcbiAgICogQHByaXZhdGVcbiAgICovXG4gIExvbmcucHJvdG90eXBlLl9faXNMb25nX187XG4gIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShMb25nLnByb3RvdHlwZSwgXCJfX2lzTG9uZ19fXCIsIHtcbiAgICB2YWx1ZTogdHJ1ZVxuICB9KTtcbiAgXG4gIC8qKlxuICAgKiBAZnVuY3Rpb25cbiAgICogQHBhcmFtIHsqfSBvYmogT2JqZWN0XG4gICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgKiBAaW5uZXJcbiAgICovXG4gIGZ1bmN0aW9uIGlzTG9uZyhvYmopIHtcbiAgICByZXR1cm4gKG9iaiAmJiBvYmpbXCJfX2lzTG9uZ19fXCJdKSA9PT0gdHJ1ZTtcbiAgfVxuICBcbiAgLyoqXG4gICAqIEBmdW5jdGlvblxuICAgKiBAcGFyYW0geyp9IHZhbHVlIG51bWJlclxuICAgKiBAcmV0dXJucyB7bnVtYmVyfVxuICAgKiBAaW5uZXJcbiAgICovXG4gIGZ1bmN0aW9uIGN0ejMyKHZhbHVlKSB7XG4gICAgdmFyIGMgPSBNYXRoLmNsejMyKHZhbHVlICYgLXZhbHVlKTtcbiAgICByZXR1cm4gdmFsdWUgPyAzMSAtIGMgOiBjO1xuICB9XG4gIFxuICAvKipcbiAgICogVGVzdHMgaWYgdGhlIHNwZWNpZmllZCBvYmplY3QgaXMgYSBMb25nLlxuICAgKiBAZnVuY3Rpb25cbiAgICogQHBhcmFtIHsqfSBvYmogT2JqZWN0XG4gICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgKi9cbiAgTG9uZy5pc0xvbmcgPSBpc0xvbmc7XG4gIFxuICAvKipcbiAgICogQSBjYWNoZSBvZiB0aGUgTG9uZyByZXByZXNlbnRhdGlvbnMgb2Ygc21hbGwgaW50ZWdlciB2YWx1ZXMuXG4gICAqIEB0eXBlIHshT2JqZWN0fVxuICAgKiBAaW5uZXJcbiAgICovXG4gIHZhciBJTlRfQ0FDSEUgPSB7fTtcbiAgXG4gIC8qKlxuICAgKiBBIGNhY2hlIG9mIHRoZSBMb25nIHJlcHJlc2VudGF0aW9ucyBvZiBzbWFsbCB1bnNpZ25lZCBpbnRlZ2VyIHZhbHVlcy5cbiAgICogQHR5cGUgeyFPYmplY3R9XG4gICAqIEBpbm5lclxuICAgKi9cbiAgdmFyIFVJTlRfQ0FDSEUgPSB7fTtcbiAgXG4gIC8qKlxuICAgKiBAcGFyYW0ge251bWJlcn0gdmFsdWVcbiAgICogQHBhcmFtIHtib29sZWFuPX0gdW5zaWduZWRcbiAgICogQHJldHVybnMgeyFMb25nfVxuICAgKiBAaW5uZXJcbiAgICovXG4gIGZ1bmN0aW9uIGZyb21JbnQodmFsdWUsIHVuc2lnbmVkKSB7XG4gICAgdmFyIG9iaiwgY2FjaGVkT2JqLCBjYWNoZTtcbiAgICBpZiAodW5zaWduZWQpIHtcbiAgICAgIHZhbHVlID4+Pj0gMDtcbiAgICAgIGlmIChjYWNoZSA9IDAgPD0gdmFsdWUgJiYgdmFsdWUgPCAyNTYpIHtcbiAgICAgICAgY2FjaGVkT2JqID0gVUlOVF9DQUNIRVt2YWx1ZV07XG4gICAgICAgIGlmIChjYWNoZWRPYmopIHJldHVybiBjYWNoZWRPYmo7XG4gICAgICB9XG4gICAgICBvYmogPSBmcm9tQml0cyh2YWx1ZSwgMCwgdHJ1ZSk7XG4gICAgICBpZiAoY2FjaGUpIFVJTlRfQ0FDSEVbdmFsdWVdID0gb2JqO1xuICAgICAgcmV0dXJuIG9iajtcbiAgICB9IGVsc2Uge1xuICAgICAgdmFsdWUgfD0gMDtcbiAgICAgIGlmIChjYWNoZSA9IC0xMjggPD0gdmFsdWUgJiYgdmFsdWUgPCAxMjgpIHtcbiAgICAgICAgY2FjaGVkT2JqID0gSU5UX0NBQ0hFW3ZhbHVlXTtcbiAgICAgICAgaWYgKGNhY2hlZE9iaikgcmV0dXJuIGNhY2hlZE9iajtcbiAgICAgIH1cbiAgICAgIG9iaiA9IGZyb21CaXRzKHZhbHVlLCB2YWx1ZSA8IDAgPyAtMSA6IDAsIGZhbHNlKTtcbiAgICAgIGlmIChjYWNoZSkgSU5UX0NBQ0hFW3ZhbHVlXSA9IG9iajtcbiAgICAgIHJldHVybiBvYmo7XG4gICAgfVxuICB9XG4gIFxuICAvKipcbiAgICogUmV0dXJucyBhIExvbmcgcmVwcmVzZW50aW5nIHRoZSBnaXZlbiAzMiBiaXQgaW50ZWdlciB2YWx1ZS5cbiAgICogQGZ1bmN0aW9uXG4gICAqIEBwYXJhbSB7bnVtYmVyfSB2YWx1ZSBUaGUgMzIgYml0IGludGVnZXIgaW4gcXVlc3Rpb25cbiAgICogQHBhcmFtIHtib29sZWFuPX0gdW5zaWduZWQgV2hldGhlciB1bnNpZ25lZCBvciBub3QsIGRlZmF1bHRzIHRvIHNpZ25lZFxuICAgKiBAcmV0dXJucyB7IUxvbmd9IFRoZSBjb3JyZXNwb25kaW5nIExvbmcgdmFsdWVcbiAgICovXG4gIExvbmcuZnJvbUludCA9IGZyb21JbnQ7XG4gIFxuICAvKipcbiAgICogQHBhcmFtIHtudW1iZXJ9IHZhbHVlXG4gICAqIEBwYXJhbSB7Ym9vbGVhbj19IHVuc2lnbmVkXG4gICAqIEByZXR1cm5zIHshTG9uZ31cbiAgICogQGlubmVyXG4gICAqL1xuICBmdW5jdGlvbiBmcm9tTnVtYmVyKHZhbHVlLCB1bnNpZ25lZCkge1xuICAgIGlmIChpc05hTih2YWx1ZSkpIHJldHVybiB1bnNpZ25lZCA/IFVaRVJPIDogWkVSTztcbiAgICBpZiAodW5zaWduZWQpIHtcbiAgICAgIGlmICh2YWx1ZSA8IDApIHJldHVybiBVWkVSTztcbiAgICAgIGlmICh2YWx1ZSA+PSBUV09fUFdSXzY0X0RCTCkgcmV0dXJuIE1BWF9VTlNJR05FRF9WQUxVRTtcbiAgICB9IGVsc2Uge1xuICAgICAgaWYgKHZhbHVlIDw9IC1UV09fUFdSXzYzX0RCTCkgcmV0dXJuIE1JTl9WQUxVRTtcbiAgICAgIGlmICh2YWx1ZSArIDEgPj0gVFdPX1BXUl82M19EQkwpIHJldHVybiBNQVhfVkFMVUU7XG4gICAgfVxuICAgIGlmICh2YWx1ZSA8IDApIHJldHVybiBmcm9tTnVtYmVyKC12YWx1ZSwgdW5zaWduZWQpLm5lZygpO1xuICAgIHJldHVybiBmcm9tQml0cyh2YWx1ZSAlIFRXT19QV1JfMzJfREJMIHwgMCwgdmFsdWUgLyBUV09fUFdSXzMyX0RCTCB8IDAsIHVuc2lnbmVkKTtcbiAgfVxuICBcbiAgLyoqXG4gICAqIFJldHVybnMgYSBMb25nIHJlcHJlc2VudGluZyB0aGUgZ2l2ZW4gdmFsdWUsIHByb3ZpZGVkIHRoYXQgaXQgaXMgYSBmaW5pdGUgbnVtYmVyLiBPdGhlcndpc2UsIHplcm8gaXMgcmV0dXJuZWQuXG4gICAqIEBmdW5jdGlvblxuICAgKiBAcGFyYW0ge251bWJlcn0gdmFsdWUgVGhlIG51bWJlciBpbiBxdWVzdGlvblxuICAgKiBAcGFyYW0ge2Jvb2xlYW49fSB1bnNpZ25lZCBXaGV0aGVyIHVuc2lnbmVkIG9yIG5vdCwgZGVmYXVsdHMgdG8gc2lnbmVkXG4gICAqIEByZXR1cm5zIHshTG9uZ30gVGhlIGNvcnJlc3BvbmRpbmcgTG9uZyB2YWx1ZVxuICAgKi9cbiAgTG9uZy5mcm9tTnVtYmVyID0gZnJvbU51bWJlcjtcbiAgXG4gIC8qKlxuICAgKiBAcGFyYW0ge251bWJlcn0gbG93Qml0c1xuICAgKiBAcGFyYW0ge251bWJlcn0gaGlnaEJpdHNcbiAgICogQHBhcmFtIHtib29sZWFuPX0gdW5zaWduZWRcbiAgICogQHJldHVybnMgeyFMb25nfVxuICAgKiBAaW5uZXJcbiAgICovXG4gIGZ1bmN0aW9uIGZyb21CaXRzKGxvd0JpdHMsIGhpZ2hCaXRzLCB1bnNpZ25lZCkge1xuICAgIHJldHVybiBuZXcgTG9uZyhsb3dCaXRzLCBoaWdoQml0cywgdW5zaWduZWQpO1xuICB9XG4gIFxuICAvKipcbiAgICogUmV0dXJucyBhIExvbmcgcmVwcmVzZW50aW5nIHRoZSA2NCBiaXQgaW50ZWdlciB0aGF0IGNvbWVzIGJ5IGNvbmNhdGVuYXRpbmcgdGhlIGdpdmVuIGxvdyBhbmQgaGlnaCBiaXRzLiBFYWNoIGlzXG4gICAqICBhc3N1bWVkIHRvIHVzZSAzMiBiaXRzLlxuICAgKiBAZnVuY3Rpb25cbiAgICogQHBhcmFtIHtudW1iZXJ9IGxvd0JpdHMgVGhlIGxvdyAzMiBiaXRzXG4gICAqIEBwYXJhbSB7bnVtYmVyfSBoaWdoQml0cyBUaGUgaGlnaCAzMiBiaXRzXG4gICAqIEBwYXJhbSB7Ym9vbGVhbj19IHVuc2lnbmVkIFdoZXRoZXIgdW5zaWduZWQgb3Igbm90LCBkZWZhdWx0cyB0byBzaWduZWRcbiAgICogQHJldHVybnMgeyFMb25nfSBUaGUgY29ycmVzcG9uZGluZyBMb25nIHZhbHVlXG4gICAqL1xuICBMb25nLmZyb21CaXRzID0gZnJvbUJpdHM7XG4gIFxuICAvKipcbiAgICogQGZ1bmN0aW9uXG4gICAqIEBwYXJhbSB7bnVtYmVyfSBiYXNlXG4gICAqIEBwYXJhbSB7bnVtYmVyfSBleHBvbmVudFxuICAgKiBAcmV0dXJucyB7bnVtYmVyfVxuICAgKiBAaW5uZXJcbiAgICovXG4gIHZhciBwb3dfZGJsID0gTWF0aC5wb3c7IC8vIFVzZWQgNCB0aW1lcyAoNCo4IHRvIDE1KzQpXG4gIFxuICAvKipcbiAgICogQHBhcmFtIHtzdHJpbmd9IHN0clxuICAgKiBAcGFyYW0geyhib29sZWFufG51bWJlcik9fSB1bnNpZ25lZFxuICAgKiBAcGFyYW0ge251bWJlcj19IHJhZGl4XG4gICAqIEByZXR1cm5zIHshTG9uZ31cbiAgICogQGlubmVyXG4gICAqL1xuICBmdW5jdGlvbiBmcm9tU3RyaW5nKHN0ciwgdW5zaWduZWQsIHJhZGl4KSB7XG4gICAgaWYgKHN0ci5sZW5ndGggPT09IDApIHRocm93IEVycm9yKCdlbXB0eSBzdHJpbmcnKTtcbiAgICBpZiAodHlwZW9mIHVuc2lnbmVkID09PSAnbnVtYmVyJykge1xuICAgICAgLy8gRm9yIGdvb2cubWF0aC5sb25nIGNvbXBhdGliaWxpdHlcbiAgICAgIHJhZGl4ID0gdW5zaWduZWQ7XG4gICAgICB1bnNpZ25lZCA9IGZhbHNlO1xuICAgIH0gZWxzZSB7XG4gICAgICB1bnNpZ25lZCA9ICEhdW5zaWduZWQ7XG4gICAgfVxuICAgIGlmIChzdHIgPT09IFwiTmFOXCIgfHwgc3RyID09PSBcIkluZmluaXR5XCIgfHwgc3RyID09PSBcIitJbmZpbml0eVwiIHx8IHN0ciA9PT0gXCItSW5maW5pdHlcIikgcmV0dXJuIHVuc2lnbmVkID8gVVpFUk8gOiBaRVJPO1xuICAgIHJhZGl4ID0gcmFkaXggfHwgMTA7XG4gICAgaWYgKHJhZGl4IDwgMiB8fCAzNiA8IHJhZGl4KSB0aHJvdyBSYW5nZUVycm9yKCdyYWRpeCcpO1xuICAgIHZhciBwO1xuICAgIGlmICgocCA9IHN0ci5pbmRleE9mKCctJykpID4gMCkgdGhyb3cgRXJyb3IoJ2ludGVyaW9yIGh5cGhlbicpO2Vsc2UgaWYgKHAgPT09IDApIHtcbiAgICAgIHJldHVybiBmcm9tU3RyaW5nKHN0ci5zdWJzdHJpbmcoMSksIHVuc2lnbmVkLCByYWRpeCkubmVnKCk7XG4gICAgfVxuICBcbiAgICAvLyBEbyBzZXZlcmFsICg4KSBkaWdpdHMgZWFjaCB0aW1lIHRocm91Z2ggdGhlIGxvb3AsIHNvIGFzIHRvXG4gICAgLy8gbWluaW1pemUgdGhlIGNhbGxzIHRvIHRoZSB2ZXJ5IGV4cGVuc2l2ZSBlbXVsYXRlZCBkaXYuXG4gICAgdmFyIHJhZGl4VG9Qb3dlciA9IGZyb21OdW1iZXIocG93X2RibChyYWRpeCwgOCkpO1xuICAgIHZhciByZXN1bHQgPSBaRVJPO1xuICAgIGZvciAodmFyIGkgPSAwOyBpIDwgc3RyLmxlbmd0aDsgaSArPSA4KSB7XG4gICAgICB2YXIgc2l6ZSA9IE1hdGgubWluKDgsIHN0ci5sZW5ndGggLSBpKSxcbiAgICAgICAgdmFsdWUgPSBwYXJzZUludChzdHIuc3Vic3RyaW5nKGksIGkgKyBzaXplKSwgcmFkaXgpO1xuICAgICAgaWYgKHNpemUgPCA4KSB7XG4gICAgICAgIHZhciBwb3dlciA9IGZyb21OdW1iZXIocG93X2RibChyYWRpeCwgc2l6ZSkpO1xuICAgICAgICByZXN1bHQgPSByZXN1bHQubXVsKHBvd2VyKS5hZGQoZnJvbU51bWJlcih2YWx1ZSkpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcmVzdWx0ID0gcmVzdWx0Lm11bChyYWRpeFRvUG93ZXIpO1xuICAgICAgICByZXN1bHQgPSByZXN1bHQuYWRkKGZyb21OdW1iZXIodmFsdWUpKTtcbiAgICAgIH1cbiAgICB9XG4gICAgcmVzdWx0LnVuc2lnbmVkID0gdW5zaWduZWQ7XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfVxuICBcbiAgLyoqXG4gICAqIFJldHVybnMgYSBMb25nIHJlcHJlc2VudGF0aW9uIG9mIHRoZSBnaXZlbiBzdHJpbmcsIHdyaXR0ZW4gdXNpbmcgdGhlIHNwZWNpZmllZCByYWRpeC5cbiAgICogQGZ1bmN0aW9uXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBzdHIgVGhlIHRleHR1YWwgcmVwcmVzZW50YXRpb24gb2YgdGhlIExvbmdcbiAgICogQHBhcmFtIHsoYm9vbGVhbnxudW1iZXIpPX0gdW5zaWduZWQgV2hldGhlciB1bnNpZ25lZCBvciBub3QsIGRlZmF1bHRzIHRvIHNpZ25lZFxuICAgKiBAcGFyYW0ge251bWJlcj19IHJhZGl4IFRoZSByYWRpeCBpbiB3aGljaCB0aGUgdGV4dCBpcyB3cml0dGVuICgyLTM2KSwgZGVmYXVsdHMgdG8gMTBcbiAgICogQHJldHVybnMgeyFMb25nfSBUaGUgY29ycmVzcG9uZGluZyBMb25nIHZhbHVlXG4gICAqL1xuICBMb25nLmZyb21TdHJpbmcgPSBmcm9tU3RyaW5nO1xuICBcbiAgLyoqXG4gICAqIEBmdW5jdGlvblxuICAgKiBAcGFyYW0geyFMb25nfG51bWJlcnxzdHJpbmd8IXtsb3c6IG51bWJlciwgaGlnaDogbnVtYmVyLCB1bnNpZ25lZDogYm9vbGVhbn19IHZhbFxuICAgKiBAcGFyYW0ge2Jvb2xlYW49fSB1bnNpZ25lZFxuICAgKiBAcmV0dXJucyB7IUxvbmd9XG4gICAqIEBpbm5lclxuICAgKi9cbiAgZnVuY3Rpb24gZnJvbVZhbHVlKHZhbCwgdW5zaWduZWQpIHtcbiAgICBpZiAodHlwZW9mIHZhbCA9PT0gJ251bWJlcicpIHJldHVybiBmcm9tTnVtYmVyKHZhbCwgdW5zaWduZWQpO1xuICAgIGlmICh0eXBlb2YgdmFsID09PSAnc3RyaW5nJykgcmV0dXJuIGZyb21TdHJpbmcodmFsLCB1bnNpZ25lZCk7XG4gICAgLy8gVGhyb3dzIGZvciBub24tb2JqZWN0cywgY29udmVydHMgbm9uLWluc3RhbmNlb2YgTG9uZzpcbiAgICByZXR1cm4gZnJvbUJpdHModmFsLmxvdywgdmFsLmhpZ2gsIHR5cGVvZiB1bnNpZ25lZCA9PT0gJ2Jvb2xlYW4nID8gdW5zaWduZWQgOiB2YWwudW5zaWduZWQpO1xuICB9XG4gIFxuICAvKipcbiAgICogQ29udmVydHMgdGhlIHNwZWNpZmllZCB2YWx1ZSB0byBhIExvbmcgdXNpbmcgdGhlIGFwcHJvcHJpYXRlIGZyb20qIGZ1bmN0aW9uIGZvciBpdHMgdHlwZS5cbiAgICogQGZ1bmN0aW9uXG4gICAqIEBwYXJhbSB7IUxvbmd8bnVtYmVyfGJpZ2ludHxzdHJpbmd8IXtsb3c6IG51bWJlciwgaGlnaDogbnVtYmVyLCB1bnNpZ25lZDogYm9vbGVhbn19IHZhbCBWYWx1ZVxuICAgKiBAcGFyYW0ge2Jvb2xlYW49fSB1bnNpZ25lZCBXaGV0aGVyIHVuc2lnbmVkIG9yIG5vdCwgZGVmYXVsdHMgdG8gc2lnbmVkXG4gICAqIEByZXR1cm5zIHshTG9uZ31cbiAgICovXG4gIExvbmcuZnJvbVZhbHVlID0gZnJvbVZhbHVlO1xuICBcbiAgLy8gTk9URTogdGhlIGNvbXBpbGVyIHNob3VsZCBpbmxpbmUgdGhlc2UgY29uc3RhbnQgdmFsdWVzIGJlbG93IGFuZCB0aGVuIHJlbW92ZSB0aGVzZSB2YXJpYWJsZXMsIHNvIHRoZXJlIHNob3VsZCBiZVxuICAvLyBubyBydW50aW1lIHBlbmFsdHkgZm9yIHRoZXNlLlxuICBcbiAgLyoqXG4gICAqIEB0eXBlIHtudW1iZXJ9XG4gICAqIEBjb25zdFxuICAgKiBAaW5uZXJcbiAgICovXG4gIHZhciBUV09fUFdSXzE2X0RCTCA9IDEgPDwgMTY7XG4gIFxuICAvKipcbiAgICogQHR5cGUge251bWJlcn1cbiAgICogQGNvbnN0XG4gICAqIEBpbm5lclxuICAgKi9cbiAgdmFyIFRXT19QV1JfMjRfREJMID0gMSA8PCAyNDtcbiAgXG4gIC8qKlxuICAgKiBAdHlwZSB7bnVtYmVyfVxuICAgKiBAY29uc3RcbiAgICogQGlubmVyXG4gICAqL1xuICB2YXIgVFdPX1BXUl8zMl9EQkwgPSBUV09fUFdSXzE2X0RCTCAqIFRXT19QV1JfMTZfREJMO1xuICBcbiAgLyoqXG4gICAqIEB0eXBlIHtudW1iZXJ9XG4gICAqIEBjb25zdFxuICAgKiBAaW5uZXJcbiAgICovXG4gIHZhciBUV09fUFdSXzY0X0RCTCA9IFRXT19QV1JfMzJfREJMICogVFdPX1BXUl8zMl9EQkw7XG4gIFxuICAvKipcbiAgICogQHR5cGUge251bWJlcn1cbiAgICogQGNvbnN0XG4gICAqIEBpbm5lclxuICAgKi9cbiAgdmFyIFRXT19QV1JfNjNfREJMID0gVFdPX1BXUl82NF9EQkwgLyAyO1xuICBcbiAgLyoqXG4gICAqIEB0eXBlIHshTG9uZ31cbiAgICogQGNvbnN0XG4gICAqIEBpbm5lclxuICAgKi9cbiAgdmFyIFRXT19QV1JfMjQgPSBmcm9tSW50KFRXT19QV1JfMjRfREJMKTtcbiAgXG4gIC8qKlxuICAgKiBAdHlwZSB7IUxvbmd9XG4gICAqIEBpbm5lclxuICAgKi9cbiAgdmFyIFpFUk8gPSBmcm9tSW50KDApO1xuICBcbiAgLyoqXG4gICAqIFNpZ25lZCB6ZXJvLlxuICAgKiBAdHlwZSB7IUxvbmd9XG4gICAqL1xuICBMb25nLlpFUk8gPSBaRVJPO1xuICBcbiAgLyoqXG4gICAqIEB0eXBlIHshTG9uZ31cbiAgICogQGlubmVyXG4gICAqL1xuICB2YXIgVVpFUk8gPSBmcm9tSW50KDAsIHRydWUpO1xuICBcbiAgLyoqXG4gICAqIFVuc2lnbmVkIHplcm8uXG4gICAqIEB0eXBlIHshTG9uZ31cbiAgICovXG4gIExvbmcuVVpFUk8gPSBVWkVSTztcbiAgXG4gIC8qKlxuICAgKiBAdHlwZSB7IUxvbmd9XG4gICAqIEBpbm5lclxuICAgKi9cbiAgdmFyIE9ORSA9IGZyb21JbnQoMSk7XG4gIFxuICAvKipcbiAgICogU2lnbmVkIG9uZS5cbiAgICogQHR5cGUgeyFMb25nfVxuICAgKi9cbiAgTG9uZy5PTkUgPSBPTkU7XG4gIFxuICAvKipcbiAgICogQHR5cGUgeyFMb25nfVxuICAgKiBAaW5uZXJcbiAgICovXG4gIHZhciBVT05FID0gZnJvbUludCgxLCB0cnVlKTtcbiAgXG4gIC8qKlxuICAgKiBVbnNpZ25lZCBvbmUuXG4gICAqIEB0eXBlIHshTG9uZ31cbiAgICovXG4gIExvbmcuVU9ORSA9IFVPTkU7XG4gIFxuICAvKipcbiAgICogQHR5cGUgeyFMb25nfVxuICAgKiBAaW5uZXJcbiAgICovXG4gIHZhciBORUdfT05FID0gZnJvbUludCgtMSk7XG4gIFxuICAvKipcbiAgICogU2lnbmVkIG5lZ2F0aXZlIG9uZS5cbiAgICogQHR5cGUgeyFMb25nfVxuICAgKi9cbiAgTG9uZy5ORUdfT05FID0gTkVHX09ORTtcbiAgXG4gIC8qKlxuICAgKiBAdHlwZSB7IUxvbmd9XG4gICAqIEBpbm5lclxuICAgKi9cbiAgdmFyIE1BWF9WQUxVRSA9IGZyb21CaXRzKDB4RkZGRkZGRkYgfCAwLCAweDdGRkZGRkZGIHwgMCwgZmFsc2UpO1xuICBcbiAgLyoqXG4gICAqIE1heGltdW0gc2lnbmVkIHZhbHVlLlxuICAgKiBAdHlwZSB7IUxvbmd9XG4gICAqL1xuICBMb25nLk1BWF9WQUxVRSA9IE1BWF9WQUxVRTtcbiAgXG4gIC8qKlxuICAgKiBAdHlwZSB7IUxvbmd9XG4gICAqIEBpbm5lclxuICAgKi9cbiAgdmFyIE1BWF9VTlNJR05FRF9WQUxVRSA9IGZyb21CaXRzKDB4RkZGRkZGRkYgfCAwLCAweEZGRkZGRkZGIHwgMCwgdHJ1ZSk7XG4gIFxuICAvKipcbiAgICogTWF4aW11bSB1bnNpZ25lZCB2YWx1ZS5cbiAgICogQHR5cGUgeyFMb25nfVxuICAgKi9cbiAgTG9uZy5NQVhfVU5TSUdORURfVkFMVUUgPSBNQVhfVU5TSUdORURfVkFMVUU7XG4gIFxuICAvKipcbiAgICogQHR5cGUgeyFMb25nfVxuICAgKiBAaW5uZXJcbiAgICovXG4gIHZhciBNSU5fVkFMVUUgPSBmcm9tQml0cygwLCAweDgwMDAwMDAwIHwgMCwgZmFsc2UpO1xuICBcbiAgLyoqXG4gICAqIE1pbmltdW0gc2lnbmVkIHZhbHVlLlxuICAgKiBAdHlwZSB7IUxvbmd9XG4gICAqL1xuICBMb25nLk1JTl9WQUxVRSA9IE1JTl9WQUxVRTtcbiAgXG4gIC8qKlxuICAgKiBAYWxpYXMgTG9uZy5wcm90b3R5cGVcbiAgICogQGlubmVyXG4gICAqL1xuICB2YXIgTG9uZ1Byb3RvdHlwZSA9IExvbmcucHJvdG90eXBlO1xuICBcbiAgLyoqXG4gICAqIENvbnZlcnRzIHRoZSBMb25nIHRvIGEgMzIgYml0IGludGVnZXIsIGFzc3VtaW5nIGl0IGlzIGEgMzIgYml0IGludGVnZXIuXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHJldHVybnMge251bWJlcn1cbiAgICovXG4gIExvbmdQcm90b3R5cGUudG9JbnQgPSBmdW5jdGlvbiB0b0ludCgpIHtcbiAgICByZXR1cm4gdGhpcy51bnNpZ25lZCA/IHRoaXMubG93ID4+PiAwIDogdGhpcy5sb3c7XG4gIH07XG4gIFxuICAvKipcbiAgICogQ29udmVydHMgdGhlIExvbmcgdG8gYSB0aGUgbmVhcmVzdCBmbG9hdGluZy1wb2ludCByZXByZXNlbnRhdGlvbiBvZiB0aGlzIHZhbHVlIChkb3VibGUsIDUzIGJpdCBtYW50aXNzYSkuXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHJldHVybnMge251bWJlcn1cbiAgICovXG4gIExvbmdQcm90b3R5cGUudG9OdW1iZXIgPSBmdW5jdGlvbiB0b051bWJlcigpIHtcbiAgICBpZiAodGhpcy51bnNpZ25lZCkgcmV0dXJuICh0aGlzLmhpZ2ggPj4+IDApICogVFdPX1BXUl8zMl9EQkwgKyAodGhpcy5sb3cgPj4+IDApO1xuICAgIHJldHVybiB0aGlzLmhpZ2ggKiBUV09fUFdSXzMyX0RCTCArICh0aGlzLmxvdyA+Pj4gMCk7XG4gIH07XG4gIFxuICAvKipcbiAgICogQ29udmVydHMgdGhlIExvbmcgdG8gYSBzdHJpbmcgd3JpdHRlbiBpbiB0aGUgc3BlY2lmaWVkIHJhZGl4LlxuICAgKiBAdGhpcyB7IUxvbmd9XG4gICAqIEBwYXJhbSB7bnVtYmVyPX0gcmFkaXggUmFkaXggKDItMzYpLCBkZWZhdWx0cyB0byAxMFxuICAgKiBAcmV0dXJucyB7c3RyaW5nfVxuICAgKiBAb3ZlcnJpZGVcbiAgICogQHRocm93cyB7UmFuZ2VFcnJvcn0gSWYgYHJhZGl4YCBpcyBvdXQgb2YgcmFuZ2VcbiAgICovXG4gIExvbmdQcm90b3R5cGUudG9TdHJpbmcgPSBmdW5jdGlvbiB0b1N0cmluZyhyYWRpeCkge1xuICAgIHJhZGl4ID0gcmFkaXggfHwgMTA7XG4gICAgaWYgKHJhZGl4IDwgMiB8fCAzNiA8IHJhZGl4KSB0aHJvdyBSYW5nZUVycm9yKCdyYWRpeCcpO1xuICAgIGlmICh0aGlzLmlzWmVybygpKSByZXR1cm4gJzAnO1xuICAgIGlmICh0aGlzLmlzTmVnYXRpdmUoKSkge1xuICAgICAgLy8gVW5zaWduZWQgTG9uZ3MgYXJlIG5ldmVyIG5lZ2F0aXZlXG4gICAgICBpZiAodGhpcy5lcShNSU5fVkFMVUUpKSB7XG4gICAgICAgIC8vIFdlIG5lZWQgdG8gY2hhbmdlIHRoZSBMb25nIHZhbHVlIGJlZm9yZSBpdCBjYW4gYmUgbmVnYXRlZCwgc28gd2UgcmVtb3ZlXG4gICAgICAgIC8vIHRoZSBib3R0b20tbW9zdCBkaWdpdCBpbiB0aGlzIGJhc2UgYW5kIHRoZW4gcmVjdXJzZSB0byBkbyB0aGUgcmVzdC5cbiAgICAgICAgdmFyIHJhZGl4TG9uZyA9IGZyb21OdW1iZXIocmFkaXgpLFxuICAgICAgICAgIGRpdiA9IHRoaXMuZGl2KHJhZGl4TG9uZyksXG4gICAgICAgICAgcmVtMSA9IGRpdi5tdWwocmFkaXhMb25nKS5zdWIodGhpcyk7XG4gICAgICAgIHJldHVybiBkaXYudG9TdHJpbmcocmFkaXgpICsgcmVtMS50b0ludCgpLnRvU3RyaW5nKHJhZGl4KTtcbiAgICAgIH0gZWxzZSByZXR1cm4gJy0nICsgdGhpcy5uZWcoKS50b1N0cmluZyhyYWRpeCk7XG4gICAgfVxuICBcbiAgICAvLyBEbyBzZXZlcmFsICg2KSBkaWdpdHMgZWFjaCB0aW1lIHRocm91Z2ggdGhlIGxvb3AsIHNvIGFzIHRvXG4gICAgLy8gbWluaW1pemUgdGhlIGNhbGxzIHRvIHRoZSB2ZXJ5IGV4cGVuc2l2ZSBlbXVsYXRlZCBkaXYuXG4gICAgdmFyIHJhZGl4VG9Qb3dlciA9IGZyb21OdW1iZXIocG93X2RibChyYWRpeCwgNiksIHRoaXMudW5zaWduZWQpLFxuICAgICAgcmVtID0gdGhpcztcbiAgICB2YXIgcmVzdWx0ID0gJyc7XG4gICAgd2hpbGUgKHRydWUpIHtcbiAgICAgIHZhciByZW1EaXYgPSByZW0uZGl2KHJhZGl4VG9Qb3dlciksXG4gICAgICAgIGludHZhbCA9IHJlbS5zdWIocmVtRGl2Lm11bChyYWRpeFRvUG93ZXIpKS50b0ludCgpID4+PiAwLFxuICAgICAgICBkaWdpdHMgPSBpbnR2YWwudG9TdHJpbmcocmFkaXgpO1xuICAgICAgcmVtID0gcmVtRGl2O1xuICAgICAgaWYgKHJlbS5pc1plcm8oKSkgcmV0dXJuIGRpZ2l0cyArIHJlc3VsdDtlbHNlIHtcbiAgICAgICAgd2hpbGUgKGRpZ2l0cy5sZW5ndGggPCA2KSBkaWdpdHMgPSAnMCcgKyBkaWdpdHM7XG4gICAgICAgIHJlc3VsdCA9ICcnICsgZGlnaXRzICsgcmVzdWx0O1xuICAgICAgfVxuICAgIH1cbiAgfTtcbiAgXG4gIC8qKlxuICAgKiBHZXRzIHRoZSBoaWdoIDMyIGJpdHMgYXMgYSBzaWduZWQgaW50ZWdlci5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcmV0dXJucyB7bnVtYmVyfSBTaWduZWQgaGlnaCBiaXRzXG4gICAqL1xuICBMb25nUHJvdG90eXBlLmdldEhpZ2hCaXRzID0gZnVuY3Rpb24gZ2V0SGlnaEJpdHMoKSB7XG4gICAgcmV0dXJuIHRoaXMuaGlnaDtcbiAgfTtcbiAgXG4gIC8qKlxuICAgKiBHZXRzIHRoZSBoaWdoIDMyIGJpdHMgYXMgYW4gdW5zaWduZWQgaW50ZWdlci5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcmV0dXJucyB7bnVtYmVyfSBVbnNpZ25lZCBoaWdoIGJpdHNcbiAgICovXG4gIExvbmdQcm90b3R5cGUuZ2V0SGlnaEJpdHNVbnNpZ25lZCA9IGZ1bmN0aW9uIGdldEhpZ2hCaXRzVW5zaWduZWQoKSB7XG4gICAgcmV0dXJuIHRoaXMuaGlnaCA+Pj4gMDtcbiAgfTtcbiAgXG4gIC8qKlxuICAgKiBHZXRzIHRoZSBsb3cgMzIgYml0cyBhcyBhIHNpZ25lZCBpbnRlZ2VyLlxuICAgKiBAdGhpcyB7IUxvbmd9XG4gICAqIEByZXR1cm5zIHtudW1iZXJ9IFNpZ25lZCBsb3cgYml0c1xuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5nZXRMb3dCaXRzID0gZnVuY3Rpb24gZ2V0TG93Qml0cygpIHtcbiAgICByZXR1cm4gdGhpcy5sb3c7XG4gIH07XG4gIFxuICAvKipcbiAgICogR2V0cyB0aGUgbG93IDMyIGJpdHMgYXMgYW4gdW5zaWduZWQgaW50ZWdlci5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcmV0dXJucyB7bnVtYmVyfSBVbnNpZ25lZCBsb3cgYml0c1xuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5nZXRMb3dCaXRzVW5zaWduZWQgPSBmdW5jdGlvbiBnZXRMb3dCaXRzVW5zaWduZWQoKSB7XG4gICAgcmV0dXJuIHRoaXMubG93ID4+PiAwO1xuICB9O1xuICBcbiAgLyoqXG4gICAqIEdldHMgdGhlIG51bWJlciBvZiBiaXRzIG5lZWRlZCB0byByZXByZXNlbnQgdGhlIGFic29sdXRlIHZhbHVlIG9mIHRoaXMgTG9uZy5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcmV0dXJucyB7bnVtYmVyfVxuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5nZXROdW1CaXRzQWJzID0gZnVuY3Rpb24gZ2V0TnVtQml0c0FicygpIHtcbiAgICBpZiAodGhpcy5pc05lZ2F0aXZlKCkpXG4gICAgICAvLyBVbnNpZ25lZCBMb25ncyBhcmUgbmV2ZXIgbmVnYXRpdmVcbiAgICAgIHJldHVybiB0aGlzLmVxKE1JTl9WQUxVRSkgPyA2NCA6IHRoaXMubmVnKCkuZ2V0TnVtQml0c0FicygpO1xuICAgIHZhciB2YWwgPSB0aGlzLmhpZ2ggIT0gMCA/IHRoaXMuaGlnaCA6IHRoaXMubG93O1xuICAgIGZvciAodmFyIGJpdCA9IDMxOyBiaXQgPiAwOyBiaXQtLSkgaWYgKCh2YWwgJiAxIDw8IGJpdCkgIT0gMCkgYnJlYWs7XG4gICAgcmV0dXJuIHRoaXMuaGlnaCAhPSAwID8gYml0ICsgMzMgOiBiaXQgKyAxO1xuICB9O1xuICBcbiAgLyoqXG4gICAqIFRlc3RzIGlmIHRoaXMgTG9uZyBjYW4gYmUgc2FmZWx5IHJlcHJlc2VudGVkIGFzIGEgSmF2YVNjcmlwdCBudW1iZXIuXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHJldHVybnMge2Jvb2xlYW59XG4gICAqL1xuICBMb25nUHJvdG90eXBlLmlzU2FmZUludGVnZXIgPSBmdW5jdGlvbiBpc1NhZmVJbnRlZ2VyKCkge1xuICAgIC8vIDJeNTMtMSBpcyB0aGUgbWF4aW11bSBzYWZlIHZhbHVlXG4gICAgdmFyIHRvcDExQml0cyA9IHRoaXMuaGlnaCA+PiAyMTtcbiAgICAvLyBbMCwgMl41My0xXVxuICAgIGlmICghdG9wMTFCaXRzKSByZXR1cm4gdHJ1ZTtcbiAgICAvLyA+IDJeNTMtMVxuICAgIGlmICh0aGlzLnVuc2lnbmVkKSByZXR1cm4gZmFsc2U7XG4gICAgLy8gWy0yXjUzLCAtMV0gZXhjZXB0IC0yXjUzXG4gICAgcmV0dXJuIHRvcDExQml0cyA9PT0gLTEgJiYgISh0aGlzLmxvdyA9PT0gMCAmJiB0aGlzLmhpZ2ggPT09IC0weDIwMDAwMCk7XG4gIH07XG4gIFxuICAvKipcbiAgICogVGVzdHMgaWYgdGhpcyBMb25nJ3MgdmFsdWUgZXF1YWxzIHplcm8uXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHJldHVybnMge2Jvb2xlYW59XG4gICAqL1xuICBMb25nUHJvdG90eXBlLmlzWmVybyA9IGZ1bmN0aW9uIGlzWmVybygpIHtcbiAgICByZXR1cm4gdGhpcy5oaWdoID09PSAwICYmIHRoaXMubG93ID09PSAwO1xuICB9O1xuICBcbiAgLyoqXG4gICAqIFRlc3RzIGlmIHRoaXMgTG9uZydzIHZhbHVlIGVxdWFscyB6ZXJvLiBUaGlzIGlzIGFuIGFsaWFzIG9mIHtAbGluayBMb25nI2lzWmVyb30uXG4gICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5lcXogPSBMb25nUHJvdG90eXBlLmlzWmVybztcbiAgXG4gIC8qKlxuICAgKiBUZXN0cyBpZiB0aGlzIExvbmcncyB2YWx1ZSBpcyBuZWdhdGl2ZS5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAgICovXG4gIExvbmdQcm90b3R5cGUuaXNOZWdhdGl2ZSA9IGZ1bmN0aW9uIGlzTmVnYXRpdmUoKSB7XG4gICAgcmV0dXJuICF0aGlzLnVuc2lnbmVkICYmIHRoaXMuaGlnaCA8IDA7XG4gIH07XG4gIFxuICAvKipcbiAgICogVGVzdHMgaWYgdGhpcyBMb25nJ3MgdmFsdWUgaXMgcG9zaXRpdmUgb3IgemVyby5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAgICovXG4gIExvbmdQcm90b3R5cGUuaXNQb3NpdGl2ZSA9IGZ1bmN0aW9uIGlzUG9zaXRpdmUoKSB7XG4gICAgcmV0dXJuIHRoaXMudW5zaWduZWQgfHwgdGhpcy5oaWdoID49IDA7XG4gIH07XG4gIFxuICAvKipcbiAgICogVGVzdHMgaWYgdGhpcyBMb25nJ3MgdmFsdWUgaXMgb2RkLlxuICAgKiBAdGhpcyB7IUxvbmd9XG4gICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5pc09kZCA9IGZ1bmN0aW9uIGlzT2RkKCkge1xuICAgIHJldHVybiAodGhpcy5sb3cgJiAxKSA9PT0gMTtcbiAgfTtcbiAgXG4gIC8qKlxuICAgKiBUZXN0cyBpZiB0aGlzIExvbmcncyB2YWx1ZSBpcyBldmVuLlxuICAgKiBAdGhpcyB7IUxvbmd9XG4gICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5pc0V2ZW4gPSBmdW5jdGlvbiBpc0V2ZW4oKSB7XG4gICAgcmV0dXJuICh0aGlzLmxvdyAmIDEpID09PSAwO1xuICB9O1xuICBcbiAgLyoqXG4gICAqIFRlc3RzIGlmIHRoaXMgTG9uZydzIHZhbHVlIGVxdWFscyB0aGUgc3BlY2lmaWVkJ3MuXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHBhcmFtIHshTG9uZ3xudW1iZXJ8YmlnaW50fHN0cmluZ30gb3RoZXIgT3RoZXIgdmFsdWVcbiAgICogQHJldHVybnMge2Jvb2xlYW59XG4gICAqL1xuICBMb25nUHJvdG90eXBlLmVxdWFscyA9IGZ1bmN0aW9uIGVxdWFscyhvdGhlcikge1xuICAgIGlmICghaXNMb25nKG90aGVyKSkgb3RoZXIgPSBmcm9tVmFsdWUob3RoZXIpO1xuICAgIGlmICh0aGlzLnVuc2lnbmVkICE9PSBvdGhlci51bnNpZ25lZCAmJiB0aGlzLmhpZ2ggPj4+IDMxID09PSAxICYmIG90aGVyLmhpZ2ggPj4+IDMxID09PSAxKSByZXR1cm4gZmFsc2U7XG4gICAgcmV0dXJuIHRoaXMuaGlnaCA9PT0gb3RoZXIuaGlnaCAmJiB0aGlzLmxvdyA9PT0gb3RoZXIubG93O1xuICB9O1xuICBcbiAgLyoqXG4gICAqIFRlc3RzIGlmIHRoaXMgTG9uZydzIHZhbHVlIGVxdWFscyB0aGUgc3BlY2lmaWVkJ3MuIFRoaXMgaXMgYW4gYWxpYXMgb2Yge0BsaW5rIExvbmcjZXF1YWxzfS5cbiAgICogQGZ1bmN0aW9uXG4gICAqIEBwYXJhbSB7IUxvbmd8bnVtYmVyfGJpZ2ludHxzdHJpbmd9IG90aGVyIE90aGVyIHZhbHVlXG4gICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5lcSA9IExvbmdQcm90b3R5cGUuZXF1YWxzO1xuICBcbiAgLyoqXG4gICAqIFRlc3RzIGlmIHRoaXMgTG9uZydzIHZhbHVlIGRpZmZlcnMgZnJvbSB0aGUgc3BlY2lmaWVkJ3MuXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHBhcmFtIHshTG9uZ3xudW1iZXJ8YmlnaW50fHN0cmluZ30gb3RoZXIgT3RoZXIgdmFsdWVcbiAgICogQHJldHVybnMge2Jvb2xlYW59XG4gICAqL1xuICBMb25nUHJvdG90eXBlLm5vdEVxdWFscyA9IGZ1bmN0aW9uIG5vdEVxdWFscyhvdGhlcikge1xuICAgIHJldHVybiAhdGhpcy5lcSgvKiB2YWxpZGF0ZXMgKi9vdGhlcik7XG4gIH07XG4gIFxuICAvKipcbiAgICogVGVzdHMgaWYgdGhpcyBMb25nJ3MgdmFsdWUgZGlmZmVycyBmcm9tIHRoZSBzcGVjaWZpZWQncy4gVGhpcyBpcyBhbiBhbGlhcyBvZiB7QGxpbmsgTG9uZyNub3RFcXVhbHN9LlxuICAgKiBAZnVuY3Rpb25cbiAgICogQHBhcmFtIHshTG9uZ3xudW1iZXJ8YmlnaW50fHN0cmluZ30gb3RoZXIgT3RoZXIgdmFsdWVcbiAgICogQHJldHVybnMge2Jvb2xlYW59XG4gICAqL1xuICBMb25nUHJvdG90eXBlLm5lcSA9IExvbmdQcm90b3R5cGUubm90RXF1YWxzO1xuICBcbiAgLyoqXG4gICAqIFRlc3RzIGlmIHRoaXMgTG9uZydzIHZhbHVlIGRpZmZlcnMgZnJvbSB0aGUgc3BlY2lmaWVkJ3MuIFRoaXMgaXMgYW4gYWxpYXMgb2Yge0BsaW5rIExvbmcjbm90RXF1YWxzfS5cbiAgICogQGZ1bmN0aW9uXG4gICAqIEBwYXJhbSB7IUxvbmd8bnVtYmVyfGJpZ2ludHxzdHJpbmd9IG90aGVyIE90aGVyIHZhbHVlXG4gICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5uZSA9IExvbmdQcm90b3R5cGUubm90RXF1YWxzO1xuICBcbiAgLyoqXG4gICAqIFRlc3RzIGlmIHRoaXMgTG9uZydzIHZhbHVlIGlzIGxlc3MgdGhhbiB0aGUgc3BlY2lmaWVkJ3MuXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHBhcmFtIHshTG9uZ3xudW1iZXJ8YmlnaW50fHN0cmluZ30gb3RoZXIgT3RoZXIgdmFsdWVcbiAgICogQHJldHVybnMge2Jvb2xlYW59XG4gICAqL1xuICBMb25nUHJvdG90eXBlLmxlc3NUaGFuID0gZnVuY3Rpb24gbGVzc1RoYW4ob3RoZXIpIHtcbiAgICByZXR1cm4gdGhpcy5jb21wKC8qIHZhbGlkYXRlcyAqL290aGVyKSA8IDA7XG4gIH07XG4gIFxuICAvKipcbiAgICogVGVzdHMgaWYgdGhpcyBMb25nJ3MgdmFsdWUgaXMgbGVzcyB0aGFuIHRoZSBzcGVjaWZpZWQncy4gVGhpcyBpcyBhbiBhbGlhcyBvZiB7QGxpbmsgTG9uZyNsZXNzVGhhbn0uXG4gICAqIEBmdW5jdGlvblxuICAgKiBAcGFyYW0geyFMb25nfG51bWJlcnxiaWdpbnR8c3RyaW5nfSBvdGhlciBPdGhlciB2YWx1ZVxuICAgKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAgICovXG4gIExvbmdQcm90b3R5cGUubHQgPSBMb25nUHJvdG90eXBlLmxlc3NUaGFuO1xuICBcbiAgLyoqXG4gICAqIFRlc3RzIGlmIHRoaXMgTG9uZydzIHZhbHVlIGlzIGxlc3MgdGhhbiBvciBlcXVhbCB0aGUgc3BlY2lmaWVkJ3MuXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHBhcmFtIHshTG9uZ3xudW1iZXJ8YmlnaW50fHN0cmluZ30gb3RoZXIgT3RoZXIgdmFsdWVcbiAgICogQHJldHVybnMge2Jvb2xlYW59XG4gICAqL1xuICBMb25nUHJvdG90eXBlLmxlc3NUaGFuT3JFcXVhbCA9IGZ1bmN0aW9uIGxlc3NUaGFuT3JFcXVhbChvdGhlcikge1xuICAgIHJldHVybiB0aGlzLmNvbXAoLyogdmFsaWRhdGVzICovb3RoZXIpIDw9IDA7XG4gIH07XG4gIFxuICAvKipcbiAgICogVGVzdHMgaWYgdGhpcyBMb25nJ3MgdmFsdWUgaXMgbGVzcyB0aGFuIG9yIGVxdWFsIHRoZSBzcGVjaWZpZWQncy4gVGhpcyBpcyBhbiBhbGlhcyBvZiB7QGxpbmsgTG9uZyNsZXNzVGhhbk9yRXF1YWx9LlxuICAgKiBAZnVuY3Rpb25cbiAgICogQHBhcmFtIHshTG9uZ3xudW1iZXJ8YmlnaW50fHN0cmluZ30gb3RoZXIgT3RoZXIgdmFsdWVcbiAgICogQHJldHVybnMge2Jvb2xlYW59XG4gICAqL1xuICBMb25nUHJvdG90eXBlLmx0ZSA9IExvbmdQcm90b3R5cGUubGVzc1RoYW5PckVxdWFsO1xuICBcbiAgLyoqXG4gICAqIFRlc3RzIGlmIHRoaXMgTG9uZydzIHZhbHVlIGlzIGxlc3MgdGhhbiBvciBlcXVhbCB0aGUgc3BlY2lmaWVkJ3MuIFRoaXMgaXMgYW4gYWxpYXMgb2Yge0BsaW5rIExvbmcjbGVzc1RoYW5PckVxdWFsfS5cbiAgICogQGZ1bmN0aW9uXG4gICAqIEBwYXJhbSB7IUxvbmd8bnVtYmVyfGJpZ2ludHxzdHJpbmd9IG90aGVyIE90aGVyIHZhbHVlXG4gICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5sZSA9IExvbmdQcm90b3R5cGUubGVzc1RoYW5PckVxdWFsO1xuICBcbiAgLyoqXG4gICAqIFRlc3RzIGlmIHRoaXMgTG9uZydzIHZhbHVlIGlzIGdyZWF0ZXIgdGhhbiB0aGUgc3BlY2lmaWVkJ3MuXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHBhcmFtIHshTG9uZ3xudW1iZXJ8YmlnaW50fHN0cmluZ30gb3RoZXIgT3RoZXIgdmFsdWVcbiAgICogQHJldHVybnMge2Jvb2xlYW59XG4gICAqL1xuICBMb25nUHJvdG90eXBlLmdyZWF0ZXJUaGFuID0gZnVuY3Rpb24gZ3JlYXRlclRoYW4ob3RoZXIpIHtcbiAgICByZXR1cm4gdGhpcy5jb21wKC8qIHZhbGlkYXRlcyAqL290aGVyKSA+IDA7XG4gIH07XG4gIFxuICAvKipcbiAgICogVGVzdHMgaWYgdGhpcyBMb25nJ3MgdmFsdWUgaXMgZ3JlYXRlciB0aGFuIHRoZSBzcGVjaWZpZWQncy4gVGhpcyBpcyBhbiBhbGlhcyBvZiB7QGxpbmsgTG9uZyNncmVhdGVyVGhhbn0uXG4gICAqIEBmdW5jdGlvblxuICAgKiBAcGFyYW0geyFMb25nfG51bWJlcnxiaWdpbnR8c3RyaW5nfSBvdGhlciBPdGhlciB2YWx1ZVxuICAgKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAgICovXG4gIExvbmdQcm90b3R5cGUuZ3QgPSBMb25nUHJvdG90eXBlLmdyZWF0ZXJUaGFuO1xuICBcbiAgLyoqXG4gICAqIFRlc3RzIGlmIHRoaXMgTG9uZydzIHZhbHVlIGlzIGdyZWF0ZXIgdGhhbiBvciBlcXVhbCB0aGUgc3BlY2lmaWVkJ3MuXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHBhcmFtIHshTG9uZ3xudW1iZXJ8YmlnaW50fHN0cmluZ30gb3RoZXIgT3RoZXIgdmFsdWVcbiAgICogQHJldHVybnMge2Jvb2xlYW59XG4gICAqL1xuICBMb25nUHJvdG90eXBlLmdyZWF0ZXJUaGFuT3JFcXVhbCA9IGZ1bmN0aW9uIGdyZWF0ZXJUaGFuT3JFcXVhbChvdGhlcikge1xuICAgIHJldHVybiB0aGlzLmNvbXAoLyogdmFsaWRhdGVzICovb3RoZXIpID49IDA7XG4gIH07XG4gIFxuICAvKipcbiAgICogVGVzdHMgaWYgdGhpcyBMb25nJ3MgdmFsdWUgaXMgZ3JlYXRlciB0aGFuIG9yIGVxdWFsIHRoZSBzcGVjaWZpZWQncy4gVGhpcyBpcyBhbiBhbGlhcyBvZiB7QGxpbmsgTG9uZyNncmVhdGVyVGhhbk9yRXF1YWx9LlxuICAgKiBAZnVuY3Rpb25cbiAgICogQHBhcmFtIHshTG9uZ3xudW1iZXJ8YmlnaW50fHN0cmluZ30gb3RoZXIgT3RoZXIgdmFsdWVcbiAgICogQHJldHVybnMge2Jvb2xlYW59XG4gICAqL1xuICBMb25nUHJvdG90eXBlLmd0ZSA9IExvbmdQcm90b3R5cGUuZ3JlYXRlclRoYW5PckVxdWFsO1xuICBcbiAgLyoqXG4gICAqIFRlc3RzIGlmIHRoaXMgTG9uZydzIHZhbHVlIGlzIGdyZWF0ZXIgdGhhbiBvciBlcXVhbCB0aGUgc3BlY2lmaWVkJ3MuIFRoaXMgaXMgYW4gYWxpYXMgb2Yge0BsaW5rIExvbmcjZ3JlYXRlclRoYW5PckVxdWFsfS5cbiAgICogQGZ1bmN0aW9uXG4gICAqIEBwYXJhbSB7IUxvbmd8bnVtYmVyfGJpZ2ludHxzdHJpbmd9IG90aGVyIE90aGVyIHZhbHVlXG4gICAqIEByZXR1cm5zIHtib29sZWFufVxuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5nZSA9IExvbmdQcm90b3R5cGUuZ3JlYXRlclRoYW5PckVxdWFsO1xuICBcbiAgLyoqXG4gICAqIENvbXBhcmVzIHRoaXMgTG9uZydzIHZhbHVlIHdpdGggdGhlIHNwZWNpZmllZCdzLlxuICAgKiBAdGhpcyB7IUxvbmd9XG4gICAqIEBwYXJhbSB7IUxvbmd8bnVtYmVyfGJpZ2ludHxzdHJpbmd9IG90aGVyIE90aGVyIHZhbHVlXG4gICAqIEByZXR1cm5zIHtudW1iZXJ9IDAgaWYgdGhleSBhcmUgdGhlIHNhbWUsIDEgaWYgdGhlIHRoaXMgaXMgZ3JlYXRlciBhbmQgLTFcbiAgICogIGlmIHRoZSBnaXZlbiBvbmUgaXMgZ3JlYXRlclxuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5jb21wYXJlID0gZnVuY3Rpb24gY29tcGFyZShvdGhlcikge1xuICAgIGlmICghaXNMb25nKG90aGVyKSkgb3RoZXIgPSBmcm9tVmFsdWUob3RoZXIpO1xuICAgIGlmICh0aGlzLmVxKG90aGVyKSkgcmV0dXJuIDA7XG4gICAgdmFyIHRoaXNOZWcgPSB0aGlzLmlzTmVnYXRpdmUoKSxcbiAgICAgIG90aGVyTmVnID0gb3RoZXIuaXNOZWdhdGl2ZSgpO1xuICAgIGlmICh0aGlzTmVnICYmICFvdGhlck5lZykgcmV0dXJuIC0xO1xuICAgIGlmICghdGhpc05lZyAmJiBvdGhlck5lZykgcmV0dXJuIDE7XG4gICAgLy8gQXQgdGhpcyBwb2ludCB0aGUgc2lnbiBiaXRzIGFyZSB0aGUgc2FtZVxuICAgIGlmICghdGhpcy51bnNpZ25lZCkgcmV0dXJuIHRoaXMuc3ViKG90aGVyKS5pc05lZ2F0aXZlKCkgPyAtMSA6IDE7XG4gICAgLy8gQm90aCBhcmUgcG9zaXRpdmUgaWYgYXQgbGVhc3Qgb25lIGlzIHVuc2lnbmVkXG4gICAgcmV0dXJuIG90aGVyLmhpZ2ggPj4+IDAgPiB0aGlzLmhpZ2ggPj4+IDAgfHwgb3RoZXIuaGlnaCA9PT0gdGhpcy5oaWdoICYmIG90aGVyLmxvdyA+Pj4gMCA+IHRoaXMubG93ID4+PiAwID8gLTEgOiAxO1xuICB9O1xuICBcbiAgLyoqXG4gICAqIENvbXBhcmVzIHRoaXMgTG9uZydzIHZhbHVlIHdpdGggdGhlIHNwZWNpZmllZCdzLiBUaGlzIGlzIGFuIGFsaWFzIG9mIHtAbGluayBMb25nI2NvbXBhcmV9LlxuICAgKiBAZnVuY3Rpb25cbiAgICogQHBhcmFtIHshTG9uZ3xudW1iZXJ8YmlnaW50fHN0cmluZ30gb3RoZXIgT3RoZXIgdmFsdWVcbiAgICogQHJldHVybnMge251bWJlcn0gMCBpZiB0aGV5IGFyZSB0aGUgc2FtZSwgMSBpZiB0aGUgdGhpcyBpcyBncmVhdGVyIGFuZCAtMVxuICAgKiAgaWYgdGhlIGdpdmVuIG9uZSBpcyBncmVhdGVyXG4gICAqL1xuICBMb25nUHJvdG90eXBlLmNvbXAgPSBMb25nUHJvdG90eXBlLmNvbXBhcmU7XG4gIFxuICAvKipcbiAgICogTmVnYXRlcyB0aGlzIExvbmcncyB2YWx1ZS5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcmV0dXJucyB7IUxvbmd9IE5lZ2F0ZWQgTG9uZ1xuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5uZWdhdGUgPSBmdW5jdGlvbiBuZWdhdGUoKSB7XG4gICAgaWYgKCF0aGlzLnVuc2lnbmVkICYmIHRoaXMuZXEoTUlOX1ZBTFVFKSkgcmV0dXJuIE1JTl9WQUxVRTtcbiAgICByZXR1cm4gdGhpcy5ub3QoKS5hZGQoT05FKTtcbiAgfTtcbiAgXG4gIC8qKlxuICAgKiBOZWdhdGVzIHRoaXMgTG9uZydzIHZhbHVlLiBUaGlzIGlzIGFuIGFsaWFzIG9mIHtAbGluayBMb25nI25lZ2F0ZX0uXG4gICAqIEBmdW5jdGlvblxuICAgKiBAcmV0dXJucyB7IUxvbmd9IE5lZ2F0ZWQgTG9uZ1xuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5uZWcgPSBMb25nUHJvdG90eXBlLm5lZ2F0ZTtcbiAgXG4gIC8qKlxuICAgKiBSZXR1cm5zIHRoZSBzdW0gb2YgdGhpcyBhbmQgdGhlIHNwZWNpZmllZCBMb25nLlxuICAgKiBAdGhpcyB7IUxvbmd9XG4gICAqIEBwYXJhbSB7IUxvbmd8bnVtYmVyfGJpZ2ludHxzdHJpbmd9IGFkZGVuZCBBZGRlbmRcbiAgICogQHJldHVybnMgeyFMb25nfSBTdW1cbiAgICovXG4gIExvbmdQcm90b3R5cGUuYWRkID0gZnVuY3Rpb24gYWRkKGFkZGVuZCkge1xuICAgIGlmICghaXNMb25nKGFkZGVuZCkpIGFkZGVuZCA9IGZyb21WYWx1ZShhZGRlbmQpO1xuICBcbiAgICAvLyBEaXZpZGUgZWFjaCBudW1iZXIgaW50byA0IGNodW5rcyBvZiAxNiBiaXRzLCBhbmQgdGhlbiBzdW0gdGhlIGNodW5rcy5cbiAgXG4gICAgdmFyIGE0OCA9IHRoaXMuaGlnaCA+Pj4gMTY7XG4gICAgdmFyIGEzMiA9IHRoaXMuaGlnaCAmIDB4RkZGRjtcbiAgICB2YXIgYTE2ID0gdGhpcy5sb3cgPj4+IDE2O1xuICAgIHZhciBhMDAgPSB0aGlzLmxvdyAmIDB4RkZGRjtcbiAgICB2YXIgYjQ4ID0gYWRkZW5kLmhpZ2ggPj4+IDE2O1xuICAgIHZhciBiMzIgPSBhZGRlbmQuaGlnaCAmIDB4RkZGRjtcbiAgICB2YXIgYjE2ID0gYWRkZW5kLmxvdyA+Pj4gMTY7XG4gICAgdmFyIGIwMCA9IGFkZGVuZC5sb3cgJiAweEZGRkY7XG4gICAgdmFyIGM0OCA9IDAsXG4gICAgICBjMzIgPSAwLFxuICAgICAgYzE2ID0gMCxcbiAgICAgIGMwMCA9IDA7XG4gICAgYzAwICs9IGEwMCArIGIwMDtcbiAgICBjMTYgKz0gYzAwID4+PiAxNjtcbiAgICBjMDAgJj0gMHhGRkZGO1xuICAgIGMxNiArPSBhMTYgKyBiMTY7XG4gICAgYzMyICs9IGMxNiA+Pj4gMTY7XG4gICAgYzE2ICY9IDB4RkZGRjtcbiAgICBjMzIgKz0gYTMyICsgYjMyO1xuICAgIGM0OCArPSBjMzIgPj4+IDE2O1xuICAgIGMzMiAmPSAweEZGRkY7XG4gICAgYzQ4ICs9IGE0OCArIGI0ODtcbiAgICBjNDggJj0gMHhGRkZGO1xuICAgIHJldHVybiBmcm9tQml0cyhjMTYgPDwgMTYgfCBjMDAsIGM0OCA8PCAxNiB8IGMzMiwgdGhpcy51bnNpZ25lZCk7XG4gIH07XG4gIFxuICAvKipcbiAgICogUmV0dXJucyB0aGUgZGlmZmVyZW5jZSBvZiB0aGlzIGFuZCB0aGUgc3BlY2lmaWVkIExvbmcuXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHBhcmFtIHshTG9uZ3xudW1iZXJ8YmlnaW50fHN0cmluZ30gc3VidHJhaGVuZCBTdWJ0cmFoZW5kXG4gICAqIEByZXR1cm5zIHshTG9uZ30gRGlmZmVyZW5jZVxuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5zdWJ0cmFjdCA9IGZ1bmN0aW9uIHN1YnRyYWN0KHN1YnRyYWhlbmQpIHtcbiAgICBpZiAoIWlzTG9uZyhzdWJ0cmFoZW5kKSkgc3VidHJhaGVuZCA9IGZyb21WYWx1ZShzdWJ0cmFoZW5kKTtcbiAgICByZXR1cm4gdGhpcy5hZGQoc3VidHJhaGVuZC5uZWcoKSk7XG4gIH07XG4gIFxuICAvKipcbiAgICogUmV0dXJucyB0aGUgZGlmZmVyZW5jZSBvZiB0aGlzIGFuZCB0aGUgc3BlY2lmaWVkIExvbmcuIFRoaXMgaXMgYW4gYWxpYXMgb2Yge0BsaW5rIExvbmcjc3VidHJhY3R9LlxuICAgKiBAZnVuY3Rpb25cbiAgICogQHBhcmFtIHshTG9uZ3xudW1iZXJ8YmlnaW50fHN0cmluZ30gc3VidHJhaGVuZCBTdWJ0cmFoZW5kXG4gICAqIEByZXR1cm5zIHshTG9uZ30gRGlmZmVyZW5jZVxuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5zdWIgPSBMb25nUHJvdG90eXBlLnN1YnRyYWN0O1xuICBcbiAgLyoqXG4gICAqIFJldHVybnMgdGhlIHByb2R1Y3Qgb2YgdGhpcyBhbmQgdGhlIHNwZWNpZmllZCBMb25nLlxuICAgKiBAdGhpcyB7IUxvbmd9XG4gICAqIEBwYXJhbSB7IUxvbmd8bnVtYmVyfGJpZ2ludHxzdHJpbmd9IG11bHRpcGxpZXIgTXVsdGlwbGllclxuICAgKiBAcmV0dXJucyB7IUxvbmd9IFByb2R1Y3RcbiAgICovXG4gIExvbmdQcm90b3R5cGUubXVsdGlwbHkgPSBmdW5jdGlvbiBtdWx0aXBseShtdWx0aXBsaWVyKSB7XG4gICAgaWYgKHRoaXMuaXNaZXJvKCkpIHJldHVybiB0aGlzO1xuICAgIGlmICghaXNMb25nKG11bHRpcGxpZXIpKSBtdWx0aXBsaWVyID0gZnJvbVZhbHVlKG11bHRpcGxpZXIpO1xuICBcbiAgICAvLyB1c2Ugd2FzbSBzdXBwb3J0IGlmIHByZXNlbnRcbiAgICBpZiAod2FzbSkge1xuICAgICAgdmFyIGxvdyA9IHdhc21bXCJtdWxcIl0odGhpcy5sb3csIHRoaXMuaGlnaCwgbXVsdGlwbGllci5sb3csIG11bHRpcGxpZXIuaGlnaCk7XG4gICAgICByZXR1cm4gZnJvbUJpdHMobG93LCB3YXNtW1wiZ2V0X2hpZ2hcIl0oKSwgdGhpcy51bnNpZ25lZCk7XG4gICAgfVxuICAgIGlmIChtdWx0aXBsaWVyLmlzWmVybygpKSByZXR1cm4gdGhpcy51bnNpZ25lZCA/IFVaRVJPIDogWkVSTztcbiAgICBpZiAodGhpcy5lcShNSU5fVkFMVUUpKSByZXR1cm4gbXVsdGlwbGllci5pc09kZCgpID8gTUlOX1ZBTFVFIDogWkVSTztcbiAgICBpZiAobXVsdGlwbGllci5lcShNSU5fVkFMVUUpKSByZXR1cm4gdGhpcy5pc09kZCgpID8gTUlOX1ZBTFVFIDogWkVSTztcbiAgICBpZiAodGhpcy5pc05lZ2F0aXZlKCkpIHtcbiAgICAgIGlmIChtdWx0aXBsaWVyLmlzTmVnYXRpdmUoKSkgcmV0dXJuIHRoaXMubmVnKCkubXVsKG11bHRpcGxpZXIubmVnKCkpO2Vsc2UgcmV0dXJuIHRoaXMubmVnKCkubXVsKG11bHRpcGxpZXIpLm5lZygpO1xuICAgIH0gZWxzZSBpZiAobXVsdGlwbGllci5pc05lZ2F0aXZlKCkpIHJldHVybiB0aGlzLm11bChtdWx0aXBsaWVyLm5lZygpKS5uZWcoKTtcbiAgXG4gICAgLy8gSWYgYm90aCBsb25ncyBhcmUgc21hbGwsIHVzZSBmbG9hdCBtdWx0aXBsaWNhdGlvblxuICAgIGlmICh0aGlzLmx0KFRXT19QV1JfMjQpICYmIG11bHRpcGxpZXIubHQoVFdPX1BXUl8yNCkpIHJldHVybiBmcm9tTnVtYmVyKHRoaXMudG9OdW1iZXIoKSAqIG11bHRpcGxpZXIudG9OdW1iZXIoKSwgdGhpcy51bnNpZ25lZCk7XG4gIFxuICAgIC8vIERpdmlkZSBlYWNoIGxvbmcgaW50byA0IGNodW5rcyBvZiAxNiBiaXRzLCBhbmQgdGhlbiBhZGQgdXAgNHg0IHByb2R1Y3RzLlxuICAgIC8vIFdlIGNhbiBza2lwIHByb2R1Y3RzIHRoYXQgd291bGQgb3ZlcmZsb3cuXG4gIFxuICAgIHZhciBhNDggPSB0aGlzLmhpZ2ggPj4+IDE2O1xuICAgIHZhciBhMzIgPSB0aGlzLmhpZ2ggJiAweEZGRkY7XG4gICAgdmFyIGExNiA9IHRoaXMubG93ID4+PiAxNjtcbiAgICB2YXIgYTAwID0gdGhpcy5sb3cgJiAweEZGRkY7XG4gICAgdmFyIGI0OCA9IG11bHRpcGxpZXIuaGlnaCA+Pj4gMTY7XG4gICAgdmFyIGIzMiA9IG11bHRpcGxpZXIuaGlnaCAmIDB4RkZGRjtcbiAgICB2YXIgYjE2ID0gbXVsdGlwbGllci5sb3cgPj4+IDE2O1xuICAgIHZhciBiMDAgPSBtdWx0aXBsaWVyLmxvdyAmIDB4RkZGRjtcbiAgICB2YXIgYzQ4ID0gMCxcbiAgICAgIGMzMiA9IDAsXG4gICAgICBjMTYgPSAwLFxuICAgICAgYzAwID0gMDtcbiAgICBjMDAgKz0gYTAwICogYjAwO1xuICAgIGMxNiArPSBjMDAgPj4+IDE2O1xuICAgIGMwMCAmPSAweEZGRkY7XG4gICAgYzE2ICs9IGExNiAqIGIwMDtcbiAgICBjMzIgKz0gYzE2ID4+PiAxNjtcbiAgICBjMTYgJj0gMHhGRkZGO1xuICAgIGMxNiArPSBhMDAgKiBiMTY7XG4gICAgYzMyICs9IGMxNiA+Pj4gMTY7XG4gICAgYzE2ICY9IDB4RkZGRjtcbiAgICBjMzIgKz0gYTMyICogYjAwO1xuICAgIGM0OCArPSBjMzIgPj4+IDE2O1xuICAgIGMzMiAmPSAweEZGRkY7XG4gICAgYzMyICs9IGExNiAqIGIxNjtcbiAgICBjNDggKz0gYzMyID4+PiAxNjtcbiAgICBjMzIgJj0gMHhGRkZGO1xuICAgIGMzMiArPSBhMDAgKiBiMzI7XG4gICAgYzQ4ICs9IGMzMiA+Pj4gMTY7XG4gICAgYzMyICY9IDB4RkZGRjtcbiAgICBjNDggKz0gYTQ4ICogYjAwICsgYTMyICogYjE2ICsgYTE2ICogYjMyICsgYTAwICogYjQ4O1xuICAgIGM0OCAmPSAweEZGRkY7XG4gICAgcmV0dXJuIGZyb21CaXRzKGMxNiA8PCAxNiB8IGMwMCwgYzQ4IDw8IDE2IHwgYzMyLCB0aGlzLnVuc2lnbmVkKTtcbiAgfTtcbiAgXG4gIC8qKlxuICAgKiBSZXR1cm5zIHRoZSBwcm9kdWN0IG9mIHRoaXMgYW5kIHRoZSBzcGVjaWZpZWQgTG9uZy4gVGhpcyBpcyBhbiBhbGlhcyBvZiB7QGxpbmsgTG9uZyNtdWx0aXBseX0uXG4gICAqIEBmdW5jdGlvblxuICAgKiBAcGFyYW0geyFMb25nfG51bWJlcnxiaWdpbnR8c3RyaW5nfSBtdWx0aXBsaWVyIE11bHRpcGxpZXJcbiAgICogQHJldHVybnMgeyFMb25nfSBQcm9kdWN0XG4gICAqL1xuICBMb25nUHJvdG90eXBlLm11bCA9IExvbmdQcm90b3R5cGUubXVsdGlwbHk7XG4gIFxuICAvKipcbiAgICogUmV0dXJucyB0aGlzIExvbmcgZGl2aWRlZCBieSB0aGUgc3BlY2lmaWVkLiBUaGUgcmVzdWx0IGlzIHNpZ25lZCBpZiB0aGlzIExvbmcgaXMgc2lnbmVkIG9yXG4gICAqICB1bnNpZ25lZCBpZiB0aGlzIExvbmcgaXMgdW5zaWduZWQuXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHBhcmFtIHshTG9uZ3xudW1iZXJ8YmlnaW50fHN0cmluZ30gZGl2aXNvciBEaXZpc29yXG4gICAqIEByZXR1cm5zIHshTG9uZ30gUXVvdGllbnRcbiAgICovXG4gIExvbmdQcm90b3R5cGUuZGl2aWRlID0gZnVuY3Rpb24gZGl2aWRlKGRpdmlzb3IpIHtcbiAgICBpZiAoIWlzTG9uZyhkaXZpc29yKSkgZGl2aXNvciA9IGZyb21WYWx1ZShkaXZpc29yKTtcbiAgICBpZiAoZGl2aXNvci5pc1plcm8oKSkgdGhyb3cgRXJyb3IoJ2RpdmlzaW9uIGJ5IHplcm8nKTtcbiAgXG4gICAgLy8gdXNlIHdhc20gc3VwcG9ydCBpZiBwcmVzZW50XG4gICAgaWYgKHdhc20pIHtcbiAgICAgIC8vIGd1YXJkIGFnYWluc3Qgc2lnbmVkIGRpdmlzaW9uIG92ZXJmbG93OiB0aGUgbGFyZ2VzdFxuICAgICAgLy8gbmVnYXRpdmUgbnVtYmVyIC8gLTEgd291bGQgYmUgMSBsYXJnZXIgdGhhbiB0aGUgbGFyZ2VzdFxuICAgICAgLy8gcG9zaXRpdmUgbnVtYmVyLCBkdWUgdG8gdHdvJ3MgY29tcGxlbWVudC5cbiAgICAgIGlmICghdGhpcy51bnNpZ25lZCAmJiB0aGlzLmhpZ2ggPT09IC0weDgwMDAwMDAwICYmIGRpdmlzb3IubG93ID09PSAtMSAmJiBkaXZpc29yLmhpZ2ggPT09IC0xKSB7XG4gICAgICAgIC8vIGJlIGNvbnNpc3RlbnQgd2l0aCBub24td2FzbSBjb2RlIHBhdGhcbiAgICAgICAgcmV0dXJuIHRoaXM7XG4gICAgICB9XG4gICAgICB2YXIgbG93ID0gKHRoaXMudW5zaWduZWQgPyB3YXNtW1wiZGl2X3VcIl0gOiB3YXNtW1wiZGl2X3NcIl0pKHRoaXMubG93LCB0aGlzLmhpZ2gsIGRpdmlzb3IubG93LCBkaXZpc29yLmhpZ2gpO1xuICAgICAgcmV0dXJuIGZyb21CaXRzKGxvdywgd2FzbVtcImdldF9oaWdoXCJdKCksIHRoaXMudW5zaWduZWQpO1xuICAgIH1cbiAgICBpZiAodGhpcy5pc1plcm8oKSkgcmV0dXJuIHRoaXMudW5zaWduZWQgPyBVWkVSTyA6IFpFUk87XG4gICAgdmFyIGFwcHJveCwgcmVtLCByZXM7XG4gICAgaWYgKCF0aGlzLnVuc2lnbmVkKSB7XG4gICAgICAvLyBUaGlzIHNlY3Rpb24gaXMgb25seSByZWxldmFudCBmb3Igc2lnbmVkIGxvbmdzIGFuZCBpcyBkZXJpdmVkIGZyb20gdGhlXG4gICAgICAvLyBjbG9zdXJlIGxpYnJhcnkgYXMgYSB3aG9sZS5cbiAgICAgIGlmICh0aGlzLmVxKE1JTl9WQUxVRSkpIHtcbiAgICAgICAgaWYgKGRpdmlzb3IuZXEoT05FKSB8fCBkaXZpc29yLmVxKE5FR19PTkUpKSByZXR1cm4gTUlOX1ZBTFVFOyAvLyByZWNhbGwgdGhhdCAtTUlOX1ZBTFVFID09IE1JTl9WQUxVRVxuICAgICAgICBlbHNlIGlmIChkaXZpc29yLmVxKE1JTl9WQUxVRSkpIHJldHVybiBPTkU7ZWxzZSB7XG4gICAgICAgICAgLy8gQXQgdGhpcyBwb2ludCwgd2UgaGF2ZSB8b3RoZXJ8ID49IDIsIHNvIHx0aGlzL290aGVyfCA8IHxNSU5fVkFMVUV8LlxuICAgICAgICAgIHZhciBoYWxmVGhpcyA9IHRoaXMuc2hyKDEpO1xuICAgICAgICAgIGFwcHJveCA9IGhhbGZUaGlzLmRpdihkaXZpc29yKS5zaGwoMSk7XG4gICAgICAgICAgaWYgKGFwcHJveC5lcShaRVJPKSkge1xuICAgICAgICAgICAgcmV0dXJuIGRpdmlzb3IuaXNOZWdhdGl2ZSgpID8gT05FIDogTkVHX09ORTtcbiAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgcmVtID0gdGhpcy5zdWIoZGl2aXNvci5tdWwoYXBwcm94KSk7XG4gICAgICAgICAgICByZXMgPSBhcHByb3guYWRkKHJlbS5kaXYoZGl2aXNvcikpO1xuICAgICAgICAgICAgcmV0dXJuIHJlcztcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSBpZiAoZGl2aXNvci5lcShNSU5fVkFMVUUpKSByZXR1cm4gdGhpcy51bnNpZ25lZCA/IFVaRVJPIDogWkVSTztcbiAgICAgIGlmICh0aGlzLmlzTmVnYXRpdmUoKSkge1xuICAgICAgICBpZiAoZGl2aXNvci5pc05lZ2F0aXZlKCkpIHJldHVybiB0aGlzLm5lZygpLmRpdihkaXZpc29yLm5lZygpKTtcbiAgICAgICAgcmV0dXJuIHRoaXMubmVnKCkuZGl2KGRpdmlzb3IpLm5lZygpO1xuICAgICAgfSBlbHNlIGlmIChkaXZpc29yLmlzTmVnYXRpdmUoKSkgcmV0dXJuIHRoaXMuZGl2KGRpdmlzb3IubmVnKCkpLm5lZygpO1xuICAgICAgcmVzID0gWkVSTztcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gVGhlIGFsZ29yaXRobSBiZWxvdyBoYXMgbm90IGJlZW4gbWFkZSBmb3IgdW5zaWduZWQgbG9uZ3MuIEl0J3MgdGhlcmVmb3JlXG4gICAgICAvLyByZXF1aXJlZCB0byB0YWtlIHNwZWNpYWwgY2FyZSBvZiB0aGUgTVNCIHByaW9yIHRvIHJ1bm5pbmcgaXQuXG4gICAgICBpZiAoIWRpdmlzb3IudW5zaWduZWQpIGRpdmlzb3IgPSBkaXZpc29yLnRvVW5zaWduZWQoKTtcbiAgICAgIGlmIChkaXZpc29yLmd0KHRoaXMpKSByZXR1cm4gVVpFUk87XG4gICAgICBpZiAoZGl2aXNvci5ndCh0aGlzLnNocnUoMSkpKVxuICAgICAgICAvLyAxNSA+Pj4gMSA9IDcgOyB3aXRoIGRpdmlzb3IgPSA4IDsgdHJ1ZVxuICAgICAgICByZXR1cm4gVU9ORTtcbiAgICAgIHJlcyA9IFVaRVJPO1xuICAgIH1cbiAgXG4gICAgLy8gUmVwZWF0IHRoZSBmb2xsb3dpbmcgdW50aWwgdGhlIHJlbWFpbmRlciBpcyBsZXNzIHRoYW4gb3RoZXI6ICBmaW5kIGFcbiAgICAvLyBmbG9hdGluZy1wb2ludCB0aGF0IGFwcHJveGltYXRlcyByZW1haW5kZXIgLyBvdGhlciAqZnJvbSBiZWxvdyosIGFkZCB0aGlzXG4gICAgLy8gaW50byB0aGUgcmVzdWx0LCBhbmQgc3VidHJhY3QgaXQgZnJvbSB0aGUgcmVtYWluZGVyLiAgSXQgaXMgY3JpdGljYWwgdGhhdFxuICAgIC8vIHRoZSBhcHByb3hpbWF0ZSB2YWx1ZSBpcyBsZXNzIHRoYW4gb3IgZXF1YWwgdG8gdGhlIHJlYWwgdmFsdWUgc28gdGhhdCB0aGVcbiAgICAvLyByZW1haW5kZXIgbmV2ZXIgYmVjb21lcyBuZWdhdGl2ZS5cbiAgICByZW0gPSB0aGlzO1xuICAgIHdoaWxlIChyZW0uZ3RlKGRpdmlzb3IpKSB7XG4gICAgICAvLyBBcHByb3hpbWF0ZSB0aGUgcmVzdWx0IG9mIGRpdmlzaW9uLiBUaGlzIG1heSBiZSBhIGxpdHRsZSBncmVhdGVyIG9yXG4gICAgICAvLyBzbWFsbGVyIHRoYW4gdGhlIGFjdHVhbCB2YWx1ZS5cbiAgICAgIGFwcHJveCA9IE1hdGgubWF4KDEsIE1hdGguZmxvb3IocmVtLnRvTnVtYmVyKCkgLyBkaXZpc29yLnRvTnVtYmVyKCkpKTtcbiAgXG4gICAgICAvLyBXZSB3aWxsIHR3ZWFrIHRoZSBhcHByb3hpbWF0ZSByZXN1bHQgYnkgY2hhbmdpbmcgaXQgaW4gdGhlIDQ4LXRoIGRpZ2l0IG9yXG4gICAgICAvLyB0aGUgc21hbGxlc3Qgbm9uLWZyYWN0aW9uYWwgZGlnaXQsIHdoaWNoZXZlciBpcyBsYXJnZXIuXG4gICAgICB2YXIgbG9nMiA9IE1hdGguY2VpbChNYXRoLmxvZyhhcHByb3gpIC8gTWF0aC5MTjIpLFxuICAgICAgICBkZWx0YSA9IGxvZzIgPD0gNDggPyAxIDogcG93X2RibCgyLCBsb2cyIC0gNDgpLFxuICAgICAgICAvLyBEZWNyZWFzZSB0aGUgYXBwcm94aW1hdGlvbiB1bnRpbCBpdCBpcyBzbWFsbGVyIHRoYW4gdGhlIHJlbWFpbmRlci4gIE5vdGVcbiAgICAgICAgLy8gdGhhdCBpZiBpdCBpcyB0b28gbGFyZ2UsIHRoZSBwcm9kdWN0IG92ZXJmbG93cyBhbmQgaXMgbmVnYXRpdmUuXG4gICAgICAgIGFwcHJveFJlcyA9IGZyb21OdW1iZXIoYXBwcm94KSxcbiAgICAgICAgYXBwcm94UmVtID0gYXBwcm94UmVzLm11bChkaXZpc29yKTtcbiAgICAgIHdoaWxlIChhcHByb3hSZW0uaXNOZWdhdGl2ZSgpIHx8IGFwcHJveFJlbS5ndChyZW0pKSB7XG4gICAgICAgIGFwcHJveCAtPSBkZWx0YTtcbiAgICAgICAgYXBwcm94UmVzID0gZnJvbU51bWJlcihhcHByb3gsIHRoaXMudW5zaWduZWQpO1xuICAgICAgICBhcHByb3hSZW0gPSBhcHByb3hSZXMubXVsKGRpdmlzb3IpO1xuICAgICAgfVxuICBcbiAgICAgIC8vIFdlIGtub3cgdGhlIGFuc3dlciBjYW4ndCBiZSB6ZXJvLi4uIGFuZCBhY3R1YWxseSwgemVybyB3b3VsZCBjYXVzZVxuICAgICAgLy8gaW5maW5pdGUgcmVjdXJzaW9uIHNpbmNlIHdlIHdvdWxkIG1ha2Ugbm8gcHJvZ3Jlc3MuXG4gICAgICBpZiAoYXBwcm94UmVzLmlzWmVybygpKSBhcHByb3hSZXMgPSBPTkU7XG4gICAgICByZXMgPSByZXMuYWRkKGFwcHJveFJlcyk7XG4gICAgICByZW0gPSByZW0uc3ViKGFwcHJveFJlbSk7XG4gICAgfVxuICAgIHJldHVybiByZXM7XG4gIH07XG4gIFxuICAvKipcbiAgICogUmV0dXJucyB0aGlzIExvbmcgZGl2aWRlZCBieSB0aGUgc3BlY2lmaWVkLiBUaGlzIGlzIGFuIGFsaWFzIG9mIHtAbGluayBMb25nI2RpdmlkZX0uXG4gICAqIEBmdW5jdGlvblxuICAgKiBAcGFyYW0geyFMb25nfG51bWJlcnxiaWdpbnR8c3RyaW5nfSBkaXZpc29yIERpdmlzb3JcbiAgICogQHJldHVybnMgeyFMb25nfSBRdW90aWVudFxuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5kaXYgPSBMb25nUHJvdG90eXBlLmRpdmlkZTtcbiAgXG4gIC8qKlxuICAgKiBSZXR1cm5zIHRoaXMgTG9uZyBtb2R1bG8gdGhlIHNwZWNpZmllZC5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcGFyYW0geyFMb25nfG51bWJlcnxiaWdpbnR8c3RyaW5nfSBkaXZpc29yIERpdmlzb3JcbiAgICogQHJldHVybnMgeyFMb25nfSBSZW1haW5kZXJcbiAgICovXG4gIExvbmdQcm90b3R5cGUubW9kdWxvID0gZnVuY3Rpb24gbW9kdWxvKGRpdmlzb3IpIHtcbiAgICBpZiAoIWlzTG9uZyhkaXZpc29yKSkgZGl2aXNvciA9IGZyb21WYWx1ZShkaXZpc29yKTtcbiAgXG4gICAgLy8gdXNlIHdhc20gc3VwcG9ydCBpZiBwcmVzZW50XG4gICAgaWYgKHdhc20pIHtcbiAgICAgIHZhciBsb3cgPSAodGhpcy51bnNpZ25lZCA/IHdhc21bXCJyZW1fdVwiXSA6IHdhc21bXCJyZW1fc1wiXSkodGhpcy5sb3csIHRoaXMuaGlnaCwgZGl2aXNvci5sb3csIGRpdmlzb3IuaGlnaCk7XG4gICAgICByZXR1cm4gZnJvbUJpdHMobG93LCB3YXNtW1wiZ2V0X2hpZ2hcIl0oKSwgdGhpcy51bnNpZ25lZCk7XG4gICAgfVxuICAgIHJldHVybiB0aGlzLnN1Yih0aGlzLmRpdihkaXZpc29yKS5tdWwoZGl2aXNvcikpO1xuICB9O1xuICBcbiAgLyoqXG4gICAqIFJldHVybnMgdGhpcyBMb25nIG1vZHVsbyB0aGUgc3BlY2lmaWVkLiBUaGlzIGlzIGFuIGFsaWFzIG9mIHtAbGluayBMb25nI21vZHVsb30uXG4gICAqIEBmdW5jdGlvblxuICAgKiBAcGFyYW0geyFMb25nfG51bWJlcnxiaWdpbnR8c3RyaW5nfSBkaXZpc29yIERpdmlzb3JcbiAgICogQHJldHVybnMgeyFMb25nfSBSZW1haW5kZXJcbiAgICovXG4gIExvbmdQcm90b3R5cGUubW9kID0gTG9uZ1Byb3RvdHlwZS5tb2R1bG87XG4gIFxuICAvKipcbiAgICogUmV0dXJucyB0aGlzIExvbmcgbW9kdWxvIHRoZSBzcGVjaWZpZWQuIFRoaXMgaXMgYW4gYWxpYXMgb2Yge0BsaW5rIExvbmcjbW9kdWxvfS5cbiAgICogQGZ1bmN0aW9uXG4gICAqIEBwYXJhbSB7IUxvbmd8bnVtYmVyfGJpZ2ludHxzdHJpbmd9IGRpdmlzb3IgRGl2aXNvclxuICAgKiBAcmV0dXJucyB7IUxvbmd9IFJlbWFpbmRlclxuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5yZW0gPSBMb25nUHJvdG90eXBlLm1vZHVsbztcbiAgXG4gIC8qKlxuICAgKiBSZXR1cm5zIHRoZSBiaXR3aXNlIE5PVCBvZiB0aGlzIExvbmcuXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHJldHVybnMgeyFMb25nfVxuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5ub3QgPSBmdW5jdGlvbiBub3QoKSB7XG4gICAgcmV0dXJuIGZyb21CaXRzKH50aGlzLmxvdywgfnRoaXMuaGlnaCwgdGhpcy51bnNpZ25lZCk7XG4gIH07XG4gIFxuICAvKipcbiAgICogUmV0dXJucyBjb3VudCBsZWFkaW5nIHplcm9zIG9mIHRoaXMgTG9uZy5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcmV0dXJucyB7IW51bWJlcn1cbiAgICovXG4gIExvbmdQcm90b3R5cGUuY291bnRMZWFkaW5nWmVyb3MgPSBmdW5jdGlvbiBjb3VudExlYWRpbmdaZXJvcygpIHtcbiAgICByZXR1cm4gdGhpcy5oaWdoID8gTWF0aC5jbHozMih0aGlzLmhpZ2gpIDogTWF0aC5jbHozMih0aGlzLmxvdykgKyAzMjtcbiAgfTtcbiAgXG4gIC8qKlxuICAgKiBSZXR1cm5zIGNvdW50IGxlYWRpbmcgemVyb3MuIFRoaXMgaXMgYW4gYWxpYXMgb2Yge0BsaW5rIExvbmcjY291bnRMZWFkaW5nWmVyb3N9LlxuICAgKiBAZnVuY3Rpb25cbiAgICogQHBhcmFtIHshTG9uZ31cbiAgICogQHJldHVybnMgeyFudW1iZXJ9XG4gICAqL1xuICBMb25nUHJvdG90eXBlLmNseiA9IExvbmdQcm90b3R5cGUuY291bnRMZWFkaW5nWmVyb3M7XG4gIFxuICAvKipcbiAgICogUmV0dXJucyBjb3VudCB0cmFpbGluZyB6ZXJvcyBvZiB0aGlzIExvbmcuXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHJldHVybnMgeyFudW1iZXJ9XG4gICAqL1xuICBMb25nUHJvdG90eXBlLmNvdW50VHJhaWxpbmdaZXJvcyA9IGZ1bmN0aW9uIGNvdW50VHJhaWxpbmdaZXJvcygpIHtcbiAgICByZXR1cm4gdGhpcy5sb3cgPyBjdHozMih0aGlzLmxvdykgOiBjdHozMih0aGlzLmhpZ2gpICsgMzI7XG4gIH07XG4gIFxuICAvKipcbiAgICogUmV0dXJucyBjb3VudCB0cmFpbGluZyB6ZXJvcy4gVGhpcyBpcyBhbiBhbGlhcyBvZiB7QGxpbmsgTG9uZyNjb3VudFRyYWlsaW5nWmVyb3N9LlxuICAgKiBAZnVuY3Rpb25cbiAgICogQHBhcmFtIHshTG9uZ31cbiAgICogQHJldHVybnMgeyFudW1iZXJ9XG4gICAqL1xuICBMb25nUHJvdG90eXBlLmN0eiA9IExvbmdQcm90b3R5cGUuY291bnRUcmFpbGluZ1plcm9zO1xuICBcbiAgLyoqXG4gICAqIFJldHVybnMgdGhlIGJpdHdpc2UgQU5EIG9mIHRoaXMgTG9uZyBhbmQgdGhlIHNwZWNpZmllZC5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcGFyYW0geyFMb25nfG51bWJlcnxiaWdpbnR8c3RyaW5nfSBvdGhlciBPdGhlciBMb25nXG4gICAqIEByZXR1cm5zIHshTG9uZ31cbiAgICovXG4gIExvbmdQcm90b3R5cGUuYW5kID0gZnVuY3Rpb24gYW5kKG90aGVyKSB7XG4gICAgaWYgKCFpc0xvbmcob3RoZXIpKSBvdGhlciA9IGZyb21WYWx1ZShvdGhlcik7XG4gICAgcmV0dXJuIGZyb21CaXRzKHRoaXMubG93ICYgb3RoZXIubG93LCB0aGlzLmhpZ2ggJiBvdGhlci5oaWdoLCB0aGlzLnVuc2lnbmVkKTtcbiAgfTtcbiAgXG4gIC8qKlxuICAgKiBSZXR1cm5zIHRoZSBiaXR3aXNlIE9SIG9mIHRoaXMgTG9uZyBhbmQgdGhlIHNwZWNpZmllZC5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcGFyYW0geyFMb25nfG51bWJlcnxiaWdpbnR8c3RyaW5nfSBvdGhlciBPdGhlciBMb25nXG4gICAqIEByZXR1cm5zIHshTG9uZ31cbiAgICovXG4gIExvbmdQcm90b3R5cGUub3IgPSBmdW5jdGlvbiBvcihvdGhlcikge1xuICAgIGlmICghaXNMb25nKG90aGVyKSkgb3RoZXIgPSBmcm9tVmFsdWUob3RoZXIpO1xuICAgIHJldHVybiBmcm9tQml0cyh0aGlzLmxvdyB8IG90aGVyLmxvdywgdGhpcy5oaWdoIHwgb3RoZXIuaGlnaCwgdGhpcy51bnNpZ25lZCk7XG4gIH07XG4gIFxuICAvKipcbiAgICogUmV0dXJucyB0aGUgYml0d2lzZSBYT1Igb2YgdGhpcyBMb25nIGFuZCB0aGUgZ2l2ZW4gb25lLlxuICAgKiBAdGhpcyB7IUxvbmd9XG4gICAqIEBwYXJhbSB7IUxvbmd8bnVtYmVyfGJpZ2ludHxzdHJpbmd9IG90aGVyIE90aGVyIExvbmdcbiAgICogQHJldHVybnMgeyFMb25nfVxuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS54b3IgPSBmdW5jdGlvbiB4b3Iob3RoZXIpIHtcbiAgICBpZiAoIWlzTG9uZyhvdGhlcikpIG90aGVyID0gZnJvbVZhbHVlKG90aGVyKTtcbiAgICByZXR1cm4gZnJvbUJpdHModGhpcy5sb3cgXiBvdGhlci5sb3csIHRoaXMuaGlnaCBeIG90aGVyLmhpZ2gsIHRoaXMudW5zaWduZWQpO1xuICB9O1xuICBcbiAgLyoqXG4gICAqIFJldHVybnMgdGhpcyBMb25nIHdpdGggYml0cyBzaGlmdGVkIHRvIHRoZSBsZWZ0IGJ5IHRoZSBnaXZlbiBhbW91bnQuXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHBhcmFtIHtudW1iZXJ8IUxvbmd9IG51bUJpdHMgTnVtYmVyIG9mIGJpdHNcbiAgICogQHJldHVybnMgeyFMb25nfSBTaGlmdGVkIExvbmdcbiAgICovXG4gIExvbmdQcm90b3R5cGUuc2hpZnRMZWZ0ID0gZnVuY3Rpb24gc2hpZnRMZWZ0KG51bUJpdHMpIHtcbiAgICBpZiAoaXNMb25nKG51bUJpdHMpKSBudW1CaXRzID0gbnVtQml0cy50b0ludCgpO1xuICAgIGlmICgobnVtQml0cyAmPSA2MykgPT09IDApIHJldHVybiB0aGlzO2Vsc2UgaWYgKG51bUJpdHMgPCAzMikgcmV0dXJuIGZyb21CaXRzKHRoaXMubG93IDw8IG51bUJpdHMsIHRoaXMuaGlnaCA8PCBudW1CaXRzIHwgdGhpcy5sb3cgPj4+IDMyIC0gbnVtQml0cywgdGhpcy51bnNpZ25lZCk7ZWxzZSByZXR1cm4gZnJvbUJpdHMoMCwgdGhpcy5sb3cgPDwgbnVtQml0cyAtIDMyLCB0aGlzLnVuc2lnbmVkKTtcbiAgfTtcbiAgXG4gIC8qKlxuICAgKiBSZXR1cm5zIHRoaXMgTG9uZyB3aXRoIGJpdHMgc2hpZnRlZCB0byB0aGUgbGVmdCBieSB0aGUgZ2l2ZW4gYW1vdW50LiBUaGlzIGlzIGFuIGFsaWFzIG9mIHtAbGluayBMb25nI3NoaWZ0TGVmdH0uXG4gICAqIEBmdW5jdGlvblxuICAgKiBAcGFyYW0ge251bWJlcnwhTG9uZ30gbnVtQml0cyBOdW1iZXIgb2YgYml0c1xuICAgKiBAcmV0dXJucyB7IUxvbmd9IFNoaWZ0ZWQgTG9uZ1xuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5zaGwgPSBMb25nUHJvdG90eXBlLnNoaWZ0TGVmdDtcbiAgXG4gIC8qKlxuICAgKiBSZXR1cm5zIHRoaXMgTG9uZyB3aXRoIGJpdHMgYXJpdGhtZXRpY2FsbHkgc2hpZnRlZCB0byB0aGUgcmlnaHQgYnkgdGhlIGdpdmVuIGFtb3VudC5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcGFyYW0ge251bWJlcnwhTG9uZ30gbnVtQml0cyBOdW1iZXIgb2YgYml0c1xuICAgKiBAcmV0dXJucyB7IUxvbmd9IFNoaWZ0ZWQgTG9uZ1xuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5zaGlmdFJpZ2h0ID0gZnVuY3Rpb24gc2hpZnRSaWdodChudW1CaXRzKSB7XG4gICAgaWYgKGlzTG9uZyhudW1CaXRzKSkgbnVtQml0cyA9IG51bUJpdHMudG9JbnQoKTtcbiAgICBpZiAoKG51bUJpdHMgJj0gNjMpID09PSAwKSByZXR1cm4gdGhpcztlbHNlIGlmIChudW1CaXRzIDwgMzIpIHJldHVybiBmcm9tQml0cyh0aGlzLmxvdyA+Pj4gbnVtQml0cyB8IHRoaXMuaGlnaCA8PCAzMiAtIG51bUJpdHMsIHRoaXMuaGlnaCA+PiBudW1CaXRzLCB0aGlzLnVuc2lnbmVkKTtlbHNlIHJldHVybiBmcm9tQml0cyh0aGlzLmhpZ2ggPj4gbnVtQml0cyAtIDMyLCB0aGlzLmhpZ2ggPj0gMCA/IDAgOiAtMSwgdGhpcy51bnNpZ25lZCk7XG4gIH07XG4gIFxuICAvKipcbiAgICogUmV0dXJucyB0aGlzIExvbmcgd2l0aCBiaXRzIGFyaXRobWV0aWNhbGx5IHNoaWZ0ZWQgdG8gdGhlIHJpZ2h0IGJ5IHRoZSBnaXZlbiBhbW91bnQuIFRoaXMgaXMgYW4gYWxpYXMgb2Yge0BsaW5rIExvbmcjc2hpZnRSaWdodH0uXG4gICAqIEBmdW5jdGlvblxuICAgKiBAcGFyYW0ge251bWJlcnwhTG9uZ30gbnVtQml0cyBOdW1iZXIgb2YgYml0c1xuICAgKiBAcmV0dXJucyB7IUxvbmd9IFNoaWZ0ZWQgTG9uZ1xuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5zaHIgPSBMb25nUHJvdG90eXBlLnNoaWZ0UmlnaHQ7XG4gIFxuICAvKipcbiAgICogUmV0dXJucyB0aGlzIExvbmcgd2l0aCBiaXRzIGxvZ2ljYWxseSBzaGlmdGVkIHRvIHRoZSByaWdodCBieSB0aGUgZ2l2ZW4gYW1vdW50LlxuICAgKiBAdGhpcyB7IUxvbmd9XG4gICAqIEBwYXJhbSB7bnVtYmVyfCFMb25nfSBudW1CaXRzIE51bWJlciBvZiBiaXRzXG4gICAqIEByZXR1cm5zIHshTG9uZ30gU2hpZnRlZCBMb25nXG4gICAqL1xuICBMb25nUHJvdG90eXBlLnNoaWZ0UmlnaHRVbnNpZ25lZCA9IGZ1bmN0aW9uIHNoaWZ0UmlnaHRVbnNpZ25lZChudW1CaXRzKSB7XG4gICAgaWYgKGlzTG9uZyhudW1CaXRzKSkgbnVtQml0cyA9IG51bUJpdHMudG9JbnQoKTtcbiAgICBpZiAoKG51bUJpdHMgJj0gNjMpID09PSAwKSByZXR1cm4gdGhpcztcbiAgICBpZiAobnVtQml0cyA8IDMyKSByZXR1cm4gZnJvbUJpdHModGhpcy5sb3cgPj4+IG51bUJpdHMgfCB0aGlzLmhpZ2ggPDwgMzIgLSBudW1CaXRzLCB0aGlzLmhpZ2ggPj4+IG51bUJpdHMsIHRoaXMudW5zaWduZWQpO1xuICAgIGlmIChudW1CaXRzID09PSAzMikgcmV0dXJuIGZyb21CaXRzKHRoaXMuaGlnaCwgMCwgdGhpcy51bnNpZ25lZCk7XG4gICAgcmV0dXJuIGZyb21CaXRzKHRoaXMuaGlnaCA+Pj4gbnVtQml0cyAtIDMyLCAwLCB0aGlzLnVuc2lnbmVkKTtcbiAgfTtcbiAgXG4gIC8qKlxuICAgKiBSZXR1cm5zIHRoaXMgTG9uZyB3aXRoIGJpdHMgbG9naWNhbGx5IHNoaWZ0ZWQgdG8gdGhlIHJpZ2h0IGJ5IHRoZSBnaXZlbiBhbW91bnQuIFRoaXMgaXMgYW4gYWxpYXMgb2Yge0BsaW5rIExvbmcjc2hpZnRSaWdodFVuc2lnbmVkfS5cbiAgICogQGZ1bmN0aW9uXG4gICAqIEBwYXJhbSB7bnVtYmVyfCFMb25nfSBudW1CaXRzIE51bWJlciBvZiBiaXRzXG4gICAqIEByZXR1cm5zIHshTG9uZ30gU2hpZnRlZCBMb25nXG4gICAqL1xuICBMb25nUHJvdG90eXBlLnNocnUgPSBMb25nUHJvdG90eXBlLnNoaWZ0UmlnaHRVbnNpZ25lZDtcbiAgXG4gIC8qKlxuICAgKiBSZXR1cm5zIHRoaXMgTG9uZyB3aXRoIGJpdHMgbG9naWNhbGx5IHNoaWZ0ZWQgdG8gdGhlIHJpZ2h0IGJ5IHRoZSBnaXZlbiBhbW91bnQuIFRoaXMgaXMgYW4gYWxpYXMgb2Yge0BsaW5rIExvbmcjc2hpZnRSaWdodFVuc2lnbmVkfS5cbiAgICogQGZ1bmN0aW9uXG4gICAqIEBwYXJhbSB7bnVtYmVyfCFMb25nfSBudW1CaXRzIE51bWJlciBvZiBiaXRzXG4gICAqIEByZXR1cm5zIHshTG9uZ30gU2hpZnRlZCBMb25nXG4gICAqL1xuICBMb25nUHJvdG90eXBlLnNocl91ID0gTG9uZ1Byb3RvdHlwZS5zaGlmdFJpZ2h0VW5zaWduZWQ7XG4gIFxuICAvKipcbiAgICogUmV0dXJucyB0aGlzIExvbmcgd2l0aCBiaXRzIHJvdGF0ZWQgdG8gdGhlIGxlZnQgYnkgdGhlIGdpdmVuIGFtb3VudC5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcGFyYW0ge251bWJlcnwhTG9uZ30gbnVtQml0cyBOdW1iZXIgb2YgYml0c1xuICAgKiBAcmV0dXJucyB7IUxvbmd9IFJvdGF0ZWQgTG9uZ1xuICAgKi9cbiAgTG9uZ1Byb3RvdHlwZS5yb3RhdGVMZWZ0ID0gZnVuY3Rpb24gcm90YXRlTGVmdChudW1CaXRzKSB7XG4gICAgdmFyIGI7XG4gICAgaWYgKGlzTG9uZyhudW1CaXRzKSkgbnVtQml0cyA9IG51bUJpdHMudG9JbnQoKTtcbiAgICBpZiAoKG51bUJpdHMgJj0gNjMpID09PSAwKSByZXR1cm4gdGhpcztcbiAgICBpZiAobnVtQml0cyA9PT0gMzIpIHJldHVybiBmcm9tQml0cyh0aGlzLmhpZ2gsIHRoaXMubG93LCB0aGlzLnVuc2lnbmVkKTtcbiAgICBpZiAobnVtQml0cyA8IDMyKSB7XG4gICAgICBiID0gMzIgLSBudW1CaXRzO1xuICAgICAgcmV0dXJuIGZyb21CaXRzKHRoaXMubG93IDw8IG51bUJpdHMgfCB0aGlzLmhpZ2ggPj4+IGIsIHRoaXMuaGlnaCA8PCBudW1CaXRzIHwgdGhpcy5sb3cgPj4+IGIsIHRoaXMudW5zaWduZWQpO1xuICAgIH1cbiAgICBudW1CaXRzIC09IDMyO1xuICAgIGIgPSAzMiAtIG51bUJpdHM7XG4gICAgcmV0dXJuIGZyb21CaXRzKHRoaXMuaGlnaCA8PCBudW1CaXRzIHwgdGhpcy5sb3cgPj4+IGIsIHRoaXMubG93IDw8IG51bUJpdHMgfCB0aGlzLmhpZ2ggPj4+IGIsIHRoaXMudW5zaWduZWQpO1xuICB9O1xuICAvKipcbiAgICogUmV0dXJucyB0aGlzIExvbmcgd2l0aCBiaXRzIHJvdGF0ZWQgdG8gdGhlIGxlZnQgYnkgdGhlIGdpdmVuIGFtb3VudC4gVGhpcyBpcyBhbiBhbGlhcyBvZiB7QGxpbmsgTG9uZyNyb3RhdGVMZWZ0fS5cbiAgICogQGZ1bmN0aW9uXG4gICAqIEBwYXJhbSB7bnVtYmVyfCFMb25nfSBudW1CaXRzIE51bWJlciBvZiBiaXRzXG4gICAqIEByZXR1cm5zIHshTG9uZ30gUm90YXRlZCBMb25nXG4gICAqL1xuICBMb25nUHJvdG90eXBlLnJvdGwgPSBMb25nUHJvdG90eXBlLnJvdGF0ZUxlZnQ7XG4gIFxuICAvKipcbiAgICogUmV0dXJucyB0aGlzIExvbmcgd2l0aCBiaXRzIHJvdGF0ZWQgdG8gdGhlIHJpZ2h0IGJ5IHRoZSBnaXZlbiBhbW91bnQuXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHBhcmFtIHtudW1iZXJ8IUxvbmd9IG51bUJpdHMgTnVtYmVyIG9mIGJpdHNcbiAgICogQHJldHVybnMgeyFMb25nfSBSb3RhdGVkIExvbmdcbiAgICovXG4gIExvbmdQcm90b3R5cGUucm90YXRlUmlnaHQgPSBmdW5jdGlvbiByb3RhdGVSaWdodChudW1CaXRzKSB7XG4gICAgdmFyIGI7XG4gICAgaWYgKGlzTG9uZyhudW1CaXRzKSkgbnVtQml0cyA9IG51bUJpdHMudG9JbnQoKTtcbiAgICBpZiAoKG51bUJpdHMgJj0gNjMpID09PSAwKSByZXR1cm4gdGhpcztcbiAgICBpZiAobnVtQml0cyA9PT0gMzIpIHJldHVybiBmcm9tQml0cyh0aGlzLmhpZ2gsIHRoaXMubG93LCB0aGlzLnVuc2lnbmVkKTtcbiAgICBpZiAobnVtQml0cyA8IDMyKSB7XG4gICAgICBiID0gMzIgLSBudW1CaXRzO1xuICAgICAgcmV0dXJuIGZyb21CaXRzKHRoaXMuaGlnaCA8PCBiIHwgdGhpcy5sb3cgPj4+IG51bUJpdHMsIHRoaXMubG93IDw8IGIgfCB0aGlzLmhpZ2ggPj4+IG51bUJpdHMsIHRoaXMudW5zaWduZWQpO1xuICAgIH1cbiAgICBudW1CaXRzIC09IDMyO1xuICAgIGIgPSAzMiAtIG51bUJpdHM7XG4gICAgcmV0dXJuIGZyb21CaXRzKHRoaXMubG93IDw8IGIgfCB0aGlzLmhpZ2ggPj4+IG51bUJpdHMsIHRoaXMuaGlnaCA8PCBiIHwgdGhpcy5sb3cgPj4+IG51bUJpdHMsIHRoaXMudW5zaWduZWQpO1xuICB9O1xuICAvKipcbiAgICogUmV0dXJucyB0aGlzIExvbmcgd2l0aCBiaXRzIHJvdGF0ZWQgdG8gdGhlIHJpZ2h0IGJ5IHRoZSBnaXZlbiBhbW91bnQuIFRoaXMgaXMgYW4gYWxpYXMgb2Yge0BsaW5rIExvbmcjcm90YXRlUmlnaHR9LlxuICAgKiBAZnVuY3Rpb25cbiAgICogQHBhcmFtIHtudW1iZXJ8IUxvbmd9IG51bUJpdHMgTnVtYmVyIG9mIGJpdHNcbiAgICogQHJldHVybnMgeyFMb25nfSBSb3RhdGVkIExvbmdcbiAgICovXG4gIExvbmdQcm90b3R5cGUucm90ciA9IExvbmdQcm90b3R5cGUucm90YXRlUmlnaHQ7XG4gIFxuICAvKipcbiAgICogQ29udmVydHMgdGhpcyBMb25nIHRvIHNpZ25lZC5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcmV0dXJucyB7IUxvbmd9IFNpZ25lZCBsb25nXG4gICAqL1xuICBMb25nUHJvdG90eXBlLnRvU2lnbmVkID0gZnVuY3Rpb24gdG9TaWduZWQoKSB7XG4gICAgaWYgKCF0aGlzLnVuc2lnbmVkKSByZXR1cm4gdGhpcztcbiAgICByZXR1cm4gZnJvbUJpdHModGhpcy5sb3csIHRoaXMuaGlnaCwgZmFsc2UpO1xuICB9O1xuICBcbiAgLyoqXG4gICAqIENvbnZlcnRzIHRoaXMgTG9uZyB0byB1bnNpZ25lZC5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcmV0dXJucyB7IUxvbmd9IFVuc2lnbmVkIGxvbmdcbiAgICovXG4gIExvbmdQcm90b3R5cGUudG9VbnNpZ25lZCA9IGZ1bmN0aW9uIHRvVW5zaWduZWQoKSB7XG4gICAgaWYgKHRoaXMudW5zaWduZWQpIHJldHVybiB0aGlzO1xuICAgIHJldHVybiBmcm9tQml0cyh0aGlzLmxvdywgdGhpcy5oaWdoLCB0cnVlKTtcbiAgfTtcbiAgXG4gIC8qKlxuICAgKiBDb252ZXJ0cyB0aGlzIExvbmcgdG8gaXRzIGJ5dGUgcmVwcmVzZW50YXRpb24uXG4gICAqIEBwYXJhbSB7Ym9vbGVhbj19IGxlIFdoZXRoZXIgbGl0dGxlIG9yIGJpZyBlbmRpYW4sIGRlZmF1bHRzIHRvIGJpZyBlbmRpYW5cbiAgICogQHRoaXMgeyFMb25nfVxuICAgKiBAcmV0dXJucyB7IUFycmF5LjxudW1iZXI+fSBCeXRlIHJlcHJlc2VudGF0aW9uXG4gICAqL1xuICBMb25nUHJvdG90eXBlLnRvQnl0ZXMgPSBmdW5jdGlvbiB0b0J5dGVzKGxlKSB7XG4gICAgcmV0dXJuIGxlID8gdGhpcy50b0J5dGVzTEUoKSA6IHRoaXMudG9CeXRlc0JFKCk7XG4gIH07XG4gIFxuICAvKipcbiAgICogQ29udmVydHMgdGhpcyBMb25nIHRvIGl0cyBsaXR0bGUgZW5kaWFuIGJ5dGUgcmVwcmVzZW50YXRpb24uXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHJldHVybnMgeyFBcnJheS48bnVtYmVyPn0gTGl0dGxlIGVuZGlhbiBieXRlIHJlcHJlc2VudGF0aW9uXG4gICAqL1xuICBMb25nUHJvdG90eXBlLnRvQnl0ZXNMRSA9IGZ1bmN0aW9uIHRvQnl0ZXNMRSgpIHtcbiAgICB2YXIgaGkgPSB0aGlzLmhpZ2gsXG4gICAgICBsbyA9IHRoaXMubG93O1xuICAgIHJldHVybiBbbG8gJiAweGZmLCBsbyA+Pj4gOCAmIDB4ZmYsIGxvID4+PiAxNiAmIDB4ZmYsIGxvID4+PiAyNCwgaGkgJiAweGZmLCBoaSA+Pj4gOCAmIDB4ZmYsIGhpID4+PiAxNiAmIDB4ZmYsIGhpID4+PiAyNF07XG4gIH07XG4gIFxuICAvKipcbiAgICogQ29udmVydHMgdGhpcyBMb25nIHRvIGl0cyBiaWcgZW5kaWFuIGJ5dGUgcmVwcmVzZW50YXRpb24uXG4gICAqIEB0aGlzIHshTG9uZ31cbiAgICogQHJldHVybnMgeyFBcnJheS48bnVtYmVyPn0gQmlnIGVuZGlhbiBieXRlIHJlcHJlc2VudGF0aW9uXG4gICAqL1xuICBMb25nUHJvdG90eXBlLnRvQnl0ZXNCRSA9IGZ1bmN0aW9uIHRvQnl0ZXNCRSgpIHtcbiAgICB2YXIgaGkgPSB0aGlzLmhpZ2gsXG4gICAgICBsbyA9IHRoaXMubG93O1xuICAgIHJldHVybiBbaGkgPj4+IDI0LCBoaSA+Pj4gMTYgJiAweGZmLCBoaSA+Pj4gOCAmIDB4ZmYsIGhpICYgMHhmZiwgbG8gPj4+IDI0LCBsbyA+Pj4gMTYgJiAweGZmLCBsbyA+Pj4gOCAmIDB4ZmYsIGxvICYgMHhmZl07XG4gIH07XG4gIFxuICAvKipcbiAgICogQ3JlYXRlcyBhIExvbmcgZnJvbSBpdHMgYnl0ZSByZXByZXNlbnRhdGlvbi5cbiAgICogQHBhcmFtIHshQXJyYXkuPG51bWJlcj59IGJ5dGVzIEJ5dGUgcmVwcmVzZW50YXRpb25cbiAgICogQHBhcmFtIHtib29sZWFuPX0gdW5zaWduZWQgV2hldGhlciB1bnNpZ25lZCBvciBub3QsIGRlZmF1bHRzIHRvIHNpZ25lZFxuICAgKiBAcGFyYW0ge2Jvb2xlYW49fSBsZSBXaGV0aGVyIGxpdHRsZSBvciBiaWcgZW5kaWFuLCBkZWZhdWx0cyB0byBiaWcgZW5kaWFuXG4gICAqIEByZXR1cm5zIHtMb25nfSBUaGUgY29ycmVzcG9uZGluZyBMb25nIHZhbHVlXG4gICAqL1xuICBMb25nLmZyb21CeXRlcyA9IGZ1bmN0aW9uIGZyb21CeXRlcyhieXRlcywgdW5zaWduZWQsIGxlKSB7XG4gICAgcmV0dXJuIGxlID8gTG9uZy5mcm9tQnl0ZXNMRShieXRlcywgdW5zaWduZWQpIDogTG9uZy5mcm9tQnl0ZXNCRShieXRlcywgdW5zaWduZWQpO1xuICB9O1xuICBcbiAgLyoqXG4gICAqIENyZWF0ZXMgYSBMb25nIGZyb20gaXRzIGxpdHRsZSBlbmRpYW4gYnl0ZSByZXByZXNlbnRhdGlvbi5cbiAgICogQHBhcmFtIHshQXJyYXkuPG51bWJlcj59IGJ5dGVzIExpdHRsZSBlbmRpYW4gYnl0ZSByZXByZXNlbnRhdGlvblxuICAgKiBAcGFyYW0ge2Jvb2xlYW49fSB1bnNpZ25lZCBXaGV0aGVyIHVuc2lnbmVkIG9yIG5vdCwgZGVmYXVsdHMgdG8gc2lnbmVkXG4gICAqIEByZXR1cm5zIHtMb25nfSBUaGUgY29ycmVzcG9uZGluZyBMb25nIHZhbHVlXG4gICAqL1xuICBMb25nLmZyb21CeXRlc0xFID0gZnVuY3Rpb24gZnJvbUJ5dGVzTEUoYnl0ZXMsIHVuc2lnbmVkKSB7XG4gICAgcmV0dXJuIG5ldyBMb25nKGJ5dGVzWzBdIHwgYnl0ZXNbMV0gPDwgOCB8IGJ5dGVzWzJdIDw8IDE2IHwgYnl0ZXNbM10gPDwgMjQsIGJ5dGVzWzRdIHwgYnl0ZXNbNV0gPDwgOCB8IGJ5dGVzWzZdIDw8IDE2IHwgYnl0ZXNbN10gPDwgMjQsIHVuc2lnbmVkKTtcbiAgfTtcbiAgXG4gIC8qKlxuICAgKiBDcmVhdGVzIGEgTG9uZyBmcm9tIGl0cyBiaWcgZW5kaWFuIGJ5dGUgcmVwcmVzZW50YXRpb24uXG4gICAqIEBwYXJhbSB7IUFycmF5LjxudW1iZXI+fSBieXRlcyBCaWcgZW5kaWFuIGJ5dGUgcmVwcmVzZW50YXRpb25cbiAgICogQHBhcmFtIHtib29sZWFuPX0gdW5zaWduZWQgV2hldGhlciB1bnNpZ25lZCBvciBub3QsIGRlZmF1bHRzIHRvIHNpZ25lZFxuICAgKiBAcmV0dXJucyB7TG9uZ30gVGhlIGNvcnJlc3BvbmRpbmcgTG9uZyB2YWx1ZVxuICAgKi9cbiAgTG9uZy5mcm9tQnl0ZXNCRSA9IGZ1bmN0aW9uIGZyb21CeXRlc0JFKGJ5dGVzLCB1bnNpZ25lZCkge1xuICAgIHJldHVybiBuZXcgTG9uZyhieXRlc1s0XSA8PCAyNCB8IGJ5dGVzWzVdIDw8IDE2IHwgYnl0ZXNbNl0gPDwgOCB8IGJ5dGVzWzddLCBieXRlc1swXSA8PCAyNCB8IGJ5dGVzWzFdIDw8IDE2IHwgYnl0ZXNbMl0gPDwgOCB8IGJ5dGVzWzNdLCB1bnNpZ25lZCk7XG4gIH07XG4gIFxuICAvLyBTdXBwb3J0IGNvbnZlcnNpb24gdG8vZnJvbSBCaWdJbnQgd2hlcmUgYXZhaWxhYmxlXG4gIGlmICh0eXBlb2YgQmlnSW50ID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAvKipcbiAgICAgKiBSZXR1cm5zIGEgTG9uZyByZXByZXNlbnRpbmcgdGhlIGdpdmVuIGJpZyBpbnRlZ2VyLlxuICAgICAqIEBmdW5jdGlvblxuICAgICAqIEBwYXJhbSB7bnVtYmVyfSB2YWx1ZSBUaGUgYmlnIGludGVnZXIgdmFsdWVcbiAgICAgKiBAcGFyYW0ge2Jvb2xlYW49fSB1bnNpZ25lZCBXaGV0aGVyIHVuc2lnbmVkIG9yIG5vdCwgZGVmYXVsdHMgdG8gc2lnbmVkXG4gICAgICogQHJldHVybnMgeyFMb25nfSBUaGUgY29ycmVzcG9uZGluZyBMb25nIHZhbHVlXG4gICAgICovXG4gICAgTG9uZy5mcm9tQmlnSW50ID0gZnVuY3Rpb24gZnJvbUJpZ0ludCh2YWx1ZSwgdW5zaWduZWQpIHtcbiAgICAgIHZhciBsb3dCaXRzID0gTnVtYmVyKEJpZ0ludC5hc0ludE4oMzIsIHZhbHVlKSk7XG4gICAgICB2YXIgaGlnaEJpdHMgPSBOdW1iZXIoQmlnSW50LmFzSW50TigzMiwgdmFsdWUgPj4gQmlnSW50KDMyKSkpO1xuICAgICAgcmV0dXJuIGZyb21CaXRzKGxvd0JpdHMsIGhpZ2hCaXRzLCB1bnNpZ25lZCk7XG4gICAgfTtcbiAgXG4gICAgLy8gT3ZlcnJpZGVcbiAgICBMb25nLmZyb21WYWx1ZSA9IGZ1bmN0aW9uIGZyb21WYWx1ZVdpdGhCaWdJbnQodmFsdWUsIHVuc2lnbmVkKSB7XG4gICAgICBpZiAodHlwZW9mIHZhbHVlID09PSBcImJpZ2ludFwiKSByZXR1cm4gZnJvbUJpZ0ludCh2YWx1ZSwgdW5zaWduZWQpO1xuICAgICAgcmV0dXJuIGZyb21WYWx1ZSh2YWx1ZSwgdW5zaWduZWQpO1xuICAgIH07XG4gIFxuICAgIC8qKlxuICAgICAqIENvbnZlcnRzIHRoZSBMb25nIHRvIGl0cyBiaWcgaW50ZWdlciByZXByZXNlbnRhdGlvbi5cbiAgICAgKiBAdGhpcyB7IUxvbmd9XG4gICAgICogQHJldHVybnMge2JpZ2ludH1cbiAgICAgKi9cbiAgICBMb25nUHJvdG90eXBlLnRvQmlnSW50ID0gZnVuY3Rpb24gdG9CaWdJbnQoKSB7XG4gICAgICB2YXIgbG93QmlnSW50ID0gQmlnSW50KHRoaXMubG93ID4+PiAwKTtcbiAgICAgIHZhciBoaWdoQmlnSW50ID0gQmlnSW50KHRoaXMudW5zaWduZWQgPyB0aGlzLmhpZ2ggPj4+IDAgOiB0aGlzLmhpZ2gpO1xuICAgICAgcmV0dXJuIGhpZ2hCaWdJbnQgPDwgQmlnSW50KDMyKSB8IGxvd0JpZ0ludDtcbiAgICB9O1xuICB9XG4gIHZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IExvbmc7XG4gIHJldHVybiBcImRlZmF1bHRcIiBpbiBleHBvcnRzID8gZXhwb3J0cy5kZWZhdWx0IDogZXhwb3J0cztcbn0pKHt9KTtcbmlmICh0eXBlb2YgZGVmaW5lID09PSAnZnVuY3Rpb24nICYmIGRlZmluZS5hbWQpIGRlZmluZShbXSwgZnVuY3Rpb24oKSB7IHJldHVybiBMb25nOyB9KTtcbmVsc2UgaWYgKHR5cGVvZiBtb2R1bGUgPT09ICdvYmplY3QnICYmIHR5cGVvZiBleHBvcnRzID09PSAnb2JqZWN0JykgbW9kdWxlLmV4cG9ydHMgPSBMb25nO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/long/umd/index.js\n");

/***/ })

};
;