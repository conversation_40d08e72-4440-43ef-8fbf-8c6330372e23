/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fetch-blob";
exports.ids = ["vendor-chunks/fetch-blob"];
exports.modules = {

/***/ "(action-browser)/./node_modules/fetch-blob/file.js":
/*!*****************************************!*\
  !*** ./node_modules/fetch-blob/file.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   File: () => (/* binding */ File),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(action-browser)/./node_modules/fetch-blob/index.js\");\n\n\nconst _File = class File extends _index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] {\n  #lastModified = 0\n  #name = ''\n\n  /**\n   * @param {*[]} fileBits\n   * @param {string} fileName\n   * @param {{lastModified?: number, type?: string}} options\n   */// @ts-ignore\n  constructor (fileBits, fileName, options = {}) {\n    if (arguments.length < 2) {\n      throw new TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`)\n    }\n    super(fileBits, options)\n\n    if (options === null) options = {}\n\n    // Simulate WebIDL type casting for NaN value in lastModified option.\n    const lastModified = options.lastModified === undefined ? Date.now() : Number(options.lastModified)\n    if (!Number.isNaN(lastModified)) {\n      this.#lastModified = lastModified\n    }\n\n    this.#name = String(fileName)\n  }\n\n  get name () {\n    return this.#name\n  }\n\n  get lastModified () {\n    return this.#lastModified\n  }\n\n  get [Symbol.toStringTag] () {\n    return 'File'\n  }\n\n  static [Symbol.hasInstance] (object) {\n    return !!object && object instanceof _index_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] &&\n      /^(File)$/.test(object[Symbol.toStringTag])\n  }\n}\n\n/** @type {typeof globalThis.File} */// @ts-ignore\nconst File = _File\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (File);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fetch-blob/file.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/fetch-blob/from.js":
/*!*****************************************!*\
  !*** ./node_modules/fetch-blob/from.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   File: () => (/* reexport safe */ _file_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   blobFrom: () => (/* binding */ blobFrom),\n/* harmony export */   blobFromSync: () => (/* binding */ blobFromSync),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   fileFrom: () => (/* binding */ fileFrom),\n/* harmony export */   fileFromSync: () => (/* binding */ fileFromSync)\n/* harmony export */ });\n/* harmony import */ var node_fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:fs */ \"node:fs\");\n/* harmony import */ var node_path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:path */ \"node:path\");\n/* harmony import */ var node_domexception__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node-domexception */ \"(action-browser)/./node_modules/node-domexception/index.js\");\n/* harmony import */ var _file_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./file.js */ \"(action-browser)/./node_modules/fetch-blob/file.js\");\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./index.js */ \"(action-browser)/./node_modules/fetch-blob/index.js\");\n\n\n\n\n\n\n\nconst { stat } = node_fs__WEBPACK_IMPORTED_MODULE_0__.promises\n\n/**\n * @param {string} path filepath on the disk\n * @param {string} [type] mimetype to use\n */\nconst blobFromSync = (path, type) => fromBlob((0,node_fs__WEBPACK_IMPORTED_MODULE_0__.statSync)(path), path, type)\n\n/**\n * @param {string} path filepath on the disk\n * @param {string} [type] mimetype to use\n * @returns {Promise<Blob>}\n */\nconst blobFrom = (path, type) => stat(path).then(stat => fromBlob(stat, path, type))\n\n/**\n * @param {string} path filepath on the disk\n * @param {string} [type] mimetype to use\n * @returns {Promise<File>}\n */\nconst fileFrom = (path, type) => stat(path).then(stat => fromFile(stat, path, type))\n\n/**\n * @param {string} path filepath on the disk\n * @param {string} [type] mimetype to use\n */\nconst fileFromSync = (path, type) => fromFile((0,node_fs__WEBPACK_IMPORTED_MODULE_0__.statSync)(path), path, type)\n\n// @ts-ignore\nconst fromBlob = (stat, path, type = '') => new _index_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]([new BlobDataItem({\n  path,\n  size: stat.size,\n  lastModified: stat.mtimeMs,\n  start: 0\n})], { type })\n\n// @ts-ignore\nconst fromFile = (stat, path, type = '') => new _file_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]([new BlobDataItem({\n  path,\n  size: stat.size,\n  lastModified: stat.mtimeMs,\n  start: 0\n})], (0,node_path__WEBPACK_IMPORTED_MODULE_1__.basename)(path), { type, lastModified: stat.mtimeMs })\n\n/**\n * This is a blob backed up by a file on the disk\n * with minium requirement. Its wrapped around a Blob as a blobPart\n * so you have no direct access to this.\n *\n * @private\n */\nclass BlobDataItem {\n  #path\n  #start\n\n  constructor (options) {\n    this.#path = options.path\n    this.#start = options.start\n    this.size = options.size\n    this.lastModified = options.lastModified\n  }\n\n  /**\n   * Slicing arguments is first validated and formatted\n   * to not be out of range by Blob.prototype.slice\n   */\n  slice (start, end) {\n    return new BlobDataItem({\n      path: this.#path,\n      lastModified: this.lastModified,\n      size: end - start,\n      start: this.#start + start\n    })\n  }\n\n  async * stream () {\n    const { mtimeMs } = await stat(this.#path)\n    if (mtimeMs > this.lastModified) {\n      throw new node_domexception__WEBPACK_IMPORTED_MODULE_2__('The requested file could not be read, typically due to permission problems that have occurred after a reference to a file was acquired.', 'NotReadableError')\n    }\n    yield * (0,node_fs__WEBPACK_IMPORTED_MODULE_0__.createReadStream)(this.#path, {\n      start: this.#start,\n      end: this.#start + this.size - 1\n    })\n  }\n\n  get [Symbol.toStringTag] () {\n    return 'Blob'\n  }\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (blobFromSync);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fetch-blob/from.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/fetch-blob/index.js":
/*!******************************************!*\
  !*** ./node_modules/fetch-blob/index.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Blob: () => (/* binding */ Blob),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _streams_cjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./streams.cjs */ \"(action-browser)/./node_modules/fetch-blob/streams.cjs\");\n/*! fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> */\n\n// TODO (jimmywarting): in the feature use conditional loading with top level await (requires 14.x)\n// Node has recently added whatwg stream into core\n\n\n\n// 64 KiB (same size chrome slice theirs blob into Uint8array's)\nconst POOL_SIZE = 65536\n\n/** @param {(Blob | Uint8Array)[]} parts */\nasync function * toIterator (parts, clone = true) {\n  for (const part of parts) {\n    if ('stream' in part) {\n      yield * (/** @type {AsyncIterableIterator<Uint8Array>} */ (part.stream()))\n    } else if (ArrayBuffer.isView(part)) {\n      if (clone) {\n        let position = part.byteOffset\n        const end = part.byteOffset + part.byteLength\n        while (position !== end) {\n          const size = Math.min(end - position, POOL_SIZE)\n          const chunk = part.buffer.slice(position, position + size)\n          position += chunk.byteLength\n          yield new Uint8Array(chunk)\n        }\n      } else {\n        yield part\n      }\n    /* c8 ignore next 10 */\n    } else {\n      // For blobs that have arrayBuffer but no stream method (nodes buffer.Blob)\n      let position = 0, b = (/** @type {Blob} */ (part))\n      while (position !== b.size) {\n        const chunk = b.slice(position, Math.min(b.size, position + POOL_SIZE))\n        const buffer = await chunk.arrayBuffer()\n        position += buffer.byteLength\n        yield new Uint8Array(buffer)\n      }\n    }\n  }\n}\n\nconst _Blob = class Blob {\n  /** @type {Array.<(Blob|Uint8Array)>} */\n  #parts = []\n  #type = ''\n  #size = 0\n  #endings = 'transparent'\n\n  /**\n   * The Blob() constructor returns a new Blob object. The content\n   * of the blob consists of the concatenation of the values given\n   * in the parameter array.\n   *\n   * @param {*} blobParts\n   * @param {{ type?: string, endings?: string }} [options]\n   */\n  constructor (blobParts = [], options = {}) {\n    if (typeof blobParts !== 'object' || blobParts === null) {\n      throw new TypeError('Failed to construct \\'Blob\\': The provided value cannot be converted to a sequence.')\n    }\n\n    if (typeof blobParts[Symbol.iterator] !== 'function') {\n      throw new TypeError('Failed to construct \\'Blob\\': The object must have a callable @@iterator property.')\n    }\n\n    if (typeof options !== 'object' && typeof options !== 'function') {\n      throw new TypeError('Failed to construct \\'Blob\\': parameter 2 cannot convert to dictionary.')\n    }\n\n    if (options === null) options = {}\n\n    const encoder = new TextEncoder()\n    for (const element of blobParts) {\n      let part\n      if (ArrayBuffer.isView(element)) {\n        part = new Uint8Array(element.buffer.slice(element.byteOffset, element.byteOffset + element.byteLength))\n      } else if (element instanceof ArrayBuffer) {\n        part = new Uint8Array(element.slice(0))\n      } else if (element instanceof Blob) {\n        part = element\n      } else {\n        part = encoder.encode(`${element}`)\n      }\n\n      this.#size += ArrayBuffer.isView(part) ? part.byteLength : part.size\n      this.#parts.push(part)\n    }\n\n    this.#endings = `${options.endings === undefined ? 'transparent' : options.endings}`\n    const type = options.type === undefined ? '' : String(options.type)\n    this.#type = /^[\\x20-\\x7E]*$/.test(type) ? type : ''\n  }\n\n  /**\n   * The Blob interface's size property returns the\n   * size of the Blob in bytes.\n   */\n  get size () {\n    return this.#size\n  }\n\n  /**\n   * The type property of a Blob object returns the MIME type of the file.\n   */\n  get type () {\n    return this.#type\n  }\n\n  /**\n   * The text() method in the Blob interface returns a Promise\n   * that resolves with a string containing the contents of\n   * the blob, interpreted as UTF-8.\n   *\n   * @return {Promise<string>}\n   */\n  async text () {\n    // More optimized than using this.arrayBuffer()\n    // that requires twice as much ram\n    const decoder = new TextDecoder()\n    let str = ''\n    for await (const part of toIterator(this.#parts, false)) {\n      str += decoder.decode(part, { stream: true })\n    }\n    // Remaining\n    str += decoder.decode()\n    return str\n  }\n\n  /**\n   * The arrayBuffer() method in the Blob interface returns a\n   * Promise that resolves with the contents of the blob as\n   * binary data contained in an ArrayBuffer.\n   *\n   * @return {Promise<ArrayBuffer>}\n   */\n  async arrayBuffer () {\n    // Easier way... Just a unnecessary overhead\n    // const view = new Uint8Array(this.size);\n    // await this.stream().getReader({mode: 'byob'}).read(view);\n    // return view.buffer;\n\n    const data = new Uint8Array(this.size)\n    let offset = 0\n    for await (const chunk of toIterator(this.#parts, false)) {\n      data.set(chunk, offset)\n      offset += chunk.length\n    }\n\n    return data.buffer\n  }\n\n  stream () {\n    const it = toIterator(this.#parts, true)\n\n    return new globalThis.ReadableStream({\n      // @ts-ignore\n      type: 'bytes',\n      async pull (ctrl) {\n        const chunk = await it.next()\n        chunk.done ? ctrl.close() : ctrl.enqueue(chunk.value)\n      },\n\n      async cancel () {\n        await it.return()\n      }\n    })\n  }\n\n  /**\n   * The Blob interface's slice() method creates and returns a\n   * new Blob object which contains data from a subset of the\n   * blob on which it's called.\n   *\n   * @param {number} [start]\n   * @param {number} [end]\n   * @param {string} [type]\n   */\n  slice (start = 0, end = this.size, type = '') {\n    const { size } = this\n\n    let relativeStart = start < 0 ? Math.max(size + start, 0) : Math.min(start, size)\n    let relativeEnd = end < 0 ? Math.max(size + end, 0) : Math.min(end, size)\n\n    const span = Math.max(relativeEnd - relativeStart, 0)\n    const parts = this.#parts\n    const blobParts = []\n    let added = 0\n\n    for (const part of parts) {\n      // don't add the overflow to new blobParts\n      if (added >= span) {\n        break\n      }\n\n      const size = ArrayBuffer.isView(part) ? part.byteLength : part.size\n      if (relativeStart && size <= relativeStart) {\n        // Skip the beginning and change the relative\n        // start & end position as we skip the unwanted parts\n        relativeStart -= size\n        relativeEnd -= size\n      } else {\n        let chunk\n        if (ArrayBuffer.isView(part)) {\n          chunk = part.subarray(relativeStart, Math.min(size, relativeEnd))\n          added += chunk.byteLength\n        } else {\n          chunk = part.slice(relativeStart, Math.min(size, relativeEnd))\n          added += chunk.size\n        }\n        relativeEnd -= size\n        blobParts.push(chunk)\n        relativeStart = 0 // All next sequential parts should start at 0\n      }\n    }\n\n    const blob = new Blob([], { type: String(type).toLowerCase() })\n    blob.#size = span\n    blob.#parts = blobParts\n\n    return blob\n  }\n\n  get [Symbol.toStringTag] () {\n    return 'Blob'\n  }\n\n  static [Symbol.hasInstance] (object) {\n    return (\n      object &&\n      typeof object === 'object' &&\n      typeof object.constructor === 'function' &&\n      (\n        typeof object.stream === 'function' ||\n        typeof object.arrayBuffer === 'function'\n      ) &&\n      /^(Blob|File)$/.test(object[Symbol.toStringTag])\n    )\n  }\n}\n\nObject.defineProperties(_Blob.prototype, {\n  size: { enumerable: true },\n  type: { enumerable: true },\n  slice: { enumerable: true }\n})\n\n/** @type {typeof globalThis.Blob} */\nconst Blob = _Blob\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Blob);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fetch-blob/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/fetch-blob/streams.cjs":
/*!*********************************************!*\
  !*** ./node_modules/fetch-blob/streams.cjs ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("/* c8 ignore start */\n// 64 KiB (same size chrome slice theirs blob into Uint8array's)\nconst POOL_SIZE = 65536\n\nif (!globalThis.ReadableStream) {\n  // `node:stream/web` got introduced in v16.5.0 as experimental\n  // and it's preferred over the polyfilled version. So we also\n  // suppress the warning that gets emitted by NodeJS for using it.\n  try {\n    const process = __webpack_require__(/*! node:process */ \"node:process\")\n    const { emitWarning } = process\n    try {\n      process.emitWarning = () => {}\n      Object.assign(globalThis, __webpack_require__(/*! node:stream/web */ \"node:stream/web\"))\n      process.emitWarning = emitWarning\n    } catch (error) {\n      process.emitWarning = emitWarning\n      throw error\n    }\n  } catch (error) {\n    // fallback to polyfill implementation\n    Object.assign(globalThis, __webpack_require__(/*! web-streams-polyfill/dist/ponyfill.es2018.js */ \"(action-browser)/./node_modules/web-streams-polyfill/dist/ponyfill.es2018.js\"))\n  }\n}\n\ntry {\n  // Don't use node: prefix for this, require+node: is not supported until node v14.14\n  // Only `import()` can use prefix in 12.20 and later\n  const { Blob } = __webpack_require__(/*! buffer */ \"buffer\")\n  if (Blob && !Blob.prototype.stream) {\n    Blob.prototype.stream = function name (params) {\n      let position = 0\n      const blob = this\n\n      return new ReadableStream({\n        type: 'bytes',\n        async pull (ctrl) {\n          const chunk = blob.slice(position, Math.min(blob.size, position + POOL_SIZE))\n          const buffer = await chunk.arrayBuffer()\n          position += buffer.byteLength\n          ctrl.enqueue(new Uint8Array(buffer))\n\n          if (position === blob.size) {\n            ctrl.close()\n          }\n        }\n      })\n    }\n  }\n} catch (error) {}\n/* c8 ignore end */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fetch-blob/streams.cjs\n");

/***/ })

};
;