"use client";

import React from 'react';
import { useTradingContext } from '@/contexts/TradingContext';
import { TrendingUp } from 'lucide-react';

export default function MarketPriceDisplay() {
  const { config, currentMarketPrice } = useTradingContext();

  // Format price with appropriate precision based on value
  const formatPrice = (price: number) => {
    if (price === 0) return "0";
    if (price < 0.001) return price.toFixed(8);
    if (price < 1) return price.toFixed(6);
    if (price < 100) return price.toFixed(4);
    return price.toFixed(2);
  };

  // Check if both cryptos are selected
  const hasBothCryptos = config.crypto1 && config.crypto2;
  const displayPair = hasBothCryptos ? `${config.crypto1}/${config.crypto2}` : "Crypto 1/Crypto 2";
  const displayPrice = hasBothCryptos && currentMarketPrice > 0 ? formatPrice(currentMarketPrice) : "0";

  // Show trading mode context
  const modeText = config.tradingMode === 'StablecoinSwap' ? 'Swap Mode' : 'Spot Mode';

  return (
    <div className="mb-4 p-3 bg-gradient-to-r from-green-500/10 to-primary/10 border border-border rounded-md">
      <div className="flex items-center justify-center gap-3">
        <div className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-green-500" />
          <span className="text-sm font-medium text-muted-foreground">
            Current Market Price ({modeText})
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-lg font-semibold text-foreground">
            {displayPair} =
          </span>
          <span className="text-2xl font-bold text-primary">
            {displayPrice}
          </span>
          {hasBothCryptos && (
            <span className="text-sm text-muted-foreground">
              {config.crypto2}
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
