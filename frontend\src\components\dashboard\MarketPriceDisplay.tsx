"use client";

import React from 'react';
import { useTradingContext } from '@/contexts/TradingContext';
import { TrendingUp } from 'lucide-react';

export default function MarketPriceDisplay() {
  const { config, currentMarketPrice } = useTradingContext();

  const formatPrice = (price: number) => price.toFixed(config.numDigits);

  // Check if both cryptos are selected
  const hasBothCryptos = config.crypto1 && config.crypto2;
  const displayPair = hasBothCryptos ? `${config.crypto1}/${config.crypto2}` : "Crypto 1/Crypto 2";
  const displayPrice = hasBothCryptos ? formatPrice(currentMarketPrice) : "0";

  return (
    <div className="mb-4 p-3 bg-gradient-to-r from-green-500/10 to-primary/10 border border-border rounded-md">
      <div className="flex items-center justify-center gap-3">
        <div className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5 text-green-500" />
          <span className="text-sm font-medium text-muted-foreground">
            Current Market Price
          </span>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-lg font-semibold text-foreground">
            {displayPair}:
          </span>
          <span className="text-2xl font-bold text-primary">
            ${displayPrice}
          </span>
        </div>
      </div>
    </div>
  );
}
