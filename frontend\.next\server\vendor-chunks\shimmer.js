"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/shimmer";
exports.ids = ["vendor-chunks/shimmer"];
exports.modules = {

/***/ "(action-browser)/./node_modules/shimmer/index.js":
/*!***************************************!*\
  !*** ./node_modules/shimmer/index.js ***!
  \***************************************/
/***/ ((module) => {

eval("\n\nfunction isFunction (funktion) {\n  return typeof funktion === 'function'\n}\n\n// Default to complaining loudly when things don't go according to plan.\nvar logger = console.error.bind(console)\n\n// Sets a property on an object, preserving its enumerability.\n// This function assumes that the property is already writable.\nfunction defineProperty (obj, name, value) {\n  var enumerable = !!obj[name] && obj.propertyIsEnumerable(name)\n  Object.defineProperty(obj, name, {\n    configurable: true,\n    enumerable: enumerable,\n    writable: true,\n    value: value\n  })\n}\n\n// Keep initialization idempotent.\nfunction shimmer (options) {\n  if (options && options.logger) {\n    if (!isFunction(options.logger)) logger(\"new logger isn't a function, not replacing\")\n    else logger = options.logger\n  }\n}\n\nfunction wrap (nodule, name, wrapper) {\n  if (!nodule || !nodule[name]) {\n    logger('no original function ' + name + ' to wrap')\n    return\n  }\n\n  if (!wrapper) {\n    logger('no wrapper function')\n    logger((new Error()).stack)\n    return\n  }\n\n  if (!isFunction(nodule[name]) || !isFunction(wrapper)) {\n    logger('original object and wrapper must be functions')\n    return\n  }\n\n  var original = nodule[name]\n  var wrapped = wrapper(original, name)\n\n  defineProperty(wrapped, '__original', original)\n  defineProperty(wrapped, '__unwrap', function () {\n    if (nodule[name] === wrapped) defineProperty(nodule, name, original)\n  })\n  defineProperty(wrapped, '__wrapped', true)\n\n  defineProperty(nodule, name, wrapped)\n  return wrapped\n}\n\nfunction massWrap (nodules, names, wrapper) {\n  if (!nodules) {\n    logger('must provide one or more modules to patch')\n    logger((new Error()).stack)\n    return\n  } else if (!Array.isArray(nodules)) {\n    nodules = [nodules]\n  }\n\n  if (!(names && Array.isArray(names))) {\n    logger('must provide one or more functions to wrap on modules')\n    return\n  }\n\n  nodules.forEach(function (nodule) {\n    names.forEach(function (name) {\n      wrap(nodule, name, wrapper)\n    })\n  })\n}\n\nfunction unwrap (nodule, name) {\n  if (!nodule || !nodule[name]) {\n    logger('no function to unwrap.')\n    logger((new Error()).stack)\n    return\n  }\n\n  if (!nodule[name].__unwrap) {\n    logger('no original to unwrap to -- has ' + name + ' already been unwrapped?')\n  } else {\n    return nodule[name].__unwrap()\n  }\n}\n\nfunction massUnwrap (nodules, names) {\n  if (!nodules) {\n    logger('must provide one or more modules to patch')\n    logger((new Error()).stack)\n    return\n  } else if (!Array.isArray(nodules)) {\n    nodules = [nodules]\n  }\n\n  if (!(names && Array.isArray(names))) {\n    logger('must provide one or more functions to unwrap on modules')\n    return\n  }\n\n  nodules.forEach(function (nodule) {\n    names.forEach(function (name) {\n      unwrap(nodule, name)\n    })\n  })\n}\n\nshimmer.wrap = wrap\nshimmer.massWrap = massWrap\nshimmer.unwrap = unwrap\nshimmer.massUnwrap = massUnwrap\n\nmodule.exports = shimmer\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/shimmer/index.js\n");

/***/ })

};
;