/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bufrw";
exports.ids = ["vendor-chunks/bufrw"];
exports.modules = {

/***/ "(action-browser)/./node_modules/bufrw/annotated_buffer.js":
/*!************************************************!*\
  !*** ./node_modules/bufrw/annotated_buffer.js ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\n\nvar hex = __webpack_require__(/*! hexer */ \"(action-browser)/./node_modules/hexer/index.js\");\n\nvar color = (__webpack_require__(/*! ansi-color */ \"(action-browser)/./node_modules/ansi-color/lib/ansi-color.js\").set);\nvar stripColor = __webpack_require__(/*! ./lib/strip_color.js */ \"(action-browser)/./node_modules/bufrw/lib/strip_color.js\");\nvar extend = __webpack_require__(/*! xtend */ \"(action-browser)/./node_modules/xtend/immutable.js\");\nvar inspect = (__webpack_require__(/*! util */ \"util\").inspect);\n\nfunction AnnotatedBuffer(buffer) {\n    this.buffer = buffer;\n    this.annotations = [];\n}\n\nObject.defineProperty(AnnotatedBuffer.prototype, 'length', {\n    enumerable: true,\n    get: function getLength() {\n        return this.buffer.length;\n    }\n});\n\n// -- strings\n\nAnnotatedBuffer.prototype.toString = function toString(encoding, start, end) {\n    var value = this.buffer.toString(encoding, start, end);\n    this.annotations.push({\n        kind: 'read',\n        name: 'string',\n        value: value,\n        encoding: encoding,\n        start: start,\n        end: end\n    });\n    return value;\n};\n\n// -- bytes\n\n// istanbul ignore next\nAnnotatedBuffer.prototype.copy = function copy(targetBuffer, targetStart, sourceStart, sourceEnd) {\n    var copied = this.buffer.copy(targetBuffer, targetStart, sourceStart, sourceEnd);\n    // istanbul ignore next\n    var start = sourceStart || 0;\n    var end = sourceEnd || start + copied;\n    this.annotations.push({\n        kind: 'read',\n        name: 'copy',\n        value: this.buffer.slice(start, end),\n        start: start,\n        end: end\n    });\n    return copied;\n};\n\n// istanbul ignore next\nAnnotatedBuffer.prototype.slice = function slice(start, end) {\n    var value = this.buffer.slice(start, end);\n    this.annotations.push({\n        kind: 'read',\n        name: 'slice',\n        value: value,\n        start: start,\n        end: end\n    });\n    return value;\n};\n\n// -- atom readers\n\n// istanbul ignore next\nAnnotatedBuffer.prototype.readInt8 = function readInt8(offset, noAssert) {\n    var value = this.buffer.readInt8(offset, noAssert);\n    this.annotations.push({\n        kind: 'read',\n        name: 'Int8',\n        value: value,\n        start: offset,\n        end: offset + 1\n    });\n    return value;\n};\n\nAnnotatedBuffer.prototype.readUInt8 = function readUInt8(offset, noAssert) {\n    var value = this.buffer.readUInt8(offset, noAssert);\n    this.annotations.push({\n        kind: 'read',\n        name: 'UInt8',\n        value: value,\n        start: offset,\n        end: offset + 1\n    });\n    return value;\n};\n\n// istanbul ignore next\nAnnotatedBuffer.prototype.readUInt16LE = function readUInt16LE(offset, noAssert) {\n    var value = this.buffer.readUInt16LE(offset, noAssert);\n    this.annotations.push({\n        kind: 'read',\n        name: 'UInt16LE',\n        value: value,\n        start: offset,\n        end: offset + 2\n    });\n    return value;\n};\n\nAnnotatedBuffer.prototype.readUInt16BE = function readUInt16BE(offset, noAssert) {\n    var value = this.buffer.readUInt16BE(offset, noAssert);\n    this.annotations.push({\n        kind: 'read',\n        name: 'UInt16BE',\n        value: value,\n        start: offset,\n        end: offset + 2\n    });\n    return value;\n};\n\n// istanbul ignore next\nAnnotatedBuffer.prototype.readUInt32LE = function readUInt32LE(offset, noAssert) {\n    var value = this.buffer.readUInt32LE(offset, noAssert);\n    this.annotations.push({\n        kind: 'read',\n        name: 'UInt32LE',\n        value: value,\n        start: offset,\n        end: offset + 4\n    });\n    return value;\n};\n\n// istanbul ignore next\nAnnotatedBuffer.prototype.readUInt32BE = function readUInt32BE(offset, noAssert) {\n    var value = this.buffer.readUInt32BE(offset, noAssert);\n    this.annotations.push({\n        kind: 'read',\n        name: 'UInt32BE',\n        value: value,\n        start: offset,\n        end: offset + 4\n    });\n    return value;\n};\n\n// istanbul ignore next\nAnnotatedBuffer.prototype.readInt16LE = function readInt16LE(offset, noAssert) {\n    var value = this.buffer.readInt16LE(offset, noAssert);\n    this.annotations.push({\n        kind: 'read',\n        name: 'Int16LE',\n        value: value,\n        start: offset,\n        end: offset + 2\n    });\n    return value;\n};\n\n// istanbul ignore next\nAnnotatedBuffer.prototype.readInt16BE = function readInt16BE(offset, noAssert) {\n    var value = this.buffer.readInt16BE(offset, noAssert);\n    this.annotations.push({\n        kind: 'read',\n        name: 'Int16BE',\n        value: value,\n        start: offset,\n        end: offset + 2\n    });\n    return value;\n};\n\n// istanbul ignore next\nAnnotatedBuffer.prototype.readInt32LE = function readInt32LE(offset, noAssert) {\n    var value = this.buffer.readInt32LE(offset, noAssert);\n    this.annotations.push({\n        kind: 'read',\n        name: 'Int32LE',\n        value: value,\n        start: offset,\n        end: offset + 4\n    });\n    return value;\n};\n\n// istanbul ignore next\nAnnotatedBuffer.prototype.readInt32BE = function readInt32BE(offset, noAssert) {\n    var value = this.buffer.readInt32BE(offset, noAssert);\n    this.annotations.push({\n        kind: 'read',\n        name: 'Int32BE',\n        value: value,\n        start: offset,\n        end: offset + 4\n    });\n    return value;\n};\n\n// istanbul ignore next\nAnnotatedBuffer.prototype.readFloatLE = function readFloatLE(offset, noAssert) {\n    var value = this.buffer.readFloatLE(offset, noAssert);\n    this.annotations.push({\n        kind: 'read',\n        name: 'FloatLE',\n        value: value,\n        start: offset,\n        end: offset + 4\n    });\n    return value;\n};\n\n// istanbul ignore next\nAnnotatedBuffer.prototype.readFloatBE = function readFloatBE(offset, noAssert) {\n    var value = this.buffer.readFloatBE(offset, noAssert);\n    this.annotations.push({\n        kind: 'read',\n        name: 'FloatBE',\n        value: value,\n        start: offset,\n        end: offset + 4\n    });\n    return value;\n};\n\n// istanbul ignore next\nAnnotatedBuffer.prototype.readDoubleLE = function readDoubleLE(offset, noAssert) {\n    var value = this.buffer.readDoubleLE(offset, noAssert);\n    this.annotations.push({\n        kind: 'read',\n        name: 'DoubleLE',\n        value: value,\n        start: offset,\n        end: offset + 8\n    });\n    return value;\n};\n\n// istanbul ignore next\nAnnotatedBuffer.prototype.readDoubleBE = function readDoubleBE(offset, noAssert) {\n    var value = this.buffer.readDoubleBE(offset, noAssert);\n    this.annotations.push({\n        kind: 'read',\n        name: 'DoubleBE',\n        value: value,\n        start: offset,\n        end: offset + 8\n    });\n    return value;\n};\n\n// -- extras\n\n// istanbul ignore next\nAnnotatedBuffer.prototype.hexdump = function hexdump(options) {\n    var self = this;\n\n    options = extend(options, {\n        emptyHuman: ' ',\n        annotateLine: annotateLine\n    });\n    if (options.boldStart === undefined) options.boldStart = true;\n    options.decorateHexen = colorRegions;\n    options.decorateHuman = colorRegions;\n    var colors = options.colors || ['magenta', 'cyan', 'yellow', 'green'];\n    var colorI = 0;\n    var annI = 0;\n    var last = 0;\n    return hex(this.buffer, options);\n\n    function annotateLine(start, end) {\n        var parts = [];\n        for (var i = last; i <= annI; i++) {\n            var ann = self.annotations[i];\n            if (ann && ann.start >= start && ann.start < end) {\n                if (options.colored) {\n                    ann.color = colors[i % colors.length];\n                }\n                parts.push(ann);\n                last = i + 1;\n            }\n        }\n        return '  ' + parts.map(function(part) {\n            var desc = part.name;\n            if (typeof part.value !== 'string' &&\n                !Buffer.isBuffer(part.value)) {\n                desc += '(' + inspect(part.value) + ')';\n            }\n            if (part.color) {\n                desc = color(desc, part.color);\n            } else if (part.start === part.end) {\n                desc += '@' + part.start.toString(16);\n            } else {\n                desc += '@[' + part.start.toString(16) + ',' +\n                               part.end.toString(16) + ']';\n            }\n            if (options.highlight) {\n                desc = options.highlight(part.start, 0, desc);\n            }\n            return desc;\n        }).join(' ');\n    }\n\n    function colorRegions(i, j, str) {\n        var ann = self.annotations[annI];\n        while (ann && i >= ann.end) {\n            ann = self.annotations[++annI];\n            colorI = (colorI + 1) % colors.length;\n        }\n\n        if (ann && options.colored &&\n            i >= ann.start &&\n            i < ann.end) {\n            str = stripColor(str);\n            str = color(str, colors[colorI]);\n            if (i === ann.start && options.boldStart) str = color(str, 'bold');\n        }\n\n        if (options.highlight) {\n            str = options.highlight(i, j, str);\n        }\n\n        return str;\n    }\n};\n\nmodule.exports = AnnotatedBuffer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9idWZydy9hbm5vdGF0ZWRfYnVmZmVyLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVhOztBQUViLFVBQVUsbUJBQU8sQ0FBQyw2REFBTzs7QUFFekIsWUFBWSwyR0FBeUI7QUFDckMsaUJBQWlCLG1CQUFPLENBQUMsc0ZBQXNCO0FBQy9DLGFBQWEsbUJBQU8sQ0FBQyxpRUFBTztBQUM1QixjQUFjLGlEQUF1Qjs7QUFFckM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7O0FBRUQ7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLDJCQUEyQixXQUFXO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGJ1ZnJ3XFxhbm5vdGF0ZWRfYnVmZmVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgMjAxNSBVYmVyIFRlY2hub2xvZ2llcywgSW5jLlxuLy9cbi8vIFBlcm1pc3Npb24gaXMgaGVyZWJ5IGdyYW50ZWQsIGZyZWUgb2YgY2hhcmdlLCB0byBhbnkgcGVyc29uIG9idGFpbmluZyBhIGNvcHlcbi8vIG9mIHRoaXMgc29mdHdhcmUgYW5kIGFzc29jaWF0ZWQgZG9jdW1lbnRhdGlvbiBmaWxlcyAodGhlIFwiU29mdHdhcmVcIiksIHRvIGRlYWxcbi8vIGluIHRoZSBTb2Z0d2FyZSB3aXRob3V0IHJlc3RyaWN0aW9uLCBpbmNsdWRpbmcgd2l0aG91dCBsaW1pdGF0aW9uIHRoZSByaWdodHNcbi8vIHRvIHVzZSwgY29weSwgbW9kaWZ5LCBtZXJnZSwgcHVibGlzaCwgZGlzdHJpYnV0ZSwgc3VibGljZW5zZSwgYW5kL29yIHNlbGxcbi8vIGNvcGllcyBvZiB0aGUgU29mdHdhcmUsIGFuZCB0byBwZXJtaXQgcGVyc29ucyB0byB3aG9tIHRoZSBTb2Z0d2FyZSBpc1xuLy8gZnVybmlzaGVkIHRvIGRvIHNvLCBzdWJqZWN0IHRvIHRoZSBmb2xsb3dpbmcgY29uZGl0aW9uczpcbi8vXG4vLyBUaGUgYWJvdmUgY29weXJpZ2h0IG5vdGljZSBhbmQgdGhpcyBwZXJtaXNzaW9uIG5vdGljZSBzaGFsbCBiZSBpbmNsdWRlZCBpblxuLy8gYWxsIGNvcGllcyBvciBzdWJzdGFudGlhbCBwb3J0aW9ucyBvZiB0aGUgU29mdHdhcmUuXG4vL1xuLy8gVEhFIFNPRlRXQVJFIElTIFBST1ZJREVEIFwiQVMgSVNcIiwgV0lUSE9VVCBXQVJSQU5UWSBPRiBBTlkgS0lORCwgRVhQUkVTUyBPUlxuLy8gSU1QTElFRCwgSU5DTFVESU5HIEJVVCBOT1QgTElNSVRFRCBUTyBUSEUgV0FSUkFOVElFUyBPRiBNRVJDSEFOVEFCSUxJVFksXG4vLyBGSVRORVNTIEZPUiBBIFBBUlRJQ1VMQVIgUFVSUE9TRSBBTkQgTk9OSU5GUklOR0VNRU5ULiBJTiBOTyBFVkVOVCBTSEFMTCBUSEVcbi8vIEFVVEhPUlMgT1IgQ09QWVJJR0hUIEhPTERFUlMgQkUgTElBQkxFIEZPUiBBTlkgQ0xBSU0sIERBTUFHRVMgT1IgT1RIRVJcbi8vIExJQUJJTElUWSwgV0hFVEhFUiBJTiBBTiBBQ1RJT04gT0YgQ09OVFJBQ1QsIFRPUlQgT1IgT1RIRVJXSVNFLCBBUklTSU5HIEZST00sXG4vLyBPVVQgT0YgT1IgSU4gQ09OTkVDVElPTiBXSVRIIFRIRSBTT0ZUV0FSRSBPUiBUSEUgVVNFIE9SIE9USEVSIERFQUxJTkdTIElOXG4vLyBUSEUgU09GVFdBUkUuXG5cbid1c2Ugc3RyaWN0JztcblxudmFyIGhleCA9IHJlcXVpcmUoJ2hleGVyJyk7XG5cbnZhciBjb2xvciA9IHJlcXVpcmUoJ2Fuc2ktY29sb3InKS5zZXQ7XG52YXIgc3RyaXBDb2xvciA9IHJlcXVpcmUoJy4vbGliL3N0cmlwX2NvbG9yLmpzJyk7XG52YXIgZXh0ZW5kID0gcmVxdWlyZSgneHRlbmQnKTtcbnZhciBpbnNwZWN0ID0gcmVxdWlyZSgndXRpbCcpLmluc3BlY3Q7XG5cbmZ1bmN0aW9uIEFubm90YXRlZEJ1ZmZlcihidWZmZXIpIHtcbiAgICB0aGlzLmJ1ZmZlciA9IGJ1ZmZlcjtcbiAgICB0aGlzLmFubm90YXRpb25zID0gW107XG59XG5cbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShBbm5vdGF0ZWRCdWZmZXIucHJvdG90eXBlLCAnbGVuZ3RoJywge1xuICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgZ2V0OiBmdW5jdGlvbiBnZXRMZW5ndGgoKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmJ1ZmZlci5sZW5ndGg7XG4gICAgfVxufSk7XG5cbi8vIC0tIHN0cmluZ3NcblxuQW5ub3RhdGVkQnVmZmVyLnByb3RvdHlwZS50b1N0cmluZyA9IGZ1bmN0aW9uIHRvU3RyaW5nKGVuY29kaW5nLCBzdGFydCwgZW5kKSB7XG4gICAgdmFyIHZhbHVlID0gdGhpcy5idWZmZXIudG9TdHJpbmcoZW5jb2RpbmcsIHN0YXJ0LCBlbmQpO1xuICAgIHRoaXMuYW5ub3RhdGlvbnMucHVzaCh7XG4gICAgICAgIGtpbmQ6ICdyZWFkJyxcbiAgICAgICAgbmFtZTogJ3N0cmluZycsXG4gICAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgICAgZW5jb2Rpbmc6IGVuY29kaW5nLFxuICAgICAgICBzdGFydDogc3RhcnQsXG4gICAgICAgIGVuZDogZW5kXG4gICAgfSk7XG4gICAgcmV0dXJuIHZhbHVlO1xufTtcblxuLy8gLS0gYnl0ZXNcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkFubm90YXRlZEJ1ZmZlci5wcm90b3R5cGUuY29weSA9IGZ1bmN0aW9uIGNvcHkodGFyZ2V0QnVmZmVyLCB0YXJnZXRTdGFydCwgc291cmNlU3RhcnQsIHNvdXJjZUVuZCkge1xuICAgIHZhciBjb3BpZWQgPSB0aGlzLmJ1ZmZlci5jb3B5KHRhcmdldEJ1ZmZlciwgdGFyZ2V0U3RhcnQsIHNvdXJjZVN0YXJ0LCBzb3VyY2VFbmQpO1xuICAgIC8vIGlzdGFuYnVsIGlnbm9yZSBuZXh0XG4gICAgdmFyIHN0YXJ0ID0gc291cmNlU3RhcnQgfHwgMDtcbiAgICB2YXIgZW5kID0gc291cmNlRW5kIHx8IHN0YXJ0ICsgY29waWVkO1xuICAgIHRoaXMuYW5ub3RhdGlvbnMucHVzaCh7XG4gICAgICAgIGtpbmQ6ICdyZWFkJyxcbiAgICAgICAgbmFtZTogJ2NvcHknLFxuICAgICAgICB2YWx1ZTogdGhpcy5idWZmZXIuc2xpY2Uoc3RhcnQsIGVuZCksXG4gICAgICAgIHN0YXJ0OiBzdGFydCxcbiAgICAgICAgZW5kOiBlbmRcbiAgICB9KTtcbiAgICByZXR1cm4gY29waWVkO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkFubm90YXRlZEJ1ZmZlci5wcm90b3R5cGUuc2xpY2UgPSBmdW5jdGlvbiBzbGljZShzdGFydCwgZW5kKSB7XG4gICAgdmFyIHZhbHVlID0gdGhpcy5idWZmZXIuc2xpY2Uoc3RhcnQsIGVuZCk7XG4gICAgdGhpcy5hbm5vdGF0aW9ucy5wdXNoKHtcbiAgICAgICAga2luZDogJ3JlYWQnLFxuICAgICAgICBuYW1lOiAnc2xpY2UnLFxuICAgICAgICB2YWx1ZTogdmFsdWUsXG4gICAgICAgIHN0YXJ0OiBzdGFydCxcbiAgICAgICAgZW5kOiBlbmRcbiAgICB9KTtcbiAgICByZXR1cm4gdmFsdWU7XG59O1xuXG4vLyAtLSBhdG9tIHJlYWRlcnNcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkFubm90YXRlZEJ1ZmZlci5wcm90b3R5cGUucmVhZEludDggPSBmdW5jdGlvbiByZWFkSW50OChvZmZzZXQsIG5vQXNzZXJ0KSB7XG4gICAgdmFyIHZhbHVlID0gdGhpcy5idWZmZXIucmVhZEludDgob2Zmc2V0LCBub0Fzc2VydCk7XG4gICAgdGhpcy5hbm5vdGF0aW9ucy5wdXNoKHtcbiAgICAgICAga2luZDogJ3JlYWQnLFxuICAgICAgICBuYW1lOiAnSW50OCcsXG4gICAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgICAgc3RhcnQ6IG9mZnNldCxcbiAgICAgICAgZW5kOiBvZmZzZXQgKyAxXG4gICAgfSk7XG4gICAgcmV0dXJuIHZhbHVlO1xufTtcblxuQW5ub3RhdGVkQnVmZmVyLnByb3RvdHlwZS5yZWFkVUludDggPSBmdW5jdGlvbiByZWFkVUludDgob2Zmc2V0LCBub0Fzc2VydCkge1xuICAgIHZhciB2YWx1ZSA9IHRoaXMuYnVmZmVyLnJlYWRVSW50OChvZmZzZXQsIG5vQXNzZXJ0KTtcbiAgICB0aGlzLmFubm90YXRpb25zLnB1c2goe1xuICAgICAgICBraW5kOiAncmVhZCcsXG4gICAgICAgIG5hbWU6ICdVSW50OCcsXG4gICAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgICAgc3RhcnQ6IG9mZnNldCxcbiAgICAgICAgZW5kOiBvZmZzZXQgKyAxXG4gICAgfSk7XG4gICAgcmV0dXJuIHZhbHVlO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkFubm90YXRlZEJ1ZmZlci5wcm90b3R5cGUucmVhZFVJbnQxNkxFID0gZnVuY3Rpb24gcmVhZFVJbnQxNkxFKG9mZnNldCwgbm9Bc3NlcnQpIHtcbiAgICB2YXIgdmFsdWUgPSB0aGlzLmJ1ZmZlci5yZWFkVUludDE2TEUob2Zmc2V0LCBub0Fzc2VydCk7XG4gICAgdGhpcy5hbm5vdGF0aW9ucy5wdXNoKHtcbiAgICAgICAga2luZDogJ3JlYWQnLFxuICAgICAgICBuYW1lOiAnVUludDE2TEUnLFxuICAgICAgICB2YWx1ZTogdmFsdWUsXG4gICAgICAgIHN0YXJ0OiBvZmZzZXQsXG4gICAgICAgIGVuZDogb2Zmc2V0ICsgMlxuICAgIH0pO1xuICAgIHJldHVybiB2YWx1ZTtcbn07XG5cbkFubm90YXRlZEJ1ZmZlci5wcm90b3R5cGUucmVhZFVJbnQxNkJFID0gZnVuY3Rpb24gcmVhZFVJbnQxNkJFKG9mZnNldCwgbm9Bc3NlcnQpIHtcbiAgICB2YXIgdmFsdWUgPSB0aGlzLmJ1ZmZlci5yZWFkVUludDE2QkUob2Zmc2V0LCBub0Fzc2VydCk7XG4gICAgdGhpcy5hbm5vdGF0aW9ucy5wdXNoKHtcbiAgICAgICAga2luZDogJ3JlYWQnLFxuICAgICAgICBuYW1lOiAnVUludDE2QkUnLFxuICAgICAgICB2YWx1ZTogdmFsdWUsXG4gICAgICAgIHN0YXJ0OiBvZmZzZXQsXG4gICAgICAgIGVuZDogb2Zmc2V0ICsgMlxuICAgIH0pO1xuICAgIHJldHVybiB2YWx1ZTtcbn07XG5cbi8vIGlzdGFuYnVsIGlnbm9yZSBuZXh0XG5Bbm5vdGF0ZWRCdWZmZXIucHJvdG90eXBlLnJlYWRVSW50MzJMRSA9IGZ1bmN0aW9uIHJlYWRVSW50MzJMRShvZmZzZXQsIG5vQXNzZXJ0KSB7XG4gICAgdmFyIHZhbHVlID0gdGhpcy5idWZmZXIucmVhZFVJbnQzMkxFKG9mZnNldCwgbm9Bc3NlcnQpO1xuICAgIHRoaXMuYW5ub3RhdGlvbnMucHVzaCh7XG4gICAgICAgIGtpbmQ6ICdyZWFkJyxcbiAgICAgICAgbmFtZTogJ1VJbnQzMkxFJyxcbiAgICAgICAgdmFsdWU6IHZhbHVlLFxuICAgICAgICBzdGFydDogb2Zmc2V0LFxuICAgICAgICBlbmQ6IG9mZnNldCArIDRcbiAgICB9KTtcbiAgICByZXR1cm4gdmFsdWU7XG59O1xuXG4vLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuQW5ub3RhdGVkQnVmZmVyLnByb3RvdHlwZS5yZWFkVUludDMyQkUgPSBmdW5jdGlvbiByZWFkVUludDMyQkUob2Zmc2V0LCBub0Fzc2VydCkge1xuICAgIHZhciB2YWx1ZSA9IHRoaXMuYnVmZmVyLnJlYWRVSW50MzJCRShvZmZzZXQsIG5vQXNzZXJ0KTtcbiAgICB0aGlzLmFubm90YXRpb25zLnB1c2goe1xuICAgICAgICBraW5kOiAncmVhZCcsXG4gICAgICAgIG5hbWU6ICdVSW50MzJCRScsXG4gICAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgICAgc3RhcnQ6IG9mZnNldCxcbiAgICAgICAgZW5kOiBvZmZzZXQgKyA0XG4gICAgfSk7XG4gICAgcmV0dXJuIHZhbHVlO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkFubm90YXRlZEJ1ZmZlci5wcm90b3R5cGUucmVhZEludDE2TEUgPSBmdW5jdGlvbiByZWFkSW50MTZMRShvZmZzZXQsIG5vQXNzZXJ0KSB7XG4gICAgdmFyIHZhbHVlID0gdGhpcy5idWZmZXIucmVhZEludDE2TEUob2Zmc2V0LCBub0Fzc2VydCk7XG4gICAgdGhpcy5hbm5vdGF0aW9ucy5wdXNoKHtcbiAgICAgICAga2luZDogJ3JlYWQnLFxuICAgICAgICBuYW1lOiAnSW50MTZMRScsXG4gICAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgICAgc3RhcnQ6IG9mZnNldCxcbiAgICAgICAgZW5kOiBvZmZzZXQgKyAyXG4gICAgfSk7XG4gICAgcmV0dXJuIHZhbHVlO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkFubm90YXRlZEJ1ZmZlci5wcm90b3R5cGUucmVhZEludDE2QkUgPSBmdW5jdGlvbiByZWFkSW50MTZCRShvZmZzZXQsIG5vQXNzZXJ0KSB7XG4gICAgdmFyIHZhbHVlID0gdGhpcy5idWZmZXIucmVhZEludDE2QkUob2Zmc2V0LCBub0Fzc2VydCk7XG4gICAgdGhpcy5hbm5vdGF0aW9ucy5wdXNoKHtcbiAgICAgICAga2luZDogJ3JlYWQnLFxuICAgICAgICBuYW1lOiAnSW50MTZCRScsXG4gICAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgICAgc3RhcnQ6IG9mZnNldCxcbiAgICAgICAgZW5kOiBvZmZzZXQgKyAyXG4gICAgfSk7XG4gICAgcmV0dXJuIHZhbHVlO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkFubm90YXRlZEJ1ZmZlci5wcm90b3R5cGUucmVhZEludDMyTEUgPSBmdW5jdGlvbiByZWFkSW50MzJMRShvZmZzZXQsIG5vQXNzZXJ0KSB7XG4gICAgdmFyIHZhbHVlID0gdGhpcy5idWZmZXIucmVhZEludDMyTEUob2Zmc2V0LCBub0Fzc2VydCk7XG4gICAgdGhpcy5hbm5vdGF0aW9ucy5wdXNoKHtcbiAgICAgICAga2luZDogJ3JlYWQnLFxuICAgICAgICBuYW1lOiAnSW50MzJMRScsXG4gICAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgICAgc3RhcnQ6IG9mZnNldCxcbiAgICAgICAgZW5kOiBvZmZzZXQgKyA0XG4gICAgfSk7XG4gICAgcmV0dXJuIHZhbHVlO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkFubm90YXRlZEJ1ZmZlci5wcm90b3R5cGUucmVhZEludDMyQkUgPSBmdW5jdGlvbiByZWFkSW50MzJCRShvZmZzZXQsIG5vQXNzZXJ0KSB7XG4gICAgdmFyIHZhbHVlID0gdGhpcy5idWZmZXIucmVhZEludDMyQkUob2Zmc2V0LCBub0Fzc2VydCk7XG4gICAgdGhpcy5hbm5vdGF0aW9ucy5wdXNoKHtcbiAgICAgICAga2luZDogJ3JlYWQnLFxuICAgICAgICBuYW1lOiAnSW50MzJCRScsXG4gICAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgICAgc3RhcnQ6IG9mZnNldCxcbiAgICAgICAgZW5kOiBvZmZzZXQgKyA0XG4gICAgfSk7XG4gICAgcmV0dXJuIHZhbHVlO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkFubm90YXRlZEJ1ZmZlci5wcm90b3R5cGUucmVhZEZsb2F0TEUgPSBmdW5jdGlvbiByZWFkRmxvYXRMRShvZmZzZXQsIG5vQXNzZXJ0KSB7XG4gICAgdmFyIHZhbHVlID0gdGhpcy5idWZmZXIucmVhZEZsb2F0TEUob2Zmc2V0LCBub0Fzc2VydCk7XG4gICAgdGhpcy5hbm5vdGF0aW9ucy5wdXNoKHtcbiAgICAgICAga2luZDogJ3JlYWQnLFxuICAgICAgICBuYW1lOiAnRmxvYXRMRScsXG4gICAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgICAgc3RhcnQ6IG9mZnNldCxcbiAgICAgICAgZW5kOiBvZmZzZXQgKyA0XG4gICAgfSk7XG4gICAgcmV0dXJuIHZhbHVlO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkFubm90YXRlZEJ1ZmZlci5wcm90b3R5cGUucmVhZEZsb2F0QkUgPSBmdW5jdGlvbiByZWFkRmxvYXRCRShvZmZzZXQsIG5vQXNzZXJ0KSB7XG4gICAgdmFyIHZhbHVlID0gdGhpcy5idWZmZXIucmVhZEZsb2F0QkUob2Zmc2V0LCBub0Fzc2VydCk7XG4gICAgdGhpcy5hbm5vdGF0aW9ucy5wdXNoKHtcbiAgICAgICAga2luZDogJ3JlYWQnLFxuICAgICAgICBuYW1lOiAnRmxvYXRCRScsXG4gICAgICAgIHZhbHVlOiB2YWx1ZSxcbiAgICAgICAgc3RhcnQ6IG9mZnNldCxcbiAgICAgICAgZW5kOiBvZmZzZXQgKyA0XG4gICAgfSk7XG4gICAgcmV0dXJuIHZhbHVlO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkFubm90YXRlZEJ1ZmZlci5wcm90b3R5cGUucmVhZERvdWJsZUxFID0gZnVuY3Rpb24gcmVhZERvdWJsZUxFKG9mZnNldCwgbm9Bc3NlcnQpIHtcbiAgICB2YXIgdmFsdWUgPSB0aGlzLmJ1ZmZlci5yZWFkRG91YmxlTEUob2Zmc2V0LCBub0Fzc2VydCk7XG4gICAgdGhpcy5hbm5vdGF0aW9ucy5wdXNoKHtcbiAgICAgICAga2luZDogJ3JlYWQnLFxuICAgICAgICBuYW1lOiAnRG91YmxlTEUnLFxuICAgICAgICB2YWx1ZTogdmFsdWUsXG4gICAgICAgIHN0YXJ0OiBvZmZzZXQsXG4gICAgICAgIGVuZDogb2Zmc2V0ICsgOFxuICAgIH0pO1xuICAgIHJldHVybiB2YWx1ZTtcbn07XG5cbi8vIGlzdGFuYnVsIGlnbm9yZSBuZXh0XG5Bbm5vdGF0ZWRCdWZmZXIucHJvdG90eXBlLnJlYWREb3VibGVCRSA9IGZ1bmN0aW9uIHJlYWREb3VibGVCRShvZmZzZXQsIG5vQXNzZXJ0KSB7XG4gICAgdmFyIHZhbHVlID0gdGhpcy5idWZmZXIucmVhZERvdWJsZUJFKG9mZnNldCwgbm9Bc3NlcnQpO1xuICAgIHRoaXMuYW5ub3RhdGlvbnMucHVzaCh7XG4gICAgICAgIGtpbmQ6ICdyZWFkJyxcbiAgICAgICAgbmFtZTogJ0RvdWJsZUJFJyxcbiAgICAgICAgdmFsdWU6IHZhbHVlLFxuICAgICAgICBzdGFydDogb2Zmc2V0LFxuICAgICAgICBlbmQ6IG9mZnNldCArIDhcbiAgICB9KTtcbiAgICByZXR1cm4gdmFsdWU7XG59O1xuXG4vLyAtLSBleHRyYXNcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkFubm90YXRlZEJ1ZmZlci5wcm90b3R5cGUuaGV4ZHVtcCA9IGZ1bmN0aW9uIGhleGR1bXAob3B0aW9ucykge1xuICAgIHZhciBzZWxmID0gdGhpcztcblxuICAgIG9wdGlvbnMgPSBleHRlbmQob3B0aW9ucywge1xuICAgICAgICBlbXB0eUh1bWFuOiAnICcsXG4gICAgICAgIGFubm90YXRlTGluZTogYW5ub3RhdGVMaW5lXG4gICAgfSk7XG4gICAgaWYgKG9wdGlvbnMuYm9sZFN0YXJ0ID09PSB1bmRlZmluZWQpIG9wdGlvbnMuYm9sZFN0YXJ0ID0gdHJ1ZTtcbiAgICBvcHRpb25zLmRlY29yYXRlSGV4ZW4gPSBjb2xvclJlZ2lvbnM7XG4gICAgb3B0aW9ucy5kZWNvcmF0ZUh1bWFuID0gY29sb3JSZWdpb25zO1xuICAgIHZhciBjb2xvcnMgPSBvcHRpb25zLmNvbG9ycyB8fCBbJ21hZ2VudGEnLCAnY3lhbicsICd5ZWxsb3cnLCAnZ3JlZW4nXTtcbiAgICB2YXIgY29sb3JJID0gMDtcbiAgICB2YXIgYW5uSSA9IDA7XG4gICAgdmFyIGxhc3QgPSAwO1xuICAgIHJldHVybiBoZXgodGhpcy5idWZmZXIsIG9wdGlvbnMpO1xuXG4gICAgZnVuY3Rpb24gYW5ub3RhdGVMaW5lKHN0YXJ0LCBlbmQpIHtcbiAgICAgICAgdmFyIHBhcnRzID0gW107XG4gICAgICAgIGZvciAodmFyIGkgPSBsYXN0OyBpIDw9IGFubkk7IGkrKykge1xuICAgICAgICAgICAgdmFyIGFubiA9IHNlbGYuYW5ub3RhdGlvbnNbaV07XG4gICAgICAgICAgICBpZiAoYW5uICYmIGFubi5zdGFydCA+PSBzdGFydCAmJiBhbm4uc3RhcnQgPCBlbmQpIHtcbiAgICAgICAgICAgICAgICBpZiAob3B0aW9ucy5jb2xvcmVkKSB7XG4gICAgICAgICAgICAgICAgICAgIGFubi5jb2xvciA9IGNvbG9yc1tpICUgY29sb3JzLmxlbmd0aF07XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHBhcnRzLnB1c2goYW5uKTtcbiAgICAgICAgICAgICAgICBsYXN0ID0gaSArIDE7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuICcgICcgKyBwYXJ0cy5tYXAoZnVuY3Rpb24ocGFydCkge1xuICAgICAgICAgICAgdmFyIGRlc2MgPSBwYXJ0Lm5hbWU7XG4gICAgICAgICAgICBpZiAodHlwZW9mIHBhcnQudmFsdWUgIT09ICdzdHJpbmcnICYmXG4gICAgICAgICAgICAgICAgIUJ1ZmZlci5pc0J1ZmZlcihwYXJ0LnZhbHVlKSkge1xuICAgICAgICAgICAgICAgIGRlc2MgKz0gJygnICsgaW5zcGVjdChwYXJ0LnZhbHVlKSArICcpJztcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChwYXJ0LmNvbG9yKSB7XG4gICAgICAgICAgICAgICAgZGVzYyA9IGNvbG9yKGRlc2MsIHBhcnQuY29sb3IpO1xuICAgICAgICAgICAgfSBlbHNlIGlmIChwYXJ0LnN0YXJ0ID09PSBwYXJ0LmVuZCkge1xuICAgICAgICAgICAgICAgIGRlc2MgKz0gJ0AnICsgcGFydC5zdGFydC50b1N0cmluZygxNik7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGRlc2MgKz0gJ0BbJyArIHBhcnQuc3RhcnQudG9TdHJpbmcoMTYpICsgJywnICtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwYXJ0LmVuZC50b1N0cmluZygxNikgKyAnXSc7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAob3B0aW9ucy5oaWdobGlnaHQpIHtcbiAgICAgICAgICAgICAgICBkZXNjID0gb3B0aW9ucy5oaWdobGlnaHQocGFydC5zdGFydCwgMCwgZGVzYyk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gZGVzYztcbiAgICAgICAgfSkuam9pbignICcpO1xuICAgIH1cblxuICAgIGZ1bmN0aW9uIGNvbG9yUmVnaW9ucyhpLCBqLCBzdHIpIHtcbiAgICAgICAgdmFyIGFubiA9IHNlbGYuYW5ub3RhdGlvbnNbYW5uSV07XG4gICAgICAgIHdoaWxlIChhbm4gJiYgaSA+PSBhbm4uZW5kKSB7XG4gICAgICAgICAgICBhbm4gPSBzZWxmLmFubm90YXRpb25zWysrYW5uSV07XG4gICAgICAgICAgICBjb2xvckkgPSAoY29sb3JJICsgMSkgJSBjb2xvcnMubGVuZ3RoO1xuICAgICAgICB9XG5cbiAgICAgICAgaWYgKGFubiAmJiBvcHRpb25zLmNvbG9yZWQgJiZcbiAgICAgICAgICAgIGkgPj0gYW5uLnN0YXJ0ICYmXG4gICAgICAgICAgICBpIDwgYW5uLmVuZCkge1xuICAgICAgICAgICAgc3RyID0gc3RyaXBDb2xvcihzdHIpO1xuICAgICAgICAgICAgc3RyID0gY29sb3Ioc3RyLCBjb2xvcnNbY29sb3JJXSk7XG4gICAgICAgICAgICBpZiAoaSA9PT0gYW5uLnN0YXJ0ICYmIG9wdGlvbnMuYm9sZFN0YXJ0KSBzdHIgPSBjb2xvcihzdHIsICdib2xkJyk7XG4gICAgICAgIH1cblxuICAgICAgICBpZiAob3B0aW9ucy5oaWdobGlnaHQpIHtcbiAgICAgICAgICAgIHN0ciA9IG9wdGlvbnMuaGlnaGxpZ2h0KGksIGosIHN0cik7XG4gICAgICAgIH1cblxuICAgICAgICByZXR1cm4gc3RyO1xuICAgIH1cbn07XG5cbm1vZHVsZS5leHBvcnRzID0gQW5ub3RhdGVkQnVmZmVyO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/annotated_buffer.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/atoms.js":
/*!*************************************!*\
  !*** ./node_modules/bufrw/atoms.js ***!
  \*************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\n\nvar WriteResult = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").WriteResult);\nvar ReadResult = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").ReadResult);\nvar BufferRW = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").BufferRW);\nvar errors = __webpack_require__(/*! ./errors */ \"(action-browser)/./node_modules/bufrw/errors.js\");\n\nfunction AtomRW(width, readAtomFrom, writeAtomInto) {\n    if (!(this instanceof AtomRW)) {\n        return new AtomRW(width, readAtomFrom, writeAtomInto);\n    }\n    this.width = width;\n    this.readAtomFrom = readAtomFrom;\n    this.writeAtomInto = writeAtomInto;\n    BufferRW.call(this);\n}\ninherits(AtomRW, BufferRW);\n\nAtomRW.prototype.poolByteLength = function byteLength(destResult) {\n    return destResult.reset(null, this.width);\n};\n\nAtomRW.prototype.poolReadFrom = function readFrom(destResult, buffer, offset) {\n    var remain = buffer.length - offset;\n    if (remain < this.width) {\n        return ReadResult.poolShortError(destResult, this.width, remain, offset);\n    }\n    return this.readAtomFrom(destResult, buffer, offset);\n};\n\nAtomRW.prototype.poolWriteInto = function writeInto(destResult, value, buffer, offset) {\n    var remain = buffer.length - offset;\n    // istanbul ignore next\n    if (remain < this.width) {\n        WriteResult.poolShortError(destResult, this.width, remain, offset);\n    }\n    return this.writeAtomInto(destResult, value, buffer, offset);\n};\n\n// jshint maxparams:5\nfunction IntegerRW(width, min, max, readAtomFrom, writeAtomInto) {\n    if (!(this instanceof IntegerRW)) {\n        return new IntegerRW(width, min, max, readAtomFrom, writeAtomInto);\n    }\n    AtomRW.call(this, width, readAtomFrom, writeAtomInto);\n    this.min = min;\n    this.max = max;\n}\ninherits(IntegerRW, AtomRW);\n\nIntegerRW.prototype.poolWriteInto = function poolWriteInto(destResult, value, buffer, offset) {\n    if (typeof value !== 'number') {\n        return destResult.reset(errors.expected(value, 'a number'));\n    }\n    if (value < this.min || value > this.max) {\n        return destResult.reset(errors.RangeError({\n            value: value,\n            min: this.min,\n            max: this.max\n        }), offset);\n    }\n    var remain = buffer.length - offset;\n    if (remain < this.width) {\n        return WriteResult.poolShortError(destResult, this.width, remain, offset);\n    }\n    return this.writeAtomInto(destResult, value, buffer, offset);\n};\n\nvar Int8 = IntegerRW(1, -0x80, 0x7f,\n    function readInt8From(destResult, buffer, offset) {\n        var value = buffer.readInt8(offset, true);\n        return destResult.reset(null, offset + 1, value);\n    },\n    function writeInt8Into(destResult, value, buffer, offset) {\n        buffer.writeInt8(value, offset, true);\n        return destResult.reset(null, offset + 1);\n    });\n\nvar Int16BE = IntegerRW(2, -0x8000, 0x7fff,\n    function readInt16BEFrom(destResult, buffer, offset) {\n        var value = buffer.readInt16BE(offset, true);\n        return destResult.reset(null, offset + 2, value);\n    },\n    function writeInt16BEInto(destResult, value, buffer, offset) {\n        buffer.writeInt16BE(value, offset, true);\n        return destResult.reset(null, offset + 2);\n    });\n\nvar Int32BE = IntegerRW(4, -0x80000000, 0x7fffffff,\n    function readInt32BEFrom(destResult, buffer, offset) {\n        var value = buffer.readInt32BE(offset, true);\n        return destResult.reset(null, offset + 4, value);\n    },\n    function writeInt32BEInto(destResult, value, buffer, offset) {\n        buffer.writeInt32BE(value, offset, true);\n        return destResult.reset(null, offset + 4);\n    });\n\nvar Int16LE = IntegerRW(2, -0x8000, 0x7fff,\n    function readInt16LEFrom(destResult, buffer, offset) {\n        var value = buffer.readInt16LE(offset, true);\n        return destResult.reset(null, offset + 2, value);\n    },\n    function writeInt16LEInto(destResult, value, buffer, offset) {\n        buffer.writeInt16LE(value, offset, true);\n        return destResult.reset(null, offset + 2);\n    });\n\nvar Int32LE = IntegerRW(4, -0x80000000, 0x7fffffff,\n    function readInt32LEFrom(destResult, buffer, offset) {\n        var value = buffer.readInt32LE(offset, true);\n        return destResult.reset(null, offset + 4, value);\n    },\n    function writeInt32LEInto(destResult, value, buffer, offset) {\n        buffer.writeInt32LE(value, offset, true);\n        return destResult.reset(null, offset + 4);\n    });\n\nvar UInt8 = IntegerRW(1, 0, 0xff,\n    function readUInt8From(destResult, buffer, offset) {\n        var value = buffer.readUInt8(offset, true);\n        return destResult.reset(null, offset + 1, value);\n    },\n    function writeUInt8Into(destResult, value, buffer, offset) {\n        buffer.writeUInt8(value, offset, true);\n        return destResult.reset(null, offset + 1);\n    });\n\nvar UInt16BE = IntegerRW(2, 0, 0xffff,\n    function readUInt16BEFrom(destResult, buffer, offset) {\n        var value = buffer.readUInt16BE(offset, true);\n        return destResult.reset(null, offset + 2, value);\n    },\n    function writeUInt16BEInto(destResult, value, buffer, offset) {\n        buffer.writeUInt16BE(value, offset, true);\n        return destResult.reset(null, offset + 2);\n    });\n\nvar UInt32BE = IntegerRW(4, 0, 0xffffffff,\n    function readUInt32BEFrom(destResult, buffer, offset) {\n        var value = buffer.readUInt32BE(offset, true);\n        return destResult.reset(null, offset + 4, value);\n    },\n    function writeUInt32BEInto(destResult, value, buffer, offset) {\n        buffer.writeUInt32BE(value, offset, true);\n        return destResult.reset(null, offset + 4);\n    });\n\nvar UInt16LE = IntegerRW(2, 0, 0xffff,\n    function readUInt16LEFrom(destResult, buffer, offset) {\n        var value = buffer.readUInt16LE(offset, true);\n        return destResult.reset(null, offset + 2, value);\n    },\n    function writeUInt16LEInto(destResult, value, buffer, offset) {\n        buffer.writeUInt16LE(value, offset, true);\n        return destResult.reset(null, offset + 2);\n    });\n\nvar UInt32LE = IntegerRW(4, 0, 0xffffffff,\n    function readUInt32LEFrom(destResult, buffer, offset) {\n        var value = buffer.readUInt32LE(offset, true);\n        return destResult.reset(null, offset + 4, value);\n    },\n    function writeUInt32LEInto(destResult, value, buffer, offset) {\n        buffer.writeUInt32LE(value, offset, true);\n        return destResult.reset(null, offset + 4);\n    });\n\nvar FloatLE = AtomRW(4,\n    function readFloatLEFrom(destResult, buffer, offset) {\n        var value = buffer.readFloatLE(offset, true);\n        return destResult.reset(null, offset + 4, value);\n    },\n    function writeFloatLEInto(destResult, value, buffer, offset) {\n        // istanbul ignore if\n        if (typeof value !== 'number') {\n            return destResult.reset(errors.expected(value, 'a number'), null);\n        } else {\n            buffer.writeFloatLE(value, offset);\n            return destResult.reset(null, offset + 4);\n        }\n    });\n\nvar FloatBE = AtomRW(4,\n    function readFloatBEFrom(destResult, buffer, offset) {\n        var value = buffer.readFloatBE(offset, true);\n        return destResult.reset(null, offset + 4, value);\n    },\n    function writeFloatBEInto(destResult, value, buffer, offset) {\n        // istanbul ignore if\n        if (typeof value !== 'number') {\n            return destResult.reset(errors.expected(value, 'a number'), null);\n        } else {\n            buffer.writeFloatBE(value, offset);\n            return destResult.reset(null, offset + 4);\n        }\n    });\n\nvar DoubleLE = AtomRW(8,\n    function readDoubleLEFrom(destResult, buffer, offset) {\n        var value = buffer.readDoubleLE(offset, true);\n        return destResult.reset(null, offset + 8, value);\n    },\n    function writeDoubleLEInto(destResult, value, buffer, offset) {\n        // istanbul ignore if\n        if (typeof value !== 'number') {\n            return destResult.reset(errors.expected(value, 'a number'), null);\n        } else {\n            buffer.writeDoubleLE(value, offset);\n            return destResult.reset(null, offset + 8);\n        }\n    });\n\nvar DoubleBE = AtomRW(8,\n    function readDoubleBEFrom(destResult, buffer, offset) {\n        var value = buffer.readDoubleBE(offset, true);\n        return destResult.reset(null, offset + 8, value);\n    },\n    function writeDoubleBEInto(destResult, value, buffer, offset) {\n        // istanbul ignore if\n        if (typeof value !== 'number') {\n            return destResult.reset(errors.expected(value, 'a number'), null);\n        } else {\n            buffer.writeDoubleBE(value, offset);\n            return destResult.reset(null, offset + 8);\n        }\n    });\n\nmodule.exports.AtomRW = AtomRW;\nmodule.exports.Int8 = Int8;\nmodule.exports.Int16BE = Int16BE;\nmodule.exports.Int32BE = Int32BE;\nmodule.exports.Int16LE = Int16LE;\nmodule.exports.Int32LE = Int32LE;\nmodule.exports.UInt8 = UInt8;\nmodule.exports.UInt16BE = UInt16BE;\nmodule.exports.UInt32BE = UInt32BE;\nmodule.exports.UInt16LE = UInt16LE;\nmodule.exports.UInt32LE = UInt32LE;\nmodule.exports.FloatLE = FloatLE;\nmodule.exports.FloatBE = FloatBE;\nmodule.exports.DoubleLE = DoubleLE;\nmodule.exports.DoubleBE = DoubleBE;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/atoms.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/base.js":
/*!************************************!*\
  !*** ./node_modules/bufrw/base.js ***!
  \************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\nvar assert = __webpack_require__(/*! assert */ \"assert\");\n\nvar errors = __webpack_require__(/*! ./errors */ \"(action-browser)/./node_modules/bufrw/errors.js\");\n\nmodule.exports.BufferRW = BufferRW;\nmodule.exports.LengthResult = LengthResult;\nmodule.exports.WriteResult = WriteResult;\nmodule.exports.ReadResult = ReadResult;\n\nfunction BufferRW(byteLength, readFrom, writeInto, isPooled) {\n    if (!(this instanceof BufferRW)) {\n        return new BufferRW(byteLength, readFrom, writeInto, isPooled);\n    }\n\n    // istanbul ignore else\n    if (byteLength && readFrom && writeInto) {\n        assert(typeof byteLength === 'function', 'expected byteLength to be function');\n        assert(typeof readFrom === 'function', 'expected readFrom to be function');\n        assert(typeof writeInto === 'function', 'expected writeInto to be function');\n        // istanbul ignore else\n        if (isPooled) {\n            this.poolByteLength = byteLength;\n            this.poolReadFrom = readFrom;\n            this.poolWriteInto = writeInto;\n        } else {\n            this.byteLength = byteLength;\n            this.readFrom = readFrom;\n            this.writeInto = writeInto;\n        }\n    } else {\n        // Args weren't specified. Expect either pool methods or regular\n        // methods to be overriden.\n\n        assert(\n            this.poolReadFrom !== BufferRW.prototype.poolReadFrom ||\n            this.readFrom !== BufferRW.prototype.readFrom,\n            'expected either poolReadFrom or readFrom to be overriden'\n        );\n        assert(\n            this.poolWriteInto !== BufferRW.prototype.poolWriteInto ||\n            this.writeInto !== BufferRW.prototype.writeInto,\n            'expected either poolWriteInto or writeInto to be overriden'\n        );\n        assert(\n            this.poolByteLength !== BufferRW.prototype.poolByteLength ||\n            this.byteLength !== BufferRW.prototype.byteLength,\n            'expected either poolByteLength or byteLength to be overriden'\n        );\n    }\n}\n\nBufferRW.prototype.readFrom = function readFrom(arg1, arg2, arg3) {\n    assert(this.poolReadFrom !== BufferRW.prototype.poolReadFrom, 'poolReadFrom is overridden');\n    var readResult = new ReadResult();\n    this.poolReadFrom(readResult, arg1, arg2, arg3);\n    return readResult;\n};\n\nBufferRW.prototype.writeInto = function writeInto(value, buffer, offset) {\n    assert(this.poolWriteInto !== BufferRW.prototype.poolWriteInto, 'poolWriteInto is overridden');\n    var writeResult = new WriteResult();\n    this.poolWriteInto(writeResult, value, buffer, offset);\n    return writeResult;\n};\n\nBufferRW.prototype.byteLength = function byteLength(arg1, arg2, arg3) {\n    assert(this.poolbyteLength !== BufferRW.prototype.poolByteLength, 'poolByteLength is overridden');\n    var lengthResult = new LengthResult();\n    this.poolByteLength(lengthResult, arg1, arg2, arg3);\n    return lengthResult;\n};\n\n// istanbul ignore next\nBufferRW.prototype.poolReadFrom = function poolReadFrom(destResult, arg1, arg2, arg3) {\n    var res = this.readFrom(arg1, arg2, arg3);\n    return destResult.copyFrom(res);\n};\n\n// istanbul ignore next\nBufferRW.prototype.poolWriteInto = function poolWriteInto(destResult, value, buffer, offset) {\n    var res = this.writeInto(value, buffer, offset);\n    return destResult.copyFrom(res);\n};\n\n// istanbul ignore next\nBufferRW.prototype.poolByteLength = function poolByteLength(destResult, arg1, arg2, arg3) {\n    var res = this.byteLength(arg1, arg2, arg3);\n    return destResult.copyFrom(res);\n};\n\nfunction LengthResult(err, length) {\n    this.err = err || null;\n    this.length = length || 0;\n}\n\nLengthResult.prototype.reset = function reset(err, length) {\n    this.err = err;\n    this.length = length;\n    return this;\n};\n\n// istanbul ignore next\nLengthResult.prototype.copyFrom = function copyFrom(srcRes) {\n    this.err = srcRes.err;\n    this.length = srcRes.length;\n    return this;\n};\n\n// istanbul ignore next\nLengthResult.error = function error(err, length) {\n    return new LengthResult(err, length);\n};\n\n// istanbul ignore next\nLengthResult.just = function just(length) {\n    return new LengthResult(null, length);\n};\n\nfunction WriteResult(err, offset) {\n    this.err = err || null;\n    this.offset = offset || 0;\n}\n\nWriteResult.prototype.reset = function reset(err, offset) {\n    this.err = err;\n    this.offset = offset;\n    return this;\n};\n\n// istanbul ignore next\nWriteResult.prototype.copyFrom = function copyFrom(srcResult) {\n    this.err = srcResult.err;\n    this.offset = srcResult.offset;\n};\n\n// istanbul ignore next\nWriteResult.error = function error(err, offset) {\n    return new WriteResult(err, offset);\n};\n\n// istanbul ignore next\n/*jshint maxparams:6*/\nWriteResult.poolRangedError = function poolRangedError(destResult, err, start, end, value) {\n    assert(typeof destResult === 'object' && destResult.constructor.name === 'WriteResult');\n\n    err.offest = start;\n    err.endOffset = end;\n    return destResult.reset(err, start, value);\n};\n\n// istanbul ignore next\nWriteResult.rangedError = function rangedError(err, start, end, value) {\n    return WriteResult.poolRangedError(new WriteResult(), start, end, value);\n};\n\n// istanbul ignore next\nWriteResult.just = function just(offset) {\n    return new WriteResult(null, offset);\n};\n\n\n// istanbul ignore next\nWriteResult.shortError = function shortError(expected, actual, offset) {\n    return WriteResult.poolShortError(new WriteResult(), expected, actual, offset);\n};\n\n// istanbul ignore next\nWriteResult.poolShortError = function poolShortError(destResult, expected, actual, offset) {\n    assert(typeof destResult === 'object' && destResult.constructor.name === 'WriteResult');\n\n    return destResult.reset(new errors.ShortBuffer({\n        expected: expected,\n        actual: actual,\n        offset: offset\n    }), offset);\n};\n\nfunction ReadResult(err, offset, value) {\n    this.err = err || null;\n    this.offset = offset || 0;\n    // istanbul ignore next\n    this.value = value === undefined ? null : value;\n}\n\n// istanbul ignore next\nReadResult.prototype.copyFrom = function copyFrom(srcResult) {\n    this.err = srcResult.err;\n    this.offset = srcResult.offset;\n    this.value = srcResult.value;\n    return this;\n};\n\n// istanbul ignore next\nReadResult.prototype.reset = function reset(err, offset, value) {\n    this.err = err;\n    this.offset = offset;\n    this.value = value;\n    return this;\n};\n\n// istanbul ignore next\nReadResult.error = function error(err, offset, value) {\n    return new ReadResult(err, offset, value);\n};\n\n// istanbul ignore next\nReadResult.poolRangedError = function poolRangedError(destResult, err, start, end, value) {\n    assert(typeof destResult === 'object' && destResult.constructor.name === 'ReadResult');\n\n    err.offest = start;\n    err.endOffset = end;\n    return destResult.reset(err, start, value);\n};\n\n// istanbul ignore next\nReadResult.rangedError = function rangedError(err, start, end, value) {\n    return ReadResult.poolRangedError(new ReadResult(), err, start, end, value);\n};\n\n// istanbul ignore next\nReadResult.just = function just(offset, value) {\n    return new ReadResult(null, offset, value);\n};\n\n// istanbul ignore next\nReadResult.shortError = function shortError(destResult, expected, actual, offset, endOffset) {\n    return ReadResult.poolShortError(new ReadResult(), expected, actual, offset, endOffset);\n};\n\nReadResult.poolShortError = function poolShortError(destResult, expected, actual, offset, endOffset) {\n    assert(typeof destResult === 'object' && destResult.constructor.name === 'ReadResult');\n    var err;\n\n    if (endOffset === undefined) {\n        err = new errors.ShortBuffer({\n            expected: expected,\n            actual: actual,\n            offset: offset\n        }); \n    } else {\n        err = new errors.ShortBufferRanged({\n            expected: expected,\n            actual: actual,\n            offset: offset,\n            endOffset: endOffset\n        });\n    }\n\n    return destResult.reset(err, offset);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9idWZydy9iYXNlLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ2E7O0FBRWIsYUFBYSxtQkFBTyxDQUFDLHNCQUFROztBQUU3QixhQUFhLG1CQUFPLENBQUMsaUVBQVU7O0FBRS9CLHVCQUF1QjtBQUN2QiwyQkFBMkI7QUFDM0IsMEJBQTBCO0FBQzFCLHlCQUF5Qjs7QUFFekI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7O0FBR0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1QsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcYnVmcndcXGJhc2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSAyMDE1IFViZXIgVGVjaG5vbG9naWVzLCBJbmMuXG4vL1xuLy8gUGVybWlzc2lvbiBpcyBoZXJlYnkgZ3JhbnRlZCwgZnJlZSBvZiBjaGFyZ2UsIHRvIGFueSBwZXJzb24gb2J0YWluaW5nIGEgY29weVxuLy8gb2YgdGhpcyBzb2Z0d2FyZSBhbmQgYXNzb2NpYXRlZCBkb2N1bWVudGF0aW9uIGZpbGVzICh0aGUgXCJTb2Z0d2FyZVwiKSwgdG8gZGVhbFxuLy8gaW4gdGhlIFNvZnR3YXJlIHdpdGhvdXQgcmVzdHJpY3Rpb24sIGluY2x1ZGluZyB3aXRob3V0IGxpbWl0YXRpb24gdGhlIHJpZ2h0c1xuLy8gdG8gdXNlLCBjb3B5LCBtb2RpZnksIG1lcmdlLCBwdWJsaXNoLCBkaXN0cmlidXRlLCBzdWJsaWNlbnNlLCBhbmQvb3Igc2VsbFxuLy8gY29waWVzIG9mIHRoZSBTb2Z0d2FyZSwgYW5kIHRvIHBlcm1pdCBwZXJzb25zIHRvIHdob20gdGhlIFNvZnR3YXJlIGlzXG4vLyBmdXJuaXNoZWQgdG8gZG8gc28sIHN1YmplY3QgdG8gdGhlIGZvbGxvd2luZyBjb25kaXRpb25zOlxuLy9cbi8vIFRoZSBhYm92ZSBjb3B5cmlnaHQgbm90aWNlIGFuZCB0aGlzIHBlcm1pc3Npb24gbm90aWNlIHNoYWxsIGJlIGluY2x1ZGVkIGluXG4vLyBhbGwgY29waWVzIG9yIHN1YnN0YW50aWFsIHBvcnRpb25zIG9mIHRoZSBTb2Z0d2FyZS5cbi8vXG4vLyBUSEUgU09GVFdBUkUgSVMgUFJPVklERUQgXCJBUyBJU1wiLCBXSVRIT1VUIFdBUlJBTlRZIE9GIEFOWSBLSU5ELCBFWFBSRVNTIE9SXG4vLyBJTVBMSUVELCBJTkNMVURJTkcgQlVUIE5PVCBMSU1JVEVEIFRPIFRIRSBXQVJSQU5USUVTIE9GIE1FUkNIQU5UQUJJTElUWSxcbi8vIEZJVE5FU1MgRk9SIEEgUEFSVElDVUxBUiBQVVJQT1NFIEFORCBOT05JTkZSSU5HRU1FTlQuIElOIE5PIEVWRU5UIFNIQUxMIFRIRVxuLy8gQVVUSE9SUyBPUiBDT1BZUklHSFQgSE9MREVSUyBCRSBMSUFCTEUgRk9SIEFOWSBDTEFJTSwgREFNQUdFUyBPUiBPVEhFUlxuLy8gTElBQklMSVRZLCBXSEVUSEVSIElOIEFOIEFDVElPTiBPRiBDT05UUkFDVCwgVE9SVCBPUiBPVEhFUldJU0UsIEFSSVNJTkcgRlJPTSxcbi8vIE9VVCBPRiBPUiBJTiBDT05ORUNUSU9OIFdJVEggVEhFIFNPRlRXQVJFIE9SIFRIRSBVU0UgT1IgT1RIRVIgREVBTElOR1MgSU5cbi8vIFRIRSBTT0ZUV0FSRS5cbid1c2Ugc3RyaWN0JztcblxudmFyIGFzc2VydCA9IHJlcXVpcmUoJ2Fzc2VydCcpO1xuXG52YXIgZXJyb3JzID0gcmVxdWlyZSgnLi9lcnJvcnMnKTtcblxubW9kdWxlLmV4cG9ydHMuQnVmZmVyUlcgPSBCdWZmZXJSVztcbm1vZHVsZS5leHBvcnRzLkxlbmd0aFJlc3VsdCA9IExlbmd0aFJlc3VsdDtcbm1vZHVsZS5leHBvcnRzLldyaXRlUmVzdWx0ID0gV3JpdGVSZXN1bHQ7XG5tb2R1bGUuZXhwb3J0cy5SZWFkUmVzdWx0ID0gUmVhZFJlc3VsdDtcblxuZnVuY3Rpb24gQnVmZmVyUlcoYnl0ZUxlbmd0aCwgcmVhZEZyb20sIHdyaXRlSW50bywgaXNQb29sZWQpIHtcbiAgICBpZiAoISh0aGlzIGluc3RhbmNlb2YgQnVmZmVyUlcpKSB7XG4gICAgICAgIHJldHVybiBuZXcgQnVmZmVyUlcoYnl0ZUxlbmd0aCwgcmVhZEZyb20sIHdyaXRlSW50bywgaXNQb29sZWQpO1xuICAgIH1cblxuICAgIC8vIGlzdGFuYnVsIGlnbm9yZSBlbHNlXG4gICAgaWYgKGJ5dGVMZW5ndGggJiYgcmVhZEZyb20gJiYgd3JpdGVJbnRvKSB7XG4gICAgICAgIGFzc2VydCh0eXBlb2YgYnl0ZUxlbmd0aCA9PT0gJ2Z1bmN0aW9uJywgJ2V4cGVjdGVkIGJ5dGVMZW5ndGggdG8gYmUgZnVuY3Rpb24nKTtcbiAgICAgICAgYXNzZXJ0KHR5cGVvZiByZWFkRnJvbSA9PT0gJ2Z1bmN0aW9uJywgJ2V4cGVjdGVkIHJlYWRGcm9tIHRvIGJlIGZ1bmN0aW9uJyk7XG4gICAgICAgIGFzc2VydCh0eXBlb2Ygd3JpdGVJbnRvID09PSAnZnVuY3Rpb24nLCAnZXhwZWN0ZWQgd3JpdGVJbnRvIHRvIGJlIGZ1bmN0aW9uJyk7XG4gICAgICAgIC8vIGlzdGFuYnVsIGlnbm9yZSBlbHNlXG4gICAgICAgIGlmIChpc1Bvb2xlZCkge1xuICAgICAgICAgICAgdGhpcy5wb29sQnl0ZUxlbmd0aCA9IGJ5dGVMZW5ndGg7XG4gICAgICAgICAgICB0aGlzLnBvb2xSZWFkRnJvbSA9IHJlYWRGcm9tO1xuICAgICAgICAgICAgdGhpcy5wb29sV3JpdGVJbnRvID0gd3JpdGVJbnRvO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgdGhpcy5ieXRlTGVuZ3RoID0gYnl0ZUxlbmd0aDtcbiAgICAgICAgICAgIHRoaXMucmVhZEZyb20gPSByZWFkRnJvbTtcbiAgICAgICAgICAgIHRoaXMud3JpdGVJbnRvID0gd3JpdGVJbnRvO1xuICAgICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgICAgLy8gQXJncyB3ZXJlbid0IHNwZWNpZmllZC4gRXhwZWN0IGVpdGhlciBwb29sIG1ldGhvZHMgb3IgcmVndWxhclxuICAgICAgICAvLyBtZXRob2RzIHRvIGJlIG92ZXJyaWRlbi5cblxuICAgICAgICBhc3NlcnQoXG4gICAgICAgICAgICB0aGlzLnBvb2xSZWFkRnJvbSAhPT0gQnVmZmVyUlcucHJvdG90eXBlLnBvb2xSZWFkRnJvbSB8fFxuICAgICAgICAgICAgdGhpcy5yZWFkRnJvbSAhPT0gQnVmZmVyUlcucHJvdG90eXBlLnJlYWRGcm9tLFxuICAgICAgICAgICAgJ2V4cGVjdGVkIGVpdGhlciBwb29sUmVhZEZyb20gb3IgcmVhZEZyb20gdG8gYmUgb3ZlcnJpZGVuJ1xuICAgICAgICApO1xuICAgICAgICBhc3NlcnQoXG4gICAgICAgICAgICB0aGlzLnBvb2xXcml0ZUludG8gIT09IEJ1ZmZlclJXLnByb3RvdHlwZS5wb29sV3JpdGVJbnRvIHx8XG4gICAgICAgICAgICB0aGlzLndyaXRlSW50byAhPT0gQnVmZmVyUlcucHJvdG90eXBlLndyaXRlSW50byxcbiAgICAgICAgICAgICdleHBlY3RlZCBlaXRoZXIgcG9vbFdyaXRlSW50byBvciB3cml0ZUludG8gdG8gYmUgb3ZlcnJpZGVuJ1xuICAgICAgICApO1xuICAgICAgICBhc3NlcnQoXG4gICAgICAgICAgICB0aGlzLnBvb2xCeXRlTGVuZ3RoICE9PSBCdWZmZXJSVy5wcm90b3R5cGUucG9vbEJ5dGVMZW5ndGggfHxcbiAgICAgICAgICAgIHRoaXMuYnl0ZUxlbmd0aCAhPT0gQnVmZmVyUlcucHJvdG90eXBlLmJ5dGVMZW5ndGgsXG4gICAgICAgICAgICAnZXhwZWN0ZWQgZWl0aGVyIHBvb2xCeXRlTGVuZ3RoIG9yIGJ5dGVMZW5ndGggdG8gYmUgb3ZlcnJpZGVuJ1xuICAgICAgICApO1xuICAgIH1cbn1cblxuQnVmZmVyUlcucHJvdG90eXBlLnJlYWRGcm9tID0gZnVuY3Rpb24gcmVhZEZyb20oYXJnMSwgYXJnMiwgYXJnMykge1xuICAgIGFzc2VydCh0aGlzLnBvb2xSZWFkRnJvbSAhPT0gQnVmZmVyUlcucHJvdG90eXBlLnBvb2xSZWFkRnJvbSwgJ3Bvb2xSZWFkRnJvbSBpcyBvdmVycmlkZGVuJyk7XG4gICAgdmFyIHJlYWRSZXN1bHQgPSBuZXcgUmVhZFJlc3VsdCgpO1xuICAgIHRoaXMucG9vbFJlYWRGcm9tKHJlYWRSZXN1bHQsIGFyZzEsIGFyZzIsIGFyZzMpO1xuICAgIHJldHVybiByZWFkUmVzdWx0O1xufTtcblxuQnVmZmVyUlcucHJvdG90eXBlLndyaXRlSW50byA9IGZ1bmN0aW9uIHdyaXRlSW50byh2YWx1ZSwgYnVmZmVyLCBvZmZzZXQpIHtcbiAgICBhc3NlcnQodGhpcy5wb29sV3JpdGVJbnRvICE9PSBCdWZmZXJSVy5wcm90b3R5cGUucG9vbFdyaXRlSW50bywgJ3Bvb2xXcml0ZUludG8gaXMgb3ZlcnJpZGRlbicpO1xuICAgIHZhciB3cml0ZVJlc3VsdCA9IG5ldyBXcml0ZVJlc3VsdCgpO1xuICAgIHRoaXMucG9vbFdyaXRlSW50byh3cml0ZVJlc3VsdCwgdmFsdWUsIGJ1ZmZlciwgb2Zmc2V0KTtcbiAgICByZXR1cm4gd3JpdGVSZXN1bHQ7XG59O1xuXG5CdWZmZXJSVy5wcm90b3R5cGUuYnl0ZUxlbmd0aCA9IGZ1bmN0aW9uIGJ5dGVMZW5ndGgoYXJnMSwgYXJnMiwgYXJnMykge1xuICAgIGFzc2VydCh0aGlzLnBvb2xieXRlTGVuZ3RoICE9PSBCdWZmZXJSVy5wcm90b3R5cGUucG9vbEJ5dGVMZW5ndGgsICdwb29sQnl0ZUxlbmd0aCBpcyBvdmVycmlkZGVuJyk7XG4gICAgdmFyIGxlbmd0aFJlc3VsdCA9IG5ldyBMZW5ndGhSZXN1bHQoKTtcbiAgICB0aGlzLnBvb2xCeXRlTGVuZ3RoKGxlbmd0aFJlc3VsdCwgYXJnMSwgYXJnMiwgYXJnMyk7XG4gICAgcmV0dXJuIGxlbmd0aFJlc3VsdDtcbn07XG5cbi8vIGlzdGFuYnVsIGlnbm9yZSBuZXh0XG5CdWZmZXJSVy5wcm90b3R5cGUucG9vbFJlYWRGcm9tID0gZnVuY3Rpb24gcG9vbFJlYWRGcm9tKGRlc3RSZXN1bHQsIGFyZzEsIGFyZzIsIGFyZzMpIHtcbiAgICB2YXIgcmVzID0gdGhpcy5yZWFkRnJvbShhcmcxLCBhcmcyLCBhcmczKTtcbiAgICByZXR1cm4gZGVzdFJlc3VsdC5jb3B5RnJvbShyZXMpO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkJ1ZmZlclJXLnByb3RvdHlwZS5wb29sV3JpdGVJbnRvID0gZnVuY3Rpb24gcG9vbFdyaXRlSW50byhkZXN0UmVzdWx0LCB2YWx1ZSwgYnVmZmVyLCBvZmZzZXQpIHtcbiAgICB2YXIgcmVzID0gdGhpcy53cml0ZUludG8odmFsdWUsIGJ1ZmZlciwgb2Zmc2V0KTtcbiAgICByZXR1cm4gZGVzdFJlc3VsdC5jb3B5RnJvbShyZXMpO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkJ1ZmZlclJXLnByb3RvdHlwZS5wb29sQnl0ZUxlbmd0aCA9IGZ1bmN0aW9uIHBvb2xCeXRlTGVuZ3RoKGRlc3RSZXN1bHQsIGFyZzEsIGFyZzIsIGFyZzMpIHtcbiAgICB2YXIgcmVzID0gdGhpcy5ieXRlTGVuZ3RoKGFyZzEsIGFyZzIsIGFyZzMpO1xuICAgIHJldHVybiBkZXN0UmVzdWx0LmNvcHlGcm9tKHJlcyk7XG59O1xuXG5mdW5jdGlvbiBMZW5ndGhSZXN1bHQoZXJyLCBsZW5ndGgpIHtcbiAgICB0aGlzLmVyciA9IGVyciB8fCBudWxsO1xuICAgIHRoaXMubGVuZ3RoID0gbGVuZ3RoIHx8IDA7XG59XG5cbkxlbmd0aFJlc3VsdC5wcm90b3R5cGUucmVzZXQgPSBmdW5jdGlvbiByZXNldChlcnIsIGxlbmd0aCkge1xuICAgIHRoaXMuZXJyID0gZXJyO1xuICAgIHRoaXMubGVuZ3RoID0gbGVuZ3RoO1xuICAgIHJldHVybiB0aGlzO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkxlbmd0aFJlc3VsdC5wcm90b3R5cGUuY29weUZyb20gPSBmdW5jdGlvbiBjb3B5RnJvbShzcmNSZXMpIHtcbiAgICB0aGlzLmVyciA9IHNyY1Jlcy5lcnI7XG4gICAgdGhpcy5sZW5ndGggPSBzcmNSZXMubGVuZ3RoO1xuICAgIHJldHVybiB0aGlzO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkxlbmd0aFJlc3VsdC5lcnJvciA9IGZ1bmN0aW9uIGVycm9yKGVyciwgbGVuZ3RoKSB7XG4gICAgcmV0dXJuIG5ldyBMZW5ndGhSZXN1bHQoZXJyLCBsZW5ndGgpO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbkxlbmd0aFJlc3VsdC5qdXN0ID0gZnVuY3Rpb24ganVzdChsZW5ndGgpIHtcbiAgICByZXR1cm4gbmV3IExlbmd0aFJlc3VsdChudWxsLCBsZW5ndGgpO1xufTtcblxuZnVuY3Rpb24gV3JpdGVSZXN1bHQoZXJyLCBvZmZzZXQpIHtcbiAgICB0aGlzLmVyciA9IGVyciB8fCBudWxsO1xuICAgIHRoaXMub2Zmc2V0ID0gb2Zmc2V0IHx8IDA7XG59XG5cbldyaXRlUmVzdWx0LnByb3RvdHlwZS5yZXNldCA9IGZ1bmN0aW9uIHJlc2V0KGVyciwgb2Zmc2V0KSB7XG4gICAgdGhpcy5lcnIgPSBlcnI7XG4gICAgdGhpcy5vZmZzZXQgPSBvZmZzZXQ7XG4gICAgcmV0dXJuIHRoaXM7XG59O1xuXG4vLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuV3JpdGVSZXN1bHQucHJvdG90eXBlLmNvcHlGcm9tID0gZnVuY3Rpb24gY29weUZyb20oc3JjUmVzdWx0KSB7XG4gICAgdGhpcy5lcnIgPSBzcmNSZXN1bHQuZXJyO1xuICAgIHRoaXMub2Zmc2V0ID0gc3JjUmVzdWx0Lm9mZnNldDtcbn07XG5cbi8vIGlzdGFuYnVsIGlnbm9yZSBuZXh0XG5Xcml0ZVJlc3VsdC5lcnJvciA9IGZ1bmN0aW9uIGVycm9yKGVyciwgb2Zmc2V0KSB7XG4gICAgcmV0dXJuIG5ldyBXcml0ZVJlc3VsdChlcnIsIG9mZnNldCk7XG59O1xuXG4vLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuLypqc2hpbnQgbWF4cGFyYW1zOjYqL1xuV3JpdGVSZXN1bHQucG9vbFJhbmdlZEVycm9yID0gZnVuY3Rpb24gcG9vbFJhbmdlZEVycm9yKGRlc3RSZXN1bHQsIGVyciwgc3RhcnQsIGVuZCwgdmFsdWUpIHtcbiAgICBhc3NlcnQodHlwZW9mIGRlc3RSZXN1bHQgPT09ICdvYmplY3QnICYmIGRlc3RSZXN1bHQuY29uc3RydWN0b3IubmFtZSA9PT0gJ1dyaXRlUmVzdWx0Jyk7XG5cbiAgICBlcnIub2ZmZXN0ID0gc3RhcnQ7XG4gICAgZXJyLmVuZE9mZnNldCA9IGVuZDtcbiAgICByZXR1cm4gZGVzdFJlc3VsdC5yZXNldChlcnIsIHN0YXJ0LCB2YWx1ZSk7XG59O1xuXG4vLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuV3JpdGVSZXN1bHQucmFuZ2VkRXJyb3IgPSBmdW5jdGlvbiByYW5nZWRFcnJvcihlcnIsIHN0YXJ0LCBlbmQsIHZhbHVlKSB7XG4gICAgcmV0dXJuIFdyaXRlUmVzdWx0LnBvb2xSYW5nZWRFcnJvcihuZXcgV3JpdGVSZXN1bHQoKSwgc3RhcnQsIGVuZCwgdmFsdWUpO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbldyaXRlUmVzdWx0Lmp1c3QgPSBmdW5jdGlvbiBqdXN0KG9mZnNldCkge1xuICAgIHJldHVybiBuZXcgV3JpdGVSZXN1bHQobnVsbCwgb2Zmc2V0KTtcbn07XG5cblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbldyaXRlUmVzdWx0LnNob3J0RXJyb3IgPSBmdW5jdGlvbiBzaG9ydEVycm9yKGV4cGVjdGVkLCBhY3R1YWwsIG9mZnNldCkge1xuICAgIHJldHVybiBXcml0ZVJlc3VsdC5wb29sU2hvcnRFcnJvcihuZXcgV3JpdGVSZXN1bHQoKSwgZXhwZWN0ZWQsIGFjdHVhbCwgb2Zmc2V0KTtcbn07XG5cbi8vIGlzdGFuYnVsIGlnbm9yZSBuZXh0XG5Xcml0ZVJlc3VsdC5wb29sU2hvcnRFcnJvciA9IGZ1bmN0aW9uIHBvb2xTaG9ydEVycm9yKGRlc3RSZXN1bHQsIGV4cGVjdGVkLCBhY3R1YWwsIG9mZnNldCkge1xuICAgIGFzc2VydCh0eXBlb2YgZGVzdFJlc3VsdCA9PT0gJ29iamVjdCcgJiYgZGVzdFJlc3VsdC5jb25zdHJ1Y3Rvci5uYW1lID09PSAnV3JpdGVSZXN1bHQnKTtcblxuICAgIHJldHVybiBkZXN0UmVzdWx0LnJlc2V0KG5ldyBlcnJvcnMuU2hvcnRCdWZmZXIoe1xuICAgICAgICBleHBlY3RlZDogZXhwZWN0ZWQsXG4gICAgICAgIGFjdHVhbDogYWN0dWFsLFxuICAgICAgICBvZmZzZXQ6IG9mZnNldFxuICAgIH0pLCBvZmZzZXQpO1xufTtcblxuZnVuY3Rpb24gUmVhZFJlc3VsdChlcnIsIG9mZnNldCwgdmFsdWUpIHtcbiAgICB0aGlzLmVyciA9IGVyciB8fCBudWxsO1xuICAgIHRoaXMub2Zmc2V0ID0gb2Zmc2V0IHx8IDA7XG4gICAgLy8gaXN0YW5idWwgaWdub3JlIG5leHRcbiAgICB0aGlzLnZhbHVlID0gdmFsdWUgPT09IHVuZGVmaW5lZCA/IG51bGwgOiB2YWx1ZTtcbn1cblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcblJlYWRSZXN1bHQucHJvdG90eXBlLmNvcHlGcm9tID0gZnVuY3Rpb24gY29weUZyb20oc3JjUmVzdWx0KSB7XG4gICAgdGhpcy5lcnIgPSBzcmNSZXN1bHQuZXJyO1xuICAgIHRoaXMub2Zmc2V0ID0gc3JjUmVzdWx0Lm9mZnNldDtcbiAgICB0aGlzLnZhbHVlID0gc3JjUmVzdWx0LnZhbHVlO1xuICAgIHJldHVybiB0aGlzO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcblJlYWRSZXN1bHQucHJvdG90eXBlLnJlc2V0ID0gZnVuY3Rpb24gcmVzZXQoZXJyLCBvZmZzZXQsIHZhbHVlKSB7XG4gICAgdGhpcy5lcnIgPSBlcnI7XG4gICAgdGhpcy5vZmZzZXQgPSBvZmZzZXQ7XG4gICAgdGhpcy52YWx1ZSA9IHZhbHVlO1xuICAgIHJldHVybiB0aGlzO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcblJlYWRSZXN1bHQuZXJyb3IgPSBmdW5jdGlvbiBlcnJvcihlcnIsIG9mZnNldCwgdmFsdWUpIHtcbiAgICByZXR1cm4gbmV3IFJlYWRSZXN1bHQoZXJyLCBvZmZzZXQsIHZhbHVlKTtcbn07XG5cbi8vIGlzdGFuYnVsIGlnbm9yZSBuZXh0XG5SZWFkUmVzdWx0LnBvb2xSYW5nZWRFcnJvciA9IGZ1bmN0aW9uIHBvb2xSYW5nZWRFcnJvcihkZXN0UmVzdWx0LCBlcnIsIHN0YXJ0LCBlbmQsIHZhbHVlKSB7XG4gICAgYXNzZXJ0KHR5cGVvZiBkZXN0UmVzdWx0ID09PSAnb2JqZWN0JyAmJiBkZXN0UmVzdWx0LmNvbnN0cnVjdG9yLm5hbWUgPT09ICdSZWFkUmVzdWx0Jyk7XG5cbiAgICBlcnIub2ZmZXN0ID0gc3RhcnQ7XG4gICAgZXJyLmVuZE9mZnNldCA9IGVuZDtcbiAgICByZXR1cm4gZGVzdFJlc3VsdC5yZXNldChlcnIsIHN0YXJ0LCB2YWx1ZSk7XG59O1xuXG4vLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuUmVhZFJlc3VsdC5yYW5nZWRFcnJvciA9IGZ1bmN0aW9uIHJhbmdlZEVycm9yKGVyciwgc3RhcnQsIGVuZCwgdmFsdWUpIHtcbiAgICByZXR1cm4gUmVhZFJlc3VsdC5wb29sUmFuZ2VkRXJyb3IobmV3IFJlYWRSZXN1bHQoKSwgZXJyLCBzdGFydCwgZW5kLCB2YWx1ZSk7XG59O1xuXG4vLyBpc3RhbmJ1bCBpZ25vcmUgbmV4dFxuUmVhZFJlc3VsdC5qdXN0ID0gZnVuY3Rpb24ganVzdChvZmZzZXQsIHZhbHVlKSB7XG4gICAgcmV0dXJuIG5ldyBSZWFkUmVzdWx0KG51bGwsIG9mZnNldCwgdmFsdWUpO1xufTtcblxuLy8gaXN0YW5idWwgaWdub3JlIG5leHRcblJlYWRSZXN1bHQuc2hvcnRFcnJvciA9IGZ1bmN0aW9uIHNob3J0RXJyb3IoZGVzdFJlc3VsdCwgZXhwZWN0ZWQsIGFjdHVhbCwgb2Zmc2V0LCBlbmRPZmZzZXQpIHtcbiAgICByZXR1cm4gUmVhZFJlc3VsdC5wb29sU2hvcnRFcnJvcihuZXcgUmVhZFJlc3VsdCgpLCBleHBlY3RlZCwgYWN0dWFsLCBvZmZzZXQsIGVuZE9mZnNldCk7XG59O1xuXG5SZWFkUmVzdWx0LnBvb2xTaG9ydEVycm9yID0gZnVuY3Rpb24gcG9vbFNob3J0RXJyb3IoZGVzdFJlc3VsdCwgZXhwZWN0ZWQsIGFjdHVhbCwgb2Zmc2V0LCBlbmRPZmZzZXQpIHtcbiAgICBhc3NlcnQodHlwZW9mIGRlc3RSZXN1bHQgPT09ICdvYmplY3QnICYmIGRlc3RSZXN1bHQuY29uc3RydWN0b3IubmFtZSA9PT0gJ1JlYWRSZXN1bHQnKTtcbiAgICB2YXIgZXJyO1xuXG4gICAgaWYgKGVuZE9mZnNldCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIGVyciA9IG5ldyBlcnJvcnMuU2hvcnRCdWZmZXIoe1xuICAgICAgICAgICAgZXhwZWN0ZWQ6IGV4cGVjdGVkLFxuICAgICAgICAgICAgYWN0dWFsOiBhY3R1YWwsXG4gICAgICAgICAgICBvZmZzZXQ6IG9mZnNldFxuICAgICAgICB9KTsgXG4gICAgfSBlbHNlIHtcbiAgICAgICAgZXJyID0gbmV3IGVycm9ycy5TaG9ydEJ1ZmZlclJhbmdlZCh7XG4gICAgICAgICAgICBleHBlY3RlZDogZXhwZWN0ZWQsXG4gICAgICAgICAgICBhY3R1YWw6IGFjdHVhbCxcbiAgICAgICAgICAgIG9mZnNldDogb2Zmc2V0LFxuICAgICAgICAgICAgZW5kT2Zmc2V0OiBlbmRPZmZzZXRcbiAgICAgICAgfSk7XG4gICAgfVxuXG4gICAgcmV0dXJuIGRlc3RSZXN1bHQucmVzZXQoZXJyLCBvZmZzZXQpO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/base.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/error_highlighter.js":
/*!*************************************************!*\
  !*** ./node_modules/bufrw/error_highlighter.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\n\nvar color = (__webpack_require__(/*! ansi-color */ \"(action-browser)/./node_modules/ansi-color/lib/ansi-color.js\").set);\nvar stripColor = __webpack_require__(/*! ./lib/strip_color.js */ \"(action-browser)/./node_modules/bufrw/lib/strip_color.js\");\n\nmodule.exports = errorHighlighter;\n\n// istanbul ignore next\nfunction errorHighlighter(err, options) {\n    options = options || {};\n    var errColor = colorer(options.errorColor || 'red+bold');\n\n    var hasOffset = !(err.offset === undefined || err.offset === null);\n    var hasEnd = !(err.endOffset === undefined || err.endOffset === null);\n    var within = false;\n\n    if (!hasOffset) return null;\n    if (hasEnd) {\n        return decorateRangedError;\n    } else {\n        return decorateError;\n    }\n\n    function decorateRangedError(totalOffset, screenOffset, str) {\n        if (totalOffset === err.offset) {\n            within = totalOffset !== err.endOffset-1;\n            return errColor(stripColor(str));\n        } else if (totalOffset === err.endOffset-1) {\n            within = false;\n            return errColor(stripColor(str));\n        } else if (within) {\n            return errColor(stripColor(str));\n        } else {\n            return str;\n        }\n    }\n\n    function decorateError(totalOffset, screenOffset, str) {\n        if (totalOffset === err.offset) {\n            return errColor(stripColor(str));\n        } else {\n            return str;\n        }\n    }\n}\n\n// istanbul ignore next\nfunction colorer(col) {\n    if (typeof col === 'function') return col;\n    return function colorIt(str) {\n        return color(str, col);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/error_highlighter.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/errors.js":
/*!**************************************!*\
  !*** ./node_modules/bufrw/errors.js ***!
  \**************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\n\nvar TypedError = __webpack_require__(/*! error/typed */ \"(action-browser)/./node_modules/error/typed.js\");\nvar WrappedError = __webpack_require__(/*! error/wrapped */ \"(action-browser)/./node_modules/error/wrapped.js\");\n\nmodule.exports.expected = function expected(got, descr) {\n    return module.exports.InvalidArgument({\n        expected: descr,\n        argType: typeof got,\n        argConstructor: got && got.constructor.name\n    });\n};\n\nmodule.exports.BrokenReaderState = TypedError({\n    type: 'bufrw.broken-reader-state',\n    message: 'reader in invalid state {state} expecting {expecting} avail {aval}',\n    state: null,\n    expecting: null,\n    avail: null\n});\n\nmodule.exports.FixedLengthMismatch = TypedError({\n    type: 'bufrw.fixed-length-mismatch',\n    message: 'supplied length {got} mismatches fixed length {expected}',\n    expected: null,\n    got: null\n});\n\nmodule.exports.RangeError = TypedError({\n    type: 'bufrw.range-error',\n    message: 'value {value} out of range, min: {min} max: {max}',\n    value: null,\n    min: null,\n    max: null\n});\n\nmodule.exports.InvalidArgument = TypedError({\n    type: 'bufrw.invalid-argument',\n    message: 'invalid argument, expected {expected}',\n    expected: null,\n    argType: null,\n    argConstructor: null\n});\n\nmodule.exports.ReadInvalidSwitchValue = TypedError({\n    type: 'bufrw.read.invalid-switch-value',\n    message: 'read invalid switch value {value}',\n    value: null\n});\n\nmodule.exports.WriteInvalidSwitchValue = TypedError({\n    type: 'bufrw.write.invalid-switch-value',\n    message: 'write invalid switch value {value}',\n    value: null\n});\n\nmodule.exports.MissingStructField = TypedError({\n    type: 'bufrw.missing.struct-field',\n    message: 'missing field {field} on {struct}',\n    field: null,\n    struct: null\n});\n\nmodule.exports.ShortBuffer = TypedError({\n    type: 'bufrw.short-buffer',\n    message: 'expected at least {expected} bytes, only have {actual} @{offset}',\n    expected: null,\n    actual: null,\n    buffer: null,\n    offset: null\n});\n\nmodule.exports.ShortBufferRanged = TypedError({\n    type: 'bufrw.short-buffer',\n    message: 'expected at least {expected} bytes, only have {actual} @[{offset}:{endOffset}]',\n    expected: null,\n    actual: null,\n    offset: null,\n    endOffset: null\n});\n\nmodule.exports.ShortRead = TypedError({\n    type: 'bufrw.short-read',\n    message: 'short read, {remaining} byte left over after consuming {offset}',\n    remaining: null,\n    buffer: null,\n    offset: null\n});\n\nmodule.exports.ShortWrite = TypedError({\n    type: 'bufrw.short-write',\n    message: 'short write, {remaining} byte left over after writing {offset}',\n    remaining: null,\n    buffer: null,\n    offset: null\n});\n\nmodule.exports.TruncatedRead = TypedError({\n    type: 'bufrw.truncated-read',\n    message: 'read truncated by end of stream with {length} bytes in buffer',\n    length: null,\n    buffer: null,\n    state: null,\n    expecting: null\n});\n\nmodule.exports.UnstableRW = WrappedError({\n    type: 'bufrw.unstable-rw',\n    message: 'Unstable RW error: {origMessage} (other: {otherMessage})',\n    otherMessage: null\n});\n\nmodule.exports.ZeroLengthChunk = TypedError({\n    type: 'bufrw.zero-length-chunk',\n    message: 'zero length chunk encountered'\n});\n\nmodule.exports.classify = classify;\n\nfunction classify(err) {\n    switch (err.type) {\n        case 'bufrw.broken-reader-state':\n        case 'bufrw.unstable-rw':\n            return 'Internal';\n\n        case 'bufrw.invalid-argument':\n        case 'bufrw.read.invalid-switch-value':\n        case 'bufrw.short-buffer':\n        case 'bufrw.short-read':\n        case 'bufrw.truncated-read':\n        case 'bufrw.zero-length-chunk':\n            return 'Read';\n\n        case 'bufrw.fixed-length-mismatch':\n        case 'bufrw.missing.struct-field':\n        case 'bufrw.range-error':\n        case 'bufrw.short-write':\n        case 'bufrw.write.invalid-switch-value':\n            return 'Write';\n\n        // istanbul ignore next\n        default:\n            return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/errors.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/fixed_width_rw.js":
/*!**********************************************!*\
  !*** ./node_modules/bufrw/fixed_width_rw.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\nmodule.exports = FixedWidthRW;\n\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\n\nvar ReadResult = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").ReadResult);\nvar BufferRW = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").BufferRW);\nvar errors = __webpack_require__(/*! ./errors */ \"(action-browser)/./node_modules/bufrw/errors.js\");\n\nfunction FixedWidthRW(length, readFrom, writeInto) {\n    if (!(this instanceof FixedWidthRW)) {\n        return new FixedWidthRW(length, readFrom, writeInto);\n    }\n    this.length = length;\n\n   // BufferRW.call(this);\n}\ninherits(FixedWidthRW, BufferRW);\n\nFixedWidthRW.prototype.poolByteLength = function poolByteLength(destResult, slice) {\n    if (slice.length !== this.length) {\n        return destResult.reset(errors.FixedLengthMismatch({\n            expected: this.length,\n            got: slice.length\n        }), null);\n    } else {\n        return destResult.reset(null, this.length);\n    }\n};\n\nFixedWidthRW.prototype.poolWriteInto = function poolWriteInto(destResult, slice, buffer, offset) {\n    if (slice.length !== this.length) {\n        return destResult.reset(errors.FixedLengthMismatch({\n            expected: this.length,\n            got: slice.length\n        }), offset);\n    }\n    slice.copy(buffer, offset);\n    //return new WriteResult(null, offset + this.length);\n    return destResult.reset(null, offset + this.length);\n};\n\nFixedWidthRW.prototype.poolReadFrom = function poolReadFrom(destResult, buffer, offset) {\n    var end = offset + this.length;\n    if (end > buffer.length) {\n        return ReadResult.poolShortError(destResult, this.length, buffer.length - offset, offset);\n    } else {\n        //var res = new ReadResult(null, end, buffer.slice(offset, end));\n        return destResult.reset(null, end, buffer.slice(offset, end));\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/fixed_width_rw.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/index.js":
/*!*************************************!*\
  !*** ./node_modules/bufrw/index.js ***!
  \*************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nmodule.exports.fromBuffer = __webpack_require__(/*! ./interface */ \"(action-browser)/./node_modules/bufrw/interface.js\").fromBuffer;\nmodule.exports.byteLength = __webpack_require__(/*! ./interface */ \"(action-browser)/./node_modules/bufrw/interface.js\").byteLength;\nmodule.exports.toBuffer = __webpack_require__(/*! ./interface */ \"(action-browser)/./node_modules/bufrw/interface.js\").toBuffer;\nmodule.exports.intoBuffer = __webpack_require__(/*! ./interface */ \"(action-browser)/./node_modules/bufrw/interface.js\").intoBuffer;\n\nmodule.exports.fromBufferTuple = __webpack_require__(/*! ./interface */ \"(action-browser)/./node_modules/bufrw/interface.js\").fromBufferTuple;\nmodule.exports.byteLengthTuple = __webpack_require__(/*! ./interface */ \"(action-browser)/./node_modules/bufrw/interface.js\").byteLengthTuple;\nmodule.exports.toBufferTuple = __webpack_require__(/*! ./interface */ \"(action-browser)/./node_modules/bufrw/interface.js\").toBufferTuple;\nmodule.exports.intoBufferTuple = __webpack_require__(/*! ./interface */ \"(action-browser)/./node_modules/bufrw/interface.js\").intoBufferTuple;\n\nmodule.exports.fromBufferResult = __webpack_require__(/*! ./interface */ \"(action-browser)/./node_modules/bufrw/interface.js\").fromBufferResult;\nmodule.exports.byteLengthResult = __webpack_require__(/*! ./interface */ \"(action-browser)/./node_modules/bufrw/interface.js\").byteLengthResult;\nmodule.exports.toBufferResult = __webpack_require__(/*! ./interface */ \"(action-browser)/./node_modules/bufrw/interface.js\").toBufferResult;\nmodule.exports.intoBufferResult = __webpack_require__(/*! ./interface */ \"(action-browser)/./node_modules/bufrw/interface.js\").intoBufferResult;\n\nmodule.exports.formatError = __webpack_require__(/*! ./interface */ \"(action-browser)/./node_modules/bufrw/interface.js\").formatError;\n\nmodule.exports.Base = __webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").BufferRW; // TODO: align names\nmodule.exports.LengthResult = __webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").LengthResult;\nmodule.exports.WriteResult = __webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").WriteResult;\nmodule.exports.ReadResult = __webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").ReadResult;\n\nvar atoms = __webpack_require__(/*! ./atoms */ \"(action-browser)/./node_modules/bufrw/atoms.js\");\n\nmodule.exports.AtomRW = atoms.AtomRW;\nmodule.exports.Int8 = atoms.Int8;\nmodule.exports.Int16BE = atoms.Int16BE;\nmodule.exports.Int32BE = atoms.Int32BE;\nmodule.exports.Int16LE = atoms.Int16LE;\nmodule.exports.Int32LE = atoms.Int32LE;\nmodule.exports.UInt8 = atoms.UInt8;\nmodule.exports.UInt16BE = atoms.UInt16BE;\nmodule.exports.UInt32BE = atoms.UInt32BE;\nmodule.exports.UInt16LE = atoms.UInt16LE;\nmodule.exports.UInt32LE = atoms.UInt32LE;\nmodule.exports.FloatLE = atoms.FloatLE;\nmodule.exports.FloatBE = atoms.FloatBE;\nmodule.exports.DoubleLE = atoms.DoubleLE;\nmodule.exports.DoubleBE = atoms.DoubleBE;\n\nmodule.exports.Null = __webpack_require__(/*! ./null */ \"(action-browser)/./node_modules/bufrw/null.js\");\nmodule.exports.FixedWidth = __webpack_require__(/*! ./fixed_width_rw */ \"(action-browser)/./node_modules/bufrw/fixed_width_rw.js\");\n\nvar VariableBuffer = __webpack_require__(/*! ./variable_buffer_rw */ \"(action-browser)/./node_modules/bufrw/variable_buffer_rw.js\");\nvar buf1 = VariableBuffer(atoms.UInt8);\nvar buf2 = VariableBuffer(atoms.UInt16BE);\nmodule.exports.buf1 = buf1;\nmodule.exports.buf2 = buf2;\nmodule.exports.VariableBuffer = VariableBuffer;\n\nvar StringRW = __webpack_require__(/*! ./string_rw */ \"(action-browser)/./node_modules/bufrw/string_rw.js\");\nvar varint = __webpack_require__(/*! ./varint */ \"(action-browser)/./node_modules/bufrw/varint.js\");\nvar str1 = StringRW(atoms.UInt8, 'utf8');\nvar str2 = StringRW(atoms.UInt16BE, 'utf8');\nvar strn = StringRW(varint.unsigned, 'utf8');\n\nmodule.exports.str1 = str1;\nmodule.exports.str2 = str2;\nmodule.exports.strn = strn;\nmodule.exports.String = StringRW;\n\nmodule.exports.varint = varint;\n\nmodule.exports.Series = __webpack_require__(/*! ./series */ \"(action-browser)/./node_modules/bufrw/series.js\");\nmodule.exports.Struct = __webpack_require__(/*! ./struct */ \"(action-browser)/./node_modules/bufrw/struct.js\");\nmodule.exports.Switch = __webpack_require__(/*! ./switch */ \"(action-browser)/./node_modules/bufrw/switch.js\");\nmodule.exports.Repeat = __webpack_require__(/*! ./repeat */ \"(action-browser)/./node_modules/bufrw/repeat.js\");\nmodule.exports.Skip = __webpack_require__(/*! ./skip */ \"(action-browser)/./node_modules/bufrw/skip.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/interface.js":
/*!*****************************************!*\
  !*** ./node_modules/bufrw/interface.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\n\nvar hex = __webpack_require__(/*! hexer */ \"(action-browser)/./node_modules/hexer/index.js\");\n\nvar util = __webpack_require__(/*! util */ \"util\");\nvar Result = __webpack_require__(/*! ./result */ \"(action-browser)/./node_modules/bufrw/result.js\");\nvar errors = __webpack_require__(/*! ./errors */ \"(action-browser)/./node_modules/bufrw/errors.js\");\n\nvar AnnotatedBuffer = __webpack_require__(/*! ./annotated_buffer */ \"(action-browser)/./node_modules/bufrw/annotated_buffer.js\");\nvar errorHighlighter = __webpack_require__(/*! ./error_highlighter */ \"(action-browser)/./node_modules/bufrw/error_highlighter.js\");\n\nfunction makeAnnotatedBuffer(buffer, start, clear) {\n    // istanbul ignore if\n    if (start > 0) buffer = buffer.slice(start);\n    // istanbul ignore if\n    if (clear) buffer.fill(0);\n    return new AnnotatedBuffer(buffer);\n}\n\nfunction annotateError(res1, res2, start, annBuf) {\n    // istanbul ignore if\n    if (!res2.err ||\n        res2.offset !== res1.offset - start ||\n        res2.err.type !== res1.err.type ||\n        res2.err.message !== res1.err.message) {\n        res1.err = errors.UnstableRW(res1.err, {\n            otherMessage: res2.err && res2.err.message\n        });\n    } else {\n        res1.err.buffer = annBuf;\n    }\n}\n\nvar emptyBuffer = Buffer.alloc(0);\n\nfunction fromBuffer(rw, buffer, offset) {\n    return fromBufferResult(rw, buffer, offset).toValue();\n}\n\nfunction byteLength(rw, value) {\n    return byteLengthResult(rw, value).toValue();\n}\n\nfunction toBuffer(rw, value) {\n    return toBufferResult(rw, value).toValue();\n}\n\nfunction intoBuffer(rw, buffer, value) {\n    return intoBufferResult(rw, buffer, value).toValue();\n}\n\n// The \"Tuple\" methods are deprecated\n\n/* istanbul ignore next */\nfunction fromBufferTuple(rw, buffer, offset) {\n    return fromBufferResult(rw, buffer, offset).toTuple();\n}\n\n/* istanbul ignore next */\nfunction byteLengthTuple(rw, value) {\n    return byteLengthResult(rw, value).toTuple();\n}\n\n/* istanbul ignore next */\nfunction toBufferTuple(rw, value) {\n    return toBufferResult(rw, value).toTuple();\n}\n\n/* istanbul ignore next */\nfunction intoBufferTuple(rw, buffer, value) {\n    return intoBufferResult(rw, buffer, value).toTuple();\n}\n\nfunction checkAllReadFrom(res, buffer) {\n    if (!res.err && res.offset !== buffer.length) {\n        res.err = errors.ShortRead({\n            remaining: buffer.length - res.offset,\n            buffer: buffer,\n            offset: res.offset\n        });\n    }\n    return res;\n}\n\nfunction genericResult(err, value, buffer, offset) {\n    if (err) {\n        if (err.offset === undefined) err.offset = offset;\n        if (err.buffer === undefined) err.buffer = buffer;\n    }\n    return new Result(err, value);\n}\n\nfunction fromBufferResult(rw, buffer, offset) {\n    var start = offset || 0;\n    var res = rw.readFrom(buffer, start);\n    res = checkAllReadFrom(res, buffer);\n    if (res.err) {\n        var annBuf = makeAnnotatedBuffer(buffer, start, false);\n        var res2 = rw.readFrom(annBuf, 0);\n        res2 = checkAllReadFrom(res2, buffer);\n        annotateError(res, res2, start, annBuf);\n    }\n    return genericResult(res.err, res.value, buffer, res.offset);\n}\n\nfunction byteLengthResult(rw, value) {\n    var lenRes = rw.byteLength(value);\n    if (lenRes.err) return new Result(lenRes.err, 0);\n    else return new Result(null, lenRes.length);\n}\n\nfunction toBufferResult(rw, value) {\n    var lenRes = rw.byteLength(value);\n    if (lenRes.err) return new Result(lenRes.err, emptyBuffer);\n    var length = lenRes.length;\n    var buffer = Buffer.alloc(length);\n    return intoBufferResult(rw, buffer, value);\n}\n\nfunction checkAllWroteOver(res, buffer) {\n    if (!res.err && res.offset !== buffer.length) {\n        res.err = errors.ShortWrite({\n            remaining: buffer.length - res.offset,\n            buffer: buffer,\n            offset: res.offset\n        });\n    }\n    return res;\n}\n\nfunction intoBufferResult(rw, buffer, value) {\n    var res = rw.writeInto(value, buffer, 0);\n    res = checkAllWroteOver(res, buffer);\n    return genericResult(res.err, buffer, buffer, res.offset);\n}\n\n// istanbul ignore next TODO\nfunction formatError(err, options) {\n    options = options || {};\n    var name = err.name || err.constructor.name;\n    var str = util.format('%s: %s\\n', name, err.message);\n    if (err.buffer && err.buffer.hexdump) {\n        str += err.buffer.hexdump({\n            colored: options.color,\n            boldStart: false,\n            highlight: options.color ? errorHighlighter(err, options) : null\n        });\n    } else if (Buffer.isBuffer(err.buffer)) {\n        if (options.color) {\n            str += formatBufferColored(err, options);\n        } else {\n            str += formatBufferUncolored(err, options);\n        }\n    }\n    return str;\n}\n\n// istanbul ignore next TODO\nfunction formatBufferColored(err, options) {\n    // istanbul ignore else\n    if (!hex) {\n        return err.buffer.toString('hex');\n    }\n\n    options = options || {};\n    var opts = options.hexerOptions ? Object.create(options.hexerOptions) : {};\n    if (opts.colored === undefined) {\n        opts.colored = true;\n    }\n    var highlight = errorHighlighter(err, options);\n    opts.decorateHexen = highlight;\n    opts.decorateHuman = highlight;\n    return hex(err.buffer, opts);\n}\n\n// istanbul ignore next TODO\nfunction formatBufferUncolored(err, options) {\n    // istanbul ignore else\n    if (!hex) {\n        return err.buffer.toString('hex');\n    }\n\n    options = options || {};\n\n    var hasOffset = !(err.offset === undefined || err.offset === null);\n    var hasEnd = !(err.endOffset === undefined || err.endOffset === null);\n    var markStart = options.markStart || '>';\n    var markEnd = options.markEnd || '<';\n    var accum = 0;\n\n    var opts = options.hexerOptions ? Object.create(options.hexerOptions) : {};\n    if (hasOffset) {\n        opts.groupSeparator = '';\n        if (hasEnd) {\n            opts.decorateHexen = decorateRangedError;\n        } else {\n            opts.decorateHexen = decorateError;\n        }\n    }\n    return hex(err.buffer, opts);\n\n    // TODO: suspected broken across lines, should either test and complete or\n    // use some sort of alternate notation such as interstitial lines\n    function decorateRangedError(totalOffset, screenOffset, hexen) {\n        var s;\n        if (totalOffset === err.offset) {\n            accum = 1;\n            s = markStart + hexen;\n            if (totalOffset === err.endOffset-1) {\n                s += markEnd;\n                accum = 0;\n            } else {\n                s = ' ' + s;\n            }\n            return s;\n        } else if (totalOffset === err.endOffset-1) {\n            s = hexen + markEnd;\n            while (accum-- > 0) s += ' ';\n            accum = 0;\n            return s;\n        } else if (accum) {\n            accum += 2;\n            return hexen;\n        } else {\n            return ' ' + hexen + ' ';\n        }\n    }\n\n    function decorateError(totalOffset, screenOffset, hexen) {\n        if (totalOffset === err.offset) {\n            return markStart + hexen + markEnd;\n        } else {\n            return ' ' + hexen + ' ';\n        }\n    }\n}\n\nmodule.exports.fromBuffer = fromBuffer;\nmodule.exports.byteLength = byteLength;\nmodule.exports.toBuffer = toBuffer;\nmodule.exports.intoBuffer = intoBuffer;\nmodule.exports.formatError = formatError;\n\nmodule.exports.fromBufferTuple = fromBufferTuple;\nmodule.exports.byteLengthTuple = byteLengthTuple;\nmodule.exports.toBufferTuple = toBufferTuple;\nmodule.exports.intoBufferTuple = intoBufferTuple;\n\nmodule.exports.fromBufferResult = fromBufferResult;\nmodule.exports.byteLengthResult = byteLengthResult;\nmodule.exports.toBufferResult = toBufferResult;\nmodule.exports.intoBufferResult = intoBufferResult;\n\nmodule.exports.makeAnnotatedBuffer = makeAnnotatedBuffer;\nmodule.exports.checkAllReadFrom = checkAllReadFrom;\nmodule.exports.annotateError = annotateError;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/interface.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/lib/strip_color.js":
/*!***********************************************!*\
  !*** ./node_modules/bufrw/lib/strip_color.js ***!
  \***********************************************/
/***/ ((module) => {

"use strict";
eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\n\nmodule.exports = stripColor;\n\n// istanbul ignore next\nfunction stripColor(str) {\n    while (true) {\n        var i = str.indexOf('\\x1b[');\n        if (i < 0) return str;\n        var j = str.indexOf('m', i);\n        if (j < 0) return str;\n        str = str.slice(0, i) + str.slice(j + 1);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/lib/strip_color.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/null.js":
/*!************************************!*\
  !*** ./node_modules/bufrw/null.js ***!
  \************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nvar AtomRW = (__webpack_require__(/*! ./atoms */ \"(action-browser)/./node_modules/bufrw/atoms.js\").AtomRW);\n\nfunction nullWriteInto(destResult, val, buffer, offset) {\n    //return new WriteResult(null, offset);\n    return destResult.reset(null, offset);\n}\n\nfunction nullReadFrom(destResult, buffer, offset) {\n    //return new ReadResult(null, offset, null);\n    return destResult.reset(null, offset, null);\n}\n\nvar NullRW = AtomRW(0, nullReadFrom, nullWriteInto);\n\nmodule.exports = NullRW;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/null.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/repeat.js":
/*!**************************************!*\
  !*** ./node_modules/bufrw/repeat.js ***!
  \**************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nmodule.exports = RepeatRW;\n\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\n\nvar BufferRW = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").BufferRW);\nvar errors = __webpack_require__(/*! ./errors */ \"(action-browser)/./node_modules/bufrw/errors.js\");\n\nfunction RepeatRW(countrw, repeatedrw) {\n    if (!(this instanceof RepeatRW)) {\n        return new RepeatRW(countrw, repeatedrw);\n    }\n    this.countrw = countrw;\n    this.repeatedrw = repeatedrw;\n\n    BufferRW.call(this);\n}\ninherits(RepeatRW, BufferRW);\n\nRepeatRW.prototype.poolByteLength = function poolByteLength(destResult, values) {\n    if (!Array.isArray(values)) {\n        return destResult.reset(errors.expected(values, 'an array'));\n    }\n    var res = this.countrw.poolByteLength(destResult, values.length);\n    if (res.err) return res;\n    var length = res.length;\n    for (var i = 0; i < values.length; i++) {\n        var partres = this.repeatedrw.poolByteLength(destResult, values[i]);\n        if (partres.err) return partres;\n        length += res.length;\n    }\n    return destResult.reset(null, length);\n};\n\nRepeatRW.prototype.poolWriteInto = function poolWriteInto(destResult, values, buffer, offset) {\n    if (!Array.isArray(values)) {\n        return destResult.reset(errors.expected(values, 'an array'), offset);\n    }\n    var res = this.countrw.poolWriteInto(destResult, values.length, buffer, offset);\n    if (res.err) return res;\n    offset = res.offset;\n    for (var i = 0; i < values.length; i++) {\n        res = this.repeatedrw.poolWriteInto(destResult, values[i], buffer, offset);\n        if (res.err) return res;\n        offset = res.offset;\n    }\n    return res;\n};\n\nRepeatRW.prototype.poolReadFrom = function poolReadFrom(destResult, buffer, offset) {\n    var res = this.countrw.poolReadFrom(destResult, buffer, offset);\n    if (res.err) return res;\n    offset = res.offset;\n    var count = res.value;\n    var values = new Array(count);\n    for (var i = 0; i < count; i++) {\n        res = this.repeatedrw.poolReadFrom(destResult, buffer, offset);\n        if (res.err) return res;\n        offset = res.offset;\n\n        if (Array.isArray(res.value)) values[i] = res.value.slice(0);\n        else if (typeof res.value === 'object') values[i] = shallowCopy(res.value);\n        else values[i] = res.value;\n    }\n    return destResult.reset(null, offset, values);\n};\n\nfunction shallowCopy(obj) {\n    var keys = Object.keys(obj);\n    var i;\n    var dest = {};\n    for (i = 0; i < keys.length; i++) {\n        dest[keys[i]] = obj[keys[i]];\n    }\n    return dest;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/repeat.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/result.js":
/*!**************************************!*\
  !*** ./node_modules/bufrw/result.js ***!
  \**************************************/
/***/ ((module) => {

eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nmodule.exports = Result;\n\nfunction Result(err, value) {\n    this.err = err || null;\n    this.value = value;\n}\n\nResult.prototype.toValue = function toValue() {\n    if (this.err) {\n        throw this.err;\n    } else {\n        return this.value;\n    }\n};\n\n/* istanbul ignore next */\nResult.prototype.toCallback = function toCallback(callback) {\n    callback(this.err, this.value);\n};\n\n// XXX to be phased out of use in favor of using Result return values.\n/* istanbul ignore next */\nResult.prototype.toTuple = function toTuple() {\n    return [this.err, this.value];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/result.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/series.js":
/*!**************************************!*\
  !*** ./node_modules/bufrw/series.js ***!
  \**************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nmodule.exports = SeriesRW;\n\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\n\nvar ReadResult = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").ReadResult);\nvar BufferRW = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").BufferRW);\nvar errors = __webpack_require__(/*! ./errors */ \"(action-browser)/./node_modules/bufrw/errors.js\");\n\nfunction SeriesRW(rws) {\n    if (!Array.isArray(rws) || arguments.length > 1) {\n        rws = Array.prototype.slice.call(arguments);\n    }\n    if (!(this instanceof SeriesRW)) {\n        return new SeriesRW(rws);\n    }\n    this.rws = rws;\n    BufferRW.call(this);\n}\ninherits(SeriesRW, BufferRW);\n\nSeriesRW.prototype.poolByteLength = function poolByteLength(destResult, values) {\n    if (!Array.isArray(values) && values !== null) {\n        return destResult.reset(errors.expected(values, 'an array or null'));\n    }\n    var length = 0;\n    for (var i = 0; i < this.rws.length; i++) {\n        this.rws[i].poolByteLength(destResult, values && values[i]);\n        if (destResult.err) return destResult;\n        length += destResult.length;\n    }\n    return destResult.reset(null, length);\n};\n\nSeriesRW.prototype.poolWriteInto = function poolWriteInto(destResult, values, buffer, offset) {\n    if (!Array.isArray(values) && values !== null) {\n        return destResult.reset(errors.expected(values, 'an array or null'), offset);\n    }\n    for (var i = 0; i < this.rws.length; i++) {\n        this.rws[i].poolWriteInto(destResult, values && values[i], buffer, offset);\n        if (destResult.err) return destResult;\n        offset = destResult.offset;\n    }\n    return destResult;\n};\n\nvar readResult = new ReadResult();\nSeriesRW.prototype.poolReadFrom = function poolReadFrom(destResult, buffer, offset) {\n    // The prior value cannot be reused, even if it is already an array of the right size.\n    // The array may have been captured by reference by the prior consumer.\n    var values = new Array(this.rws.length);\n    for (var i = 0; i < this.rws.length; i++) {\n        this.rws[i].poolReadFrom(readResult, buffer, offset);\n        if (readResult.err) return destResult.copyFrom(readResult);\n        offset = readResult.offset;\n        values[i] = readResult.value;\n    }\n    // The values must be assigned to the result last so reading a series is reentrant.\n    destResult.value = values;\n    return destResult.reset(null, offset, destResult.value);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/series.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/skip.js":
/*!************************************!*\
  !*** ./node_modules/bufrw/skip.js ***!
  \************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nmodule.exports = SkipRW;\n\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\n\nvar ReadResult = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").ReadResult);\nvar FixedWidthRW = __webpack_require__(/*! ./fixed_width_rw */ \"(action-browser)/./node_modules/bufrw/fixed_width_rw.js\");\n\nfunction SkipRW(length, fill) {\n    if (!(this instanceof SkipRW)) {\n        return new SkipRW(length, fill);\n    }\n    this.fill = fill || 0;\n    FixedWidthRW.call(this, length);\n}\ninherits(SkipRW, FixedWidthRW);\n\nSkipRW.prototype.poolByteLength = function poolByteLength(destResult) {\n    return destResult.reset(null, this.length);\n};\n\nSkipRW.prototype.poolWriteInto = function poolWriteInto(destResult, val, buffer, offset) {\n    var end = offset + this.length;\n    buffer.fill(this.fill, offset, end);\n    return destResult.reset(null, end);\n};\n\nSkipRW.prototype.poolReadFrom = function poolReadFrom(destResult, buffer, offset) {\n    var end = offset + this.length;\n    if (end > buffer.length) {\n        return ReadResult.poolShortError(destResult, this.length, buffer.length - offset, offset);\n    } else {\n        return destResult.reset(null, end, null);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9idWZydy9za2lwLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBLGVBQWUsa0RBQXdCOztBQUV2QyxpQkFBaUIsK0ZBQTRCO0FBQzdDLG1CQUFtQixtQkFBTyxDQUFDLGlGQUFrQjs7QUFFN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGJ1ZnJ3XFxza2lwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgMjAxNSBVYmVyIFRlY2hub2xvZ2llcywgSW5jLlxuLy9cbi8vIFBlcm1pc3Npb24gaXMgaGVyZWJ5IGdyYW50ZWQsIGZyZWUgb2YgY2hhcmdlLCB0byBhbnkgcGVyc29uIG9idGFpbmluZyBhIGNvcHlcbi8vIG9mIHRoaXMgc29mdHdhcmUgYW5kIGFzc29jaWF0ZWQgZG9jdW1lbnRhdGlvbiBmaWxlcyAodGhlIFwiU29mdHdhcmVcIiksIHRvIGRlYWxcbi8vIGluIHRoZSBTb2Z0d2FyZSB3aXRob3V0IHJlc3RyaWN0aW9uLCBpbmNsdWRpbmcgd2l0aG91dCBsaW1pdGF0aW9uIHRoZSByaWdodHNcbi8vIHRvIHVzZSwgY29weSwgbW9kaWZ5LCBtZXJnZSwgcHVibGlzaCwgZGlzdHJpYnV0ZSwgc3VibGljZW5zZSwgYW5kL29yIHNlbGxcbi8vIGNvcGllcyBvZiB0aGUgU29mdHdhcmUsIGFuZCB0byBwZXJtaXQgcGVyc29ucyB0byB3aG9tIHRoZSBTb2Z0d2FyZSBpc1xuLy8gZnVybmlzaGVkIHRvIGRvIHNvLCBzdWJqZWN0IHRvIHRoZSBmb2xsb3dpbmcgY29uZGl0aW9uczpcbi8vXG4vLyBUaGUgYWJvdmUgY29weXJpZ2h0IG5vdGljZSBhbmQgdGhpcyBwZXJtaXNzaW9uIG5vdGljZSBzaGFsbCBiZSBpbmNsdWRlZCBpblxuLy8gYWxsIGNvcGllcyBvciBzdWJzdGFudGlhbCBwb3J0aW9ucyBvZiB0aGUgU29mdHdhcmUuXG4vL1xuLy8gVEhFIFNPRlRXQVJFIElTIFBST1ZJREVEIFwiQVMgSVNcIiwgV0lUSE9VVCBXQVJSQU5UWSBPRiBBTlkgS0lORCwgRVhQUkVTUyBPUlxuLy8gSU1QTElFRCwgSU5DTFVESU5HIEJVVCBOT1QgTElNSVRFRCBUTyBUSEUgV0FSUkFOVElFUyBPRiBNRVJDSEFOVEFCSUxJVFksXG4vLyBGSVRORVNTIEZPUiBBIFBBUlRJQ1VMQVIgUFVSUE9TRSBBTkQgTk9OSU5GUklOR0VNRU5ULiBJTiBOTyBFVkVOVCBTSEFMTCBUSEVcbi8vIEFVVEhPUlMgT1IgQ09QWVJJR0hUIEhPTERFUlMgQkUgTElBQkxFIEZPUiBBTlkgQ0xBSU0sIERBTUFHRVMgT1IgT1RIRVJcbi8vIExJQUJJTElUWSwgV0hFVEhFUiBJTiBBTiBBQ1RJT04gT0YgQ09OVFJBQ1QsIFRPUlQgT1IgT1RIRVJXSVNFLCBBUklTSU5HIEZST00sXG4vLyBPVVQgT0YgT1IgSU4gQ09OTkVDVElPTiBXSVRIIFRIRSBTT0ZUV0FSRSBPUiBUSEUgVVNFIE9SIE9USEVSIERFQUxJTkdTIElOXG4vLyBUSEUgU09GVFdBUkUuXG5cbm1vZHVsZS5leHBvcnRzID0gU2tpcFJXO1xuXG52YXIgaW5oZXJpdHMgPSByZXF1aXJlKCd1dGlsJykuaW5oZXJpdHM7XG5cbnZhciBSZWFkUmVzdWx0ID0gcmVxdWlyZSgnLi9iYXNlJykuUmVhZFJlc3VsdDtcbnZhciBGaXhlZFdpZHRoUlcgPSByZXF1aXJlKCcuL2ZpeGVkX3dpZHRoX3J3Jyk7XG5cbmZ1bmN0aW9uIFNraXBSVyhsZW5ndGgsIGZpbGwpIHtcbiAgICBpZiAoISh0aGlzIGluc3RhbmNlb2YgU2tpcFJXKSkge1xuICAgICAgICByZXR1cm4gbmV3IFNraXBSVyhsZW5ndGgsIGZpbGwpO1xuICAgIH1cbiAgICB0aGlzLmZpbGwgPSBmaWxsIHx8IDA7XG4gICAgRml4ZWRXaWR0aFJXLmNhbGwodGhpcywgbGVuZ3RoKTtcbn1cbmluaGVyaXRzKFNraXBSVywgRml4ZWRXaWR0aFJXKTtcblxuU2tpcFJXLnByb3RvdHlwZS5wb29sQnl0ZUxlbmd0aCA9IGZ1bmN0aW9uIHBvb2xCeXRlTGVuZ3RoKGRlc3RSZXN1bHQpIHtcbiAgICByZXR1cm4gZGVzdFJlc3VsdC5yZXNldChudWxsLCB0aGlzLmxlbmd0aCk7XG59O1xuXG5Ta2lwUlcucHJvdG90eXBlLnBvb2xXcml0ZUludG8gPSBmdW5jdGlvbiBwb29sV3JpdGVJbnRvKGRlc3RSZXN1bHQsIHZhbCwgYnVmZmVyLCBvZmZzZXQpIHtcbiAgICB2YXIgZW5kID0gb2Zmc2V0ICsgdGhpcy5sZW5ndGg7XG4gICAgYnVmZmVyLmZpbGwodGhpcy5maWxsLCBvZmZzZXQsIGVuZCk7XG4gICAgcmV0dXJuIGRlc3RSZXN1bHQucmVzZXQobnVsbCwgZW5kKTtcbn07XG5cblNraXBSVy5wcm90b3R5cGUucG9vbFJlYWRGcm9tID0gZnVuY3Rpb24gcG9vbFJlYWRGcm9tKGRlc3RSZXN1bHQsIGJ1ZmZlciwgb2Zmc2V0KSB7XG4gICAgdmFyIGVuZCA9IG9mZnNldCArIHRoaXMubGVuZ3RoO1xuICAgIGlmIChlbmQgPiBidWZmZXIubGVuZ3RoKSB7XG4gICAgICAgIHJldHVybiBSZWFkUmVzdWx0LnBvb2xTaG9ydEVycm9yKGRlc3RSZXN1bHQsIHRoaXMubGVuZ3RoLCBidWZmZXIubGVuZ3RoIC0gb2Zmc2V0LCBvZmZzZXQpO1xuICAgIH0gZWxzZSB7XG4gICAgICAgIHJldHVybiBkZXN0UmVzdWx0LnJlc2V0KG51bGwsIGVuZCwgbnVsbCk7XG4gICAgfVxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/skip.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/string_rw.js":
/*!*****************************************!*\
  !*** ./node_modules/bufrw/string_rw.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\nmodule.exports = StringRW;\n\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\n\nvar ReadResult = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").ReadResult);\nvar errors = __webpack_require__(/*! ./errors */ \"(action-browser)/./node_modules/bufrw/errors.js\");\nvar BufferRW = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").BufferRW);\n\nfunction StringRW(sizerw, encoding) {\n    if (!(this instanceof StringRW)) {\n        return new StringRW(sizerw, encoding);\n    }\n    this.sizerw = sizerw;\n    this.encoding = encoding || 'utf8';\n    if (!this.sizerw.width) {\n        this.poolWriteInto = this.poolWriteVariableWidthInto;\n    } else {\n        this.poolWriteInto = this.poolWriteFixedWidthInto;\n    }\n\n    BufferRW.call(this);\n}\ninherits(StringRW, BufferRW);\n\nStringRW.prototype.poolByteLength = function poolByteLength(destResult, str) {\n    var length = 0;\n    if (typeof str === 'string') {\n        length = Buffer.byteLength(str, this.encoding);\n    } else if (str !== null && str !== undefined) {\n        return destResult.reset(errors.expected(str, 'string, null, or undefined'), null);\n    }\n    this.sizerw.poolByteLength(destResult, length);\n    if (destResult.err) return destResult;\n    return destResult.reset(null, destResult.length + length);\n};\n\nStringRW.prototype.poolWriteFixedWidthInto = \nfunction poolWriteFixedWidthInto(destResult, str, buffer, offset) {\n    var start = offset + this.sizerw.width;\n    var length = 0;\n    if (typeof str === 'string') {\n        length = buffer.write(str, start, this.encoding);\n    } else if (str !== null && str !== undefined) {\n        return destResult.reset(errors.expected(str, 'string, null, or undefined'), offset);\n    }\n    this.sizerw.poolWriteInto(destResult, length, buffer, offset);\n    // istanbul ignore if\n    if (destResult.err) return destResult;\n    return destResult.reset(null, start + length);\n};\n\nStringRW.prototype.poolWriteVariableWidthInto = \nfunction poolWriteVariableWidthInto(destResult, str, buffer, offset) {\n    var size = 0;\n    if (typeof str === 'string') {\n        size = Buffer.byteLength(str, this.encoding);\n    } else if (str !== null && str !== undefined) {\n        return destResult.reset(errors.expected(str, 'string, null, or undefined'), offset);\n    }\n    var res = this.sizerw.poolWriteInto(destResult, size, buffer, offset);\n    if (res.err) return res;\n    offset = res.offset;\n    if (typeof str === 'string') {\n        res.offset += buffer.write(str, offset, this.encoding);\n    }\n    return res;\n};\n\nStringRW.prototype.poolReadFrom = function poolReadFrom(destResult, buffer, offset) {\n    var res = this.sizerw.poolReadFrom(destResult, buffer, offset);\n    if (res.err) return res;\n    var length = res.value;\n    var remain = buffer.length - res.offset;\n    if (remain < length) {\n        return ReadResult.poolShortError(destResult, length, remain, offset, res.offset);\n    } else {\n        offset = res.offset;\n        var end = offset + length;\n        var str = buffer.toString(this.encoding, offset, end);\n        return destResult.reset(null, end, str);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9idWZydy9zdHJpbmdfcncuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDYTs7QUFFYjs7QUFFQSxlQUFlLGtEQUF3Qjs7QUFFdkMsaUJBQWlCLCtGQUE0QjtBQUM3QyxhQUFhLG1CQUFPLENBQUMsaUVBQVU7QUFDL0IsZUFBZSw2RkFBMEI7O0FBRXpDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcYnVmcndcXHN0cmluZ19ydy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgKGMpIDIwMTUgVWJlciBUZWNobm9sb2dpZXMsIEluYy5cbi8vXG4vLyBQZXJtaXNzaW9uIGlzIGhlcmVieSBncmFudGVkLCBmcmVlIG9mIGNoYXJnZSwgdG8gYW55IHBlcnNvbiBvYnRhaW5pbmcgYSBjb3B5XG4vLyBvZiB0aGlzIHNvZnR3YXJlIGFuZCBhc3NvY2lhdGVkIGRvY3VtZW50YXRpb24gZmlsZXMgKHRoZSBcIlNvZnR3YXJlXCIpLCB0byBkZWFsXG4vLyBpbiB0aGUgU29mdHdhcmUgd2l0aG91dCByZXN0cmljdGlvbiwgaW5jbHVkaW5nIHdpdGhvdXQgbGltaXRhdGlvbiB0aGUgcmlnaHRzXG4vLyB0byB1c2UsIGNvcHksIG1vZGlmeSwgbWVyZ2UsIHB1Ymxpc2gsIGRpc3RyaWJ1dGUsIHN1YmxpY2Vuc2UsIGFuZC9vciBzZWxsXG4vLyBjb3BpZXMgb2YgdGhlIFNvZnR3YXJlLCBhbmQgdG8gcGVybWl0IHBlcnNvbnMgdG8gd2hvbSB0aGUgU29mdHdhcmUgaXNcbi8vIGZ1cm5pc2hlZCB0byBkbyBzbywgc3ViamVjdCB0byB0aGUgZm9sbG93aW5nIGNvbmRpdGlvbnM6XG4vL1xuLy8gVGhlIGFib3ZlIGNvcHlyaWdodCBub3RpY2UgYW5kIHRoaXMgcGVybWlzc2lvbiBub3RpY2Ugc2hhbGwgYmUgaW5jbHVkZWQgaW5cbi8vIGFsbCBjb3BpZXMgb3Igc3Vic3RhbnRpYWwgcG9ydGlvbnMgb2YgdGhlIFNvZnR3YXJlLlxuLy9cbi8vIFRIRSBTT0ZUV0FSRSBJUyBQUk9WSURFRCBcIkFTIElTXCIsIFdJVEhPVVQgV0FSUkFOVFkgT0YgQU5ZIEtJTkQsIEVYUFJFU1MgT1Jcbi8vIElNUExJRUQsIElOQ0xVRElORyBCVVQgTk9UIExJTUlURUQgVE8gVEhFIFdBUlJBTlRJRVMgT0YgTUVSQ0hBTlRBQklMSVRZLFxuLy8gRklUTkVTUyBGT1IgQSBQQVJUSUNVTEFSIFBVUlBPU0UgQU5EIE5PTklORlJJTkdFTUVOVC4gSU4gTk8gRVZFTlQgU0hBTEwgVEhFXG4vLyBBVVRIT1JTIE9SIENPUFlSSUdIVCBIT0xERVJTIEJFIExJQUJMRSBGT1IgQU5ZIENMQUlNLCBEQU1BR0VTIE9SIE9USEVSXG4vLyBMSUFCSUxJVFksIFdIRVRIRVIgSU4gQU4gQUNUSU9OIE9GIENPTlRSQUNULCBUT1JUIE9SIE9USEVSV0lTRSwgQVJJU0lORyBGUk9NLFxuLy8gT1VUIE9GIE9SIElOIENPTk5FQ1RJT04gV0lUSCBUSEUgU09GVFdBUkUgT1IgVEhFIFVTRSBPUiBPVEhFUiBERUFMSU5HUyBJTlxuLy8gVEhFIFNPRlRXQVJFLlxuJ3VzZSBzdHJpY3QnO1xuXG5tb2R1bGUuZXhwb3J0cyA9IFN0cmluZ1JXO1xuXG52YXIgaW5oZXJpdHMgPSByZXF1aXJlKCd1dGlsJykuaW5oZXJpdHM7XG5cbnZhciBSZWFkUmVzdWx0ID0gcmVxdWlyZSgnLi9iYXNlJykuUmVhZFJlc3VsdDtcbnZhciBlcnJvcnMgPSByZXF1aXJlKCcuL2Vycm9ycycpO1xudmFyIEJ1ZmZlclJXID0gcmVxdWlyZSgnLi9iYXNlJykuQnVmZmVyUlc7XG5cbmZ1bmN0aW9uIFN0cmluZ1JXKHNpemVydywgZW5jb2RpbmcpIHtcbiAgICBpZiAoISh0aGlzIGluc3RhbmNlb2YgU3RyaW5nUlcpKSB7XG4gICAgICAgIHJldHVybiBuZXcgU3RyaW5nUlcoc2l6ZXJ3LCBlbmNvZGluZyk7XG4gICAgfVxuICAgIHRoaXMuc2l6ZXJ3ID0gc2l6ZXJ3O1xuICAgIHRoaXMuZW5jb2RpbmcgPSBlbmNvZGluZyB8fCAndXRmOCc7XG4gICAgaWYgKCF0aGlzLnNpemVydy53aWR0aCkge1xuICAgICAgICB0aGlzLnBvb2xXcml0ZUludG8gPSB0aGlzLnBvb2xXcml0ZVZhcmlhYmxlV2lkdGhJbnRvO1xuICAgIH0gZWxzZSB7XG4gICAgICAgIHRoaXMucG9vbFdyaXRlSW50byA9IHRoaXMucG9vbFdyaXRlRml4ZWRXaWR0aEludG87XG4gICAgfVxuXG4gICAgQnVmZmVyUlcuY2FsbCh0aGlzKTtcbn1cbmluaGVyaXRzKFN0cmluZ1JXLCBCdWZmZXJSVyk7XG5cblN0cmluZ1JXLnByb3RvdHlwZS5wb29sQnl0ZUxlbmd0aCA9IGZ1bmN0aW9uIHBvb2xCeXRlTGVuZ3RoKGRlc3RSZXN1bHQsIHN0cikge1xuICAgIHZhciBsZW5ndGggPSAwO1xuICAgIGlmICh0eXBlb2Ygc3RyID09PSAnc3RyaW5nJykge1xuICAgICAgICBsZW5ndGggPSBCdWZmZXIuYnl0ZUxlbmd0aChzdHIsIHRoaXMuZW5jb2RpbmcpO1xuICAgIH0gZWxzZSBpZiAoc3RyICE9PSBudWxsICYmIHN0ciAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHJldHVybiBkZXN0UmVzdWx0LnJlc2V0KGVycm9ycy5leHBlY3RlZChzdHIsICdzdHJpbmcsIG51bGwsIG9yIHVuZGVmaW5lZCcpLCBudWxsKTtcbiAgICB9XG4gICAgdGhpcy5zaXplcncucG9vbEJ5dGVMZW5ndGgoZGVzdFJlc3VsdCwgbGVuZ3RoKTtcbiAgICBpZiAoZGVzdFJlc3VsdC5lcnIpIHJldHVybiBkZXN0UmVzdWx0O1xuICAgIHJldHVybiBkZXN0UmVzdWx0LnJlc2V0KG51bGwsIGRlc3RSZXN1bHQubGVuZ3RoICsgbGVuZ3RoKTtcbn07XG5cblN0cmluZ1JXLnByb3RvdHlwZS5wb29sV3JpdGVGaXhlZFdpZHRoSW50byA9IFxuZnVuY3Rpb24gcG9vbFdyaXRlRml4ZWRXaWR0aEludG8oZGVzdFJlc3VsdCwgc3RyLCBidWZmZXIsIG9mZnNldCkge1xuICAgIHZhciBzdGFydCA9IG9mZnNldCArIHRoaXMuc2l6ZXJ3LndpZHRoO1xuICAgIHZhciBsZW5ndGggPSAwO1xuICAgIGlmICh0eXBlb2Ygc3RyID09PSAnc3RyaW5nJykge1xuICAgICAgICBsZW5ndGggPSBidWZmZXIud3JpdGUoc3RyLCBzdGFydCwgdGhpcy5lbmNvZGluZyk7XG4gICAgfSBlbHNlIGlmIChzdHIgIT09IG51bGwgJiYgc3RyICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcmV0dXJuIGRlc3RSZXN1bHQucmVzZXQoZXJyb3JzLmV4cGVjdGVkKHN0ciwgJ3N0cmluZywgbnVsbCwgb3IgdW5kZWZpbmVkJyksIG9mZnNldCk7XG4gICAgfVxuICAgIHRoaXMuc2l6ZXJ3LnBvb2xXcml0ZUludG8oZGVzdFJlc3VsdCwgbGVuZ3RoLCBidWZmZXIsIG9mZnNldCk7XG4gICAgLy8gaXN0YW5idWwgaWdub3JlIGlmXG4gICAgaWYgKGRlc3RSZXN1bHQuZXJyKSByZXR1cm4gZGVzdFJlc3VsdDtcbiAgICByZXR1cm4gZGVzdFJlc3VsdC5yZXNldChudWxsLCBzdGFydCArIGxlbmd0aCk7XG59O1xuXG5TdHJpbmdSVy5wcm90b3R5cGUucG9vbFdyaXRlVmFyaWFibGVXaWR0aEludG8gPSBcbmZ1bmN0aW9uIHBvb2xXcml0ZVZhcmlhYmxlV2lkdGhJbnRvKGRlc3RSZXN1bHQsIHN0ciwgYnVmZmVyLCBvZmZzZXQpIHtcbiAgICB2YXIgc2l6ZSA9IDA7XG4gICAgaWYgKHR5cGVvZiBzdHIgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgIHNpemUgPSBCdWZmZXIuYnl0ZUxlbmd0aChzdHIsIHRoaXMuZW5jb2RpbmcpO1xuICAgIH0gZWxzZSBpZiAoc3RyICE9PSBudWxsICYmIHN0ciAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgIHJldHVybiBkZXN0UmVzdWx0LnJlc2V0KGVycm9ycy5leHBlY3RlZChzdHIsICdzdHJpbmcsIG51bGwsIG9yIHVuZGVmaW5lZCcpLCBvZmZzZXQpO1xuICAgIH1cbiAgICB2YXIgcmVzID0gdGhpcy5zaXplcncucG9vbFdyaXRlSW50byhkZXN0UmVzdWx0LCBzaXplLCBidWZmZXIsIG9mZnNldCk7XG4gICAgaWYgKHJlcy5lcnIpIHJldHVybiByZXM7XG4gICAgb2Zmc2V0ID0gcmVzLm9mZnNldDtcbiAgICBpZiAodHlwZW9mIHN0ciA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgcmVzLm9mZnNldCArPSBidWZmZXIud3JpdGUoc3RyLCBvZmZzZXQsIHRoaXMuZW5jb2RpbmcpO1xuICAgIH1cbiAgICByZXR1cm4gcmVzO1xufTtcblxuU3RyaW5nUlcucHJvdG90eXBlLnBvb2xSZWFkRnJvbSA9IGZ1bmN0aW9uIHBvb2xSZWFkRnJvbShkZXN0UmVzdWx0LCBidWZmZXIsIG9mZnNldCkge1xuICAgIHZhciByZXMgPSB0aGlzLnNpemVydy5wb29sUmVhZEZyb20oZGVzdFJlc3VsdCwgYnVmZmVyLCBvZmZzZXQpO1xuICAgIGlmIChyZXMuZXJyKSByZXR1cm4gcmVzO1xuICAgIHZhciBsZW5ndGggPSByZXMudmFsdWU7XG4gICAgdmFyIHJlbWFpbiA9IGJ1ZmZlci5sZW5ndGggLSByZXMub2Zmc2V0O1xuICAgIGlmIChyZW1haW4gPCBsZW5ndGgpIHtcbiAgICAgICAgcmV0dXJuIFJlYWRSZXN1bHQucG9vbFNob3J0RXJyb3IoZGVzdFJlc3VsdCwgbGVuZ3RoLCByZW1haW4sIG9mZnNldCwgcmVzLm9mZnNldCk7XG4gICAgfSBlbHNlIHtcbiAgICAgICAgb2Zmc2V0ID0gcmVzLm9mZnNldDtcbiAgICAgICAgdmFyIGVuZCA9IG9mZnNldCArIGxlbmd0aDtcbiAgICAgICAgdmFyIHN0ciA9IGJ1ZmZlci50b1N0cmluZyh0aGlzLmVuY29kaW5nLCBvZmZzZXQsIGVuZCk7XG4gICAgICAgIHJldHVybiBkZXN0UmVzdWx0LnJlc2V0KG51bGwsIGVuZCwgc3RyKTtcbiAgICB9XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/string_rw.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/struct.js":
/*!**************************************!*\
  !*** ./node_modules/bufrw/struct.js ***!
  \**************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nmodule.exports = StructRW;\n\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\n\nvar ReadResult = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").ReadResult);\nvar BufferRW = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").BufferRW);\nvar errors = __webpack_require__(/*! ./errors */ \"(action-browser)/./node_modules/bufrw/errors.js\");\n\nfunction StructRW(cons, fields, opts) {\n    if (!(this instanceof StructRW)) {\n        return new StructRW(cons, fields);\n    }\n    if (typeof cons === 'object') {\n        fields = cons;\n        cons = null;\n    }\n    var i;\n    opts = opts || {};\n    this.cons = cons || Object;\n    this.fields = [];\n    // TODO: useful to have a StructRWField prototype?\n    if (Array.isArray(fields)) {\n        this.fields.push.apply(this.fields, fields);\n    } else {\n        var fieldNames = Object.keys(fields);\n        for (i = 0; i < fieldNames.length; ++i) {\n            var field = {};\n            field.name = fieldNames[i];\n            field.rw = fields[field.name];\n            this.fields.push(field);\n        }\n    }\n    this.namedFields = {};\n    for (i = 0; i < this.fields.length; ++i) {\n        if (this.fields[i].name) {\n            this.namedFields[this.fields[i].name] = this.fields[i];\n        }\n    }\n}\ninherits(StructRW, BufferRW);\n\nStructRW.prototype.poolByteLength = function poolByteLength(destResult, obj) {\n    var length = 0;\n    for (var i = 0; i < this.fields.length; i++) {\n        var field = this.fields[i];\n\n        if (field.name && !obj.hasOwnProperty(field.name)) {\n            return destResult.reset(errors.MissingStructField({\n                field: field.name,\n                struct: this.cons.name\n            }), null);\n        }\n\n        var value = field.name && obj && obj[field.name];\n        if (field.call) {\n            if (field.call.poolByteLength) {\n                field.call.poolByteLength(destResult, obj);\n            } else if (field.call.byteLength) {\n                var res = field.call.byteLength(obj);\n                destResult.copyFrom(res);\n            } else {\n                continue;\n            }\n        } else {\n            field.rw.poolByteLength(destResult, value);\n        }\n        if (destResult.err) return destResult;\n        length += destResult.length;\n    }\n    return destResult.reset(null, length);\n};\n\nStructRW.prototype.poolWriteInto = function poolWriteInto(destResult, obj, buffer, offset) {\n    destResult.reset(null, offset);\n    for (var i = 0; i < this.fields.length; i++) {\n        var field = this.fields[i];\n\n        if (field.name && !obj.hasOwnProperty(field.name)) {\n            return destResult.reset(errors.MissingStructField({\n                field: field.name,\n                struct: this.cons.name\n            }), null);\n        }\n\n        var value = field.name && obj[field.name];\n        if (field.call) {\n            if (field.call.poolWriteInto) {\n                field.call.poolWriteInto(destResult, obj, buffer, offset);\n            } else if (field.call.writeInto) {\n                var res = field.call.writeInto(obj, buffer, offset);\n                destResult.copyFrom(res);\n            } else {\n                continue;\n            }\n        } else {\n            field.rw.poolWriteInto(destResult, value, buffer, offset);\n        }\n        if (destResult.err) return destResult;\n        offset = destResult.offset;\n    }\n    return destResult;\n};\n\nvar readRes = new ReadResult();\nStructRW.prototype.poolReadFrom = function poolReadFrom(destResult, buffer, offset) {\n    if (typeof destResult.value === 'object' && destResult.value !== null) {\n        if (destResult.value.constructor !== this.cons) {\n            destResult.value = new this.cons();\n        }\n    } else {\n        destResult.value = new this.cons();\n    }\n    for (var i = 0; i < this.fields.length; i++) {\n        var field = this.fields[i];\n        if (field.call) {\n            if (field.call.poolReadFrom) {\n                field.call.poolReadFrom(readRes, destResult.value, buffer, offset);\n            } else if (field.call.readFrom) {\n                var res = field.call.readFrom(destResult.value, buffer, offset);\n                readRes.copyFrom(res);\n            } else {\n                continue;\n            }\n        } else {\n            field.rw.poolReadFrom(readRes, buffer, offset);\n        }\n        if (readRes.err) return destResult.copyFrom(readRes);\n        offset = readRes.offset;\n        if (field.name) {\n            destResult.value[field.name] = readRes.value;\n        }\n    }\n    return destResult.reset(null, offset, destResult.value);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/struct.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/switch.js":
/*!**************************************!*\
  !*** ./node_modules/bufrw/switch.js ***!
  \**************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nmodule.exports = SwitchRW;\n\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\n\nvar BufferRW = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").BufferRW);\nvar errors = __webpack_require__(/*! ./errors */ \"(action-browser)/./node_modules/bufrw/errors.js\");\n\n// TODO: cases should be an es6 map\n\nfunction SwitchRW(valrw, cases, opts) {\n    if (!(this instanceof SwitchRW)) {\n        return new SwitchRW(valrw, cases, opts);\n    }\n    opts = opts || {};\n    this.valrw = valrw;\n    this.cases = cases;\n    this.cons = opts.cons || Pair;\n    this.valKey = opts.valKey || '0';\n    this.dataKey = opts.dataKey || '1';\n    // istanbul ignore if TODO\n    if (opts.structMode) {\n        this.poolReadFrom = this.structReadFrom;\n    }\n\n    BufferRW.call(this);\n}\ninherits(SwitchRW, BufferRW);\n\nSwitchRW.prototype.poolByteLength = function poolByteLength(destResult, obj) {\n    var val = obj[this.valKey];\n    var data = obj[this.dataKey];\n    var datarw = this.cases[val];\n    if (datarw === undefined) {\n        return destResult.reset(errors.WriteInvalidSwitchValue({\n            value: val\n        }));\n    }\n    this.valrw.poolByteLength(destResult, val);\n    if (destResult.err) return destResult;\n    var vallen = destResult.length;\n\n    datarw.poolByteLength(destResult, data);\n    if (destResult.err) return destResult;\n    var caselen = destResult.length;\n\n    return destResult.reset(null, caselen + vallen);\n};\n\nSwitchRW.prototype.poolWriteInto = function poolWriteInto(destResult, obj, buffer, offset) {\n    var val = obj[this.valKey];\n    var data = obj[this.dataKey];\n    var datarw = this.cases[val];\n    if (datarw === undefined) {\n        return destResult.reset(errors.WriteInvalidSwitchValue({\n            value: val\n        }), offset);\n    }\n    var res = this.valrw.poolWriteInto(destResult, val, buffer, offset);\n    if (res.err) return res;\n    res = datarw.poolWriteInto(destResult, data, buffer, res.offset);\n    return res;\n};\n\nSwitchRW.prototype.poolReadFrom = function poolReadFrom(destResult, buffer, offset) {\n    var res = this.valrw.poolReadFrom(destResult, buffer, offset);\n    if (res.err) return res;\n    offset = res.offset;\n    var val = res.value;\n    var datarw = this.cases[val];\n    if (datarw === undefined) {\n        return destResult.reset(errors.ReadInvalidSwitchValue({\n            value: val\n        }), offset);\n    }\n    res = datarw.poolReadFrom(destResult, buffer, offset);\n    if (res.err) return res;\n    offset = res.offset;\n    var data = res.value;\n    var obj = new this.cons(val, data);\n    return destResult.reset(null, offset, obj);\n};\n\n// istanbul ignore next TODO\nSwitchRW.prototype.poolStructReadFrom = \nfunction poolStructReadFrom(destResult, obj, buffer, offset) {\n    var res = this.valrw.poolReadFrom(destResult, buffer, offset);\n    if (res.err) return res;\n    offset = res.offset;\n    var val = res.value;\n    var datarw = this.cases[val];\n    if (datarw === undefined) {\n        return destResult.reset(errors.ReadInvalidSwitchValue({\n            value: val\n        }), offset);\n    }\n    obj[this.valKey] = val;\n    res = datarw.poolReadFrom(destResult, buffer, offset);\n    if (!res.err) {\n        obj[this.dataKey] = res.value;\n    }\n    return res;\n};\n\nfunction Pair(a, b) {\n    Array.call(this);\n    this[0] = a;\n    this[1] = b;\n}\ninherits(Pair, Array);\n\nSwitchRW.Pair = Pair;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/switch.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/variable_buffer_rw.js":
/*!**************************************************!*\
  !*** ./node_modules/bufrw/variable_buffer_rw.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\nmodule.exports = VariableBufferRW;\n\nvar inherits = (__webpack_require__(/*! util */ \"util\").inherits);\n\nvar ReadResult = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").ReadResult);\nvar BufferRW = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").BufferRW);\nvar errors = __webpack_require__(/*! ./errors */ \"(action-browser)/./node_modules/bufrw/errors.js\");\n\nfunction VariableBufferRW(sizerw, lazy) {\n    if (!(this instanceof VariableBufferRW)) {\n        return new VariableBufferRW(sizerw, lazy);\n    }\n    this.sizerw = sizerw;\n    if (lazy) {\n        this.poolReadFrom = this.lazyPoolReadFrom;\n    } else {\n        this.poolReadFrom = this.eagerPoolReadFrom;\n    }\n    BufferRW.call(this);\n}\ninherits(VariableBufferRW, BufferRW);\n\nVariableBufferRW.prototype.poolByteLength = function poolByteLength(destResult, buf) {\n    var length = 0;\n    if (Buffer.isBuffer(buf)) {\n        length = buf.length;\n    } else if (buf === null || buf === undefined) {\n        length = 0;\n    } else {\n        return destResult.reset(errors.expected(buf, 'buffer, null, or undefined'), null);\n    }\n    this.sizerw.poolByteLength(destResult, length);\n    if (destResult.err) return destResult;\n    return destResult.reset(null, destResult.length + length);\n};\n\nVariableBufferRW.prototype.poolWriteInto = function poolWriteInto(destResult, buf, buffer, offset) {\n    var start = offset + this.sizerw.width;\n    var length = 0;\n    if (Buffer.isBuffer(buf)) {\n        length = buf.copy(buffer, start);\n    } else if (buf === null || buf === undefined) {\n        length = 0;\n    } else {\n        return destResult.reset(errors.expected(buf, 'buffer, null, or undefined'), offset);\n    }\n    this.sizerw.poolWriteInto(destResult, length, buffer, offset);\n    if (destResult.err) return destResult;\n    return destResult.reset(null, start + length);\n};\n\nVariableBufferRW.prototype.eagerPoolReadFrom = function eagerPoolReadFrom(destResult, buffer, offset) {\n    this.sizerw.poolReadFrom(destResult, buffer, offset);\n    if (destResult.err) return destResult;\n    var length = destResult.value;\n    var remain = buffer.length - destResult.offset;\n    if (remain < length) {\n        return ReadResult.poolShortError(destResult, length, remain, offset, destResult.offset);\n    } else {\n        offset = destResult.offset;\n        var buf = Buffer.alloc(length);\n        buffer.copy(buf, 0, offset);\n        return destResult.reset(null, offset + length, buf);\n    }\n};\n\nVariableBufferRW.prototype.lazyPoolReadFrom = function lazyPoolReadFrom(destResult, buffer, offset) {\n    this.sizerw.poolReadFrom(destResult, buffer, offset);\n    if (destResult.err) return destResult;\n    var length = destResult.value;\n    var remain = buffer.length - destResult.offset;\n    if (remain < length) {\n        return ReadResult.poolShortError(destResult, length, remain, offset, destResult.offset);\n    } else {\n        offset = destResult.offset;\n        var end = offset + length;\n        var buf = buffer.slice(offset, end);\n        return destResult.reset(null, end, buf);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/variable_buffer_rw.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/bufrw/varint.js":
/*!**************************************!*\
  !*** ./node_modules/bufrw/varint.js ***!
  \**************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval("// Copyright (c) 2015 Uber Technologies, Inc.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\n\nvar WriteResult = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").WriteResult);\nvar ReadResult = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").ReadResult);\nvar BufferRW = (__webpack_require__(/*! ./base */ \"(action-browser)/./node_modules/bufrw/base.js\").BufferRW);\nvar errors = __webpack_require__(/*! ./errors */ \"(action-browser)/./node_modules/bufrw/errors.js\");\n\n// TODO: zigzag support for signed-s\n\nmodule.exports.unsigned = BufferRW(\n    unsignedVarIntByteLength,\n    readUnsignedVarIntFrom,\n    writeUnsignedVarIntInto,\n    true);\n\nfunction unsignedVarIntByteLength(destResult, n) {\n    if (typeof n !== 'number' || n < 0) {\n        // TODO: integer check\n        return destResult.reset(errors.expected(n, 'unsigned integer'));\n    }\n    if (n === 0) return destResult.reset(null, 1);\n\n    var needed = Math.ceil(countBits(n) / 7);\n    return destResult.reset(null, needed);\n}\n\nfunction writeUnsignedVarIntInto(destResult, n, buffer, offset) {\n    if (typeof n !== 'number' || n < 0) {\n        // TODO: integer check\n        return destResult.reset(errors.expected(n, 'unsigned integer'), null);\n    }\n\n    var needed = Math.ceil(countBits(n) / 7);\n    var start = offset;\n    var end = offset + needed;\n\n    if (end > buffer.length) {\n        var remain = buffer.length - offset;\n        return WriteResult.poolShortError(destResult, needed, remain, offset);\n    }\n\n    offset = end;\n    while (offset > start) {\n        var b = n & 0x7f;\n        n >>= 7;\n        if (offset !== end) b |= 0x80;\n        buffer.writeUInt8(b, --offset, true);\n        if (n <= 0) break;\n    }\n\n    return destResult.reset(null, end);\n}\n\nfunction readUnsignedVarIntFrom(destResult, buffer, offset) {\n    var start = offset;\n    var n = 0;\n    while (offset < buffer.length) {\n        var b = buffer.readUInt8(offset++, true);\n        if (n !== 0) n <<= 7;\n        n += b & 0x7f;\n        if (!(b & 0x80)) {\n            return destResult.reset(null, offset, n);\n        }\n    }\n    var got = offset - start;\n    return ReadResult.poolShortError(destResult, got + 1, got, start, offset);\n}\n\nfunction countBits(n) {\n    var res = 1;\n    while (n >>= 1) res++;\n    return res;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/bufrw/varint.js\n");

/***/ })

};
;