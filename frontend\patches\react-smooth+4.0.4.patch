diff --git a/node_modules/react-smooth/es6/AnimateGroup.js b/node_modules/react-smooth/es6/AnimateGroup.js
index 4fca635..6a8a9af 100644
--- a/node_modules/react-smooth/es6/AnimateGroup.js
+++ b/node_modules/react-smooth/es6/AnimateGroup.js
@@ -1,5 +1,5 @@
 import React, { Children } from 'react';
-import { TransitionGroup } from 'react-transition-group';
+import TransitionGroup from 'react-transition-group/esm/TransitionGroup.js';
 import PropTypes from 'prop-types';
 import AnimateGroupChild from './AnimateGroupChild';
 function AnimateGroup(props) {
diff --git a/node_modules/react-smooth/es6/AnimateGroupChild.js b/node_modules/react-smooth/es6/AnimateGroupChild.js
index 098c7bd..136dac0 100644
--- a/node_modules/react-smooth/es6/AnimateGroupChild.js
+++ b/node_modules/react-smooth/es6/AnimateGroupChild.js
@@ -19,7 +19,7 @@ function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key i
 function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
 function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
 import React, { Component, Children } from 'react';
-import { Transition } from 'react-transition-group';
+import Transition from 'react-transition-group/esm/Transition.js';
 import PropTypes from 'prop-types';
 import Animate from './Animate';
 var parseDurationOfSingleTransition = function parseDurationOfSingleTransition() {
