"use client";

import React, { useState, useEffect } from 'react';
import { useTradingContext, BotSystemStatus } from '@/contexts/TradingContext';
import { useAIContext } from '@/contexts/AIContext';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { TargetPriceModal } from '@/components/modals/TargetPriceModal';
import { AlarmConfigModal } from '@/components/modals/AlarmConfigModal';
import { AVAILABLE_CRYPTOS, AVAILABLE_QUOTES_SIMPLE, AVAILABLE_STABLECOINS, TradingMode, ALLOWED_CRYPTO1, ALLOWED_CRYPTO2 } from '@/lib/types';
import { CryptoInput } from '@/components/ui/crypto-input';


// Define locally to avoid import issues
const DEFAULT_QUOTE_CURRENCIES = ["USDT", "USDC", "BTC"];
// Force refresh of stablecoins list
const STABLECOINS_LIST = ["USDC", "DAI", "TUSD", "FDUSD", "USDT", "EUR"];
import { AlertTriangle, Power, PowerOff, RotateCcw, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';


export default function TradingConfigSidebar() {
  // Safely get trading context with error handling
  let tradingContext;
  try {
    tradingContext = useTradingContext();
  } catch (error) {
    console.error('Trading context not available:', error);
    return (
      <aside className="w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col">
        <div className="flex items-center justify-center h-full">
          <p className="text-red-500">Trading context not available. Please refresh the page.</p>
        </div>
      </aside>
    );
  }

  const { config, dispatch, botSystemStatus, appSettings, setTargetPrices: contextSetTargetPrices } = tradingContext;
  const isBotActive = botSystemStatus === 'Running';
  const isBotWarmingUp = botSystemStatus === 'WarmingUp';

  // AI context removed as requested
  const { toast } = useToast();

  const [isTargetModalOpen, setIsTargetModalOpen] = useState(false);
  const [isAlarmModalOpen, setIsAlarmModalOpen] = useState(false);

  // AI Suggestion form state removed


  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    let val: any;

    if (type === 'checkbox') {
      val = checked;
    } else if (type === 'number') {
      // Handle empty string or invalid numbers gracefully
      if (value === '' || value === null || value === undefined) {
        val = 0;
      } else {
        const parsed = parseFloat(value);
        val = isNaN(parsed) ? 0 : parsed;
      }
    } else {
      val = value;
    }

    dispatch({ type: 'SET_CONFIG', payload: { [name]: val } });
  };

  const handleSelectChange = (name: string, value: string) => {
    dispatch({ type: 'SET_CONFIG', payload: { [name]: value } });
    if (name === 'crypto1') {
      // Reset crypto2 if new crypto1 doesn't support current crypto2
      const validQuotes = AVAILABLE_QUOTES_SIMPLE[value as keyof typeof AVAILABLE_QUOTES_SIMPLE] || DEFAULT_QUOTE_CURRENCIES || ["USDT", "USDC", "BTC"];
      if (!config.crypto2 || !Array.isArray(validQuotes) || !validQuotes.includes(config.crypto2)) {
        dispatch({ type: 'SET_CONFIG', payload: { crypto2: validQuotes[0] || 'USDT' } }); // Ensure crypto2 gets a valid default
      }
    }
  };

  const handleCrypto1Selection = (crypto: string) => {
    dispatch({ type: 'SET_CONFIG', payload: { crypto1: crypto } });
    // Reset crypto2 based on trading mode when crypto1 changes
    if (config.tradingMode === "StablecoinSwap") {
      // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1
      const allowedCrypto2 = (ALLOWED_CRYPTO1 || ["BTC", "ETH", "BNB", "SOL", "LINK", "AVAX", "DOT", "UNI", "NEAR", "AAVE", "ATOM", "VET", "RENDER", "POL", "ALGO", "ARB", "FET", "PAXG", "GALA", "CRV", "COMP", "ENJ"]).filter(c => c !== crypto);
      if (!allowedCrypto2.includes(config.crypto2) || config.crypto2 === crypto) {
        dispatch({ type: 'SET_CONFIG', payload: { crypto2: allowedCrypto2[0] } });
      }
    } else {
      // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2
      const allowedCrypto2 = ALLOWED_CRYPTO2 || ["USDC", "DAI", "TUSD", "FDUSD", "USDT", "EUR"];
      if (!allowedCrypto2.includes(config.crypto2)) {
        dispatch({ type: 'SET_CONFIG', payload: { crypto2: allowedCrypto2[0] } });
      }
    }
  };

  const handleCrypto2Selection = (crypto: string) => {
    // In stablecoin swap mode, ensure crypto2 is different from crypto1
    if (config.tradingMode === "StablecoinSwap" && crypto === config.crypto1) {
      // Don't allow selecting the same crypto as crypto1 in swap mode
      return;
    }
    dispatch({ type: 'SET_CONFIG', payload: { crypto2: crypto } });
  };

  const handleTradingModeChange = (checked: boolean) => {
    const newMode: TradingMode = checked ? "StablecoinSwap" : "SimpleSpot";

    // Reset crypto2 based on the new trading mode
    let newCrypto2: string;
    if (newMode === "StablecoinSwap") {
      // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1
      const allowedCrypto2 = (ALLOWED_CRYPTO1 || ["BTC", "ETH", "BNB", "SOL", "LINK", "AVAX", "DOT", "UNI", "NEAR", "AAVE", "ATOM", "VET", "RENDER", "POL", "ALGO", "ARB", "FET", "PAXG", "GALA", "CRV", "COMP", "ENJ"]).filter(c => c !== config.crypto1);
      newCrypto2 = allowedCrypto2[0];
    } else {
      // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2
      const allowedCrypto2 = ALLOWED_CRYPTO2 || ["USDC", "DAI", "TUSD", "FDUSD", "USDT", "EUR"];
      newCrypto2 = allowedCrypto2[0];
    }

    dispatch({ type: 'SET_CONFIG', payload: { tradingMode: newMode, crypto2: newCrypto2 } });
  };

  const handleIncomeSplitChange = (cryptoKey: 'incomeSplitCrypto1Percent' | 'incomeSplitCrypto2Percent', value: string) => {
    let percent = parseFloat(value);
    if (isNaN(percent)) percent = 0;
    if (percent < 0) percent = 0;
    if (percent > 100) percent = 100;

    if (cryptoKey === 'incomeSplitCrypto1Percent') {
      dispatch({ type: 'SET_CONFIG', payload: { incomeSplitCrypto1Percent: percent, incomeSplitCrypto2Percent: 100 - percent } });
    } else {
      dispatch({ type: 'SET_CONFIG', payload: { incomeSplitCrypto2Percent: percent, incomeSplitCrypto1Percent: 100 - percent } });
    }
  };

  // AI suggestion handler removed



  // AI suggestion effect removed


  // Use static crypto lists
  const crypto1Options = AVAILABLE_CRYPTOS || [];
  const crypto2Options = config.tradingMode === "SimpleSpot"
    ? (AVAILABLE_QUOTES_SIMPLE[config.crypto1 as keyof typeof AVAILABLE_QUOTES_SIMPLE] || DEFAULT_QUOTE_CURRENCIES || ["USDT", "USDC", "BTC"])
    : (AVAILABLE_CRYPTOS || []).filter(c => c !== config.crypto1); // Ensure Crypto2 is not same as Crypto1 in Swap mode

  // Debug log to see what's actually loaded
  console.log('🔍 DEBUG: AVAILABLE_CRYPTOS length:', AVAILABLE_CRYPTOS?.length);
  console.log('🔍 DEBUG: crypto1Options length:', crypto1Options.length);
  console.log('🔍 DEBUG: First 20 cryptos:', AVAILABLE_CRYPTOS?.slice(0, 20));
  console.log('🔍 DEBUG: ALLOWED_CRYPTO1:', ALLOWED_CRYPTO1);
  console.log('🔍 DEBUG: ALLOWED_CRYPTO2:', ALLOWED_CRYPTO2);
  console.log('🔍 DEBUG: AVAILABLE_STABLECOINS:', AVAILABLE_STABLECOINS);

  return (
    <aside className="w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold text-sidebar-primary">Trading Configuration</h2>
      </div>
      <ScrollArea className="flex-1 pr-2">
        <div className="space-y-6">
          {/* Trading Mode */}
          <Card className="bg-sidebar-accent border-sidebar-border">
            <CardHeader>
              <CardTitle className="text-sidebar-accent-foreground">Trading Mode</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableStablecoinSwap"
                  checked={config.tradingMode === "StablecoinSwap"}
                  onCheckedChange={handleTradingModeChange}
                />
                <Label htmlFor="enableStablecoinSwap" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                  Enable Stablecoin Swap Mode
                </Label>
              </div>
              <p className="text-xs text-muted-foreground">
                Current mode: {config.tradingMode === "SimpleSpot" ? "Simple Spot" : "Stablecoin Swap"}
              </p>
              {config.tradingMode === "StablecoinSwap" && (
                 <div>
                  <Label htmlFor="preferredStablecoin">Preferred Stablecoin</Label>
                  <Select name="preferredStablecoin" value={config.preferredStablecoin} onValueChange={(val) => handleSelectChange("preferredStablecoin", val as string)}>
                    <SelectTrigger id="preferredStablecoin"><SelectValue placeholder="Select stablecoin" /></SelectTrigger>
                    <SelectContent className="max-h-[300px] overflow-y-auto">
                      {STABLECOINS_LIST.map(sc => (
                        <SelectItem key={sc} value={sc}>{sc}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </CardContent>
          </Card>

          {/* AI Trading Mode Suggestion removed as requested */}

          {/* Trading Pair */}
          <Card className="bg-sidebar-accent border-sidebar-border">
            <CardHeader><CardTitle className="text-sidebar-accent-foreground">Trading Pair</CardTitle></CardHeader>
            <CardContent className="space-y-4">
              <CryptoInput
                label="Crypto 1 (Base)"
                value={config.crypto1}
                allowedCryptos={ALLOWED_CRYPTO1 || ["BTC", "ETH", "BNB", "SOL", "LINK", "AVAX", "DOT", "UNI", "NEAR", "AAVE", "ATOM", "VET", "RENDER", "POL", "ALGO", "ARB", "FET", "PAXG", "GALA", "CRV", "COMP", "ENJ"]}
                onValidCrypto={handleCrypto1Selection}
                placeholder="e.g., BTC, ETH, SOL"
                description="Enter the base cryptocurrency symbol"
              />
              <CryptoInput
                label={config.tradingMode === "StablecoinSwap" ? "Crypto 2" : "Crypto 2 (Stablecoin)"}
                value={config.crypto2}
                allowedCryptos={config.tradingMode === "StablecoinSwap"
                  ? (ALLOWED_CRYPTO1 || ["BTC", "ETH", "BNB", "SOL", "LINK", "AVAX", "DOT", "UNI", "NEAR", "AAVE", "ATOM", "VET", "RENDER", "POL", "ALGO", "ARB", "FET", "PAXG", "GALA", "CRV", "COMP", "ENJ"]).filter(c => c !== config.crypto1)
                  : (ALLOWED_CRYPTO2 || ["USDC", "DAI", "TUSD", "FDUSD", "USDT", "EUR"])
                }
                onValidCrypto={handleCrypto2Selection}
                placeholder={config.tradingMode === "StablecoinSwap" ? "e.g., BTC, ETH, SOL" : "e.g., USDT, USDC, DAI"}
                description={config.tradingMode === "StablecoinSwap" ? "Enter the second cryptocurrency symbol" : "Enter the quote/stablecoin symbol"}
              />
            </CardContent>
          </Card>

          {/* Parameters */}
          <Card className="bg-sidebar-accent border-sidebar-border">
            <CardHeader><CardTitle className="text-sidebar-accent-foreground">Parameters</CardTitle></CardHeader>
            <CardContent className="space-y-3">
              {[
                { name: "baseBid", label: "Base Bid (Crypto 2)", type: "number", step: "0.01" },
                { name: "multiplier", label: "Multiplier", type: "number", step: "0.001" },
                { name: "numDigits", label: "Display Digits", type: "number", step: "1" },
                { name: "slippagePercent", label: "Slippage %", type: "number", step: "0.01" },
              ].map(field => (
                <div key={field.name}>
                  <Label htmlFor={field.name}>{field.label}</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    type={field.type}
                    value={config[field.name as keyof typeof config] as string | number}
                    onChange={handleInputChange}
                    step={field.step}
                    min="0"
                  />
                </div>
              ))}
              <div>
                <Label>Couple Income % Split (must sum to 100)</Label>
                <div className="flex gap-2">
                  <div>
                    <Label htmlFor="incomeSplitCrypto1Percent" className="text-xs">{config.crypto1 || "Crypto 1"}%</Label>
                    <Input id="incomeSplitCrypto1Percent" type="number" value={config.incomeSplitCrypto1Percent} onChange={(e) => handleIncomeSplitChange('incomeSplitCrypto1Percent', e.target.value)} min="0" max="100" />
                  </div>
                  <div>
                    <Label htmlFor="incomeSplitCrypto2Percent" className="text-xs">{config.crypto2 || "Crypto 2"}%</Label>
                    <Input id="incomeSplitCrypto2Percent" type="number" value={config.incomeSplitCrypto2Percent} onChange={(e) => handleIncomeSplitChange('incomeSplitCrypto2Percent', e.target.value)} min="0" max="100" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </ScrollArea>

      <div className="flex-shrink-0 mt-4">
        <Separator className="mb-4 bg-sidebar-border" />

        <div className="space-y-3">
          <div className={cn(
              "text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border",
              isBotActive ? "bg-green-600 text-primary-foreground" : "bg-muted text-muted-foreground"
            )}
          >
            {isBotActive ? <Power className="h-4 w-4"/> : (isBotWarmingUp ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <PowerOff className="h-4 w-4"/>)}

            Bot Status: {isBotActive ? 'Running' : (isBotWarmingUp ? 'Warming Up' : 'Stopped')}
          </div>
          <Button onClick={() => setIsTargetModalOpen(true)} className="w-full btn-outline-neo">Set Target Prices</Button>
          <Button onClick={() => setIsAlarmModalOpen(true)} className="w-full btn-outline-neo">Set Alarm</Button>
          <Button
            onClick={() => {
              if (isBotActive) {
                dispatch({ type: 'SYSTEM_STOP_BOT' });
              } else {
                dispatch({ type: 'SYSTEM_START_BOT_INITIATE' });
              }
            }}
            className={cn('w-full btn-neo', isBotActive || isBotWarmingUp ? 'bg-destructive hover:bg-destructive/90' : 'bg-green-600 hover:bg-green-600/90')}
            disabled={isBotWarmingUp}
          >
            {isBotActive ? <Power className="h-4 w-4"/> : (isBotWarmingUp ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <PowerOff className="h-4 w-4"/>)}
            {isBotActive ? 'Stop Bot' : (isBotWarmingUp ? 'Warming Up...' : 'Start Bot')}
          </Button>
          <Button
            onClick={() => {
              dispatch({ type: 'SYSTEM_RESET_BOT' });
              toast({
                title: "Bot Reset",
                description: "Trading bot has been reset to fresh state. All positions cleared. Saved sessions are preserved.",
                duration: 4000
              });
            }}
            variant="outline"
            className="w-full btn-outline-neo"
            disabled={isBotWarmingUp}
          >
            <RotateCcw className="h-4 w-4 mr-2"/>
            Reset Bot
          </Button>
        </div>
      </div>

      <TargetPriceModal isOpen={isTargetModalOpen} onClose={() => setIsTargetModalOpen(false)} onSetTargetPrices={contextSetTargetPrices} />
      <AlarmConfigModal isOpen={isAlarmModalOpen} onClose={() => setIsAlarmModalOpen(false)} />
    </aside>
  );
}

