"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/error";
exports.ids = ["vendor-chunks/error"];
exports.modules = {

/***/ "(action-browser)/./node_modules/error/typed.js":
/*!*************************************!*\
  !*** ./node_modules/error/typed.js ***!
  \*************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar template = __webpack_require__(/*! string-template */ \"(action-browser)/./node_modules/string-template/index.js\");\nvar extend = __webpack_require__(/*! xtend/mutable */ \"(action-browser)/./node_modules/xtend/mutable.js\");\nvar assert = __webpack_require__(/*! assert */ \"assert\");\n\nvar isWordBoundary = /[_.-](\\w|$)/g;\n\nmodule.exports = TypedError;\n\nfunction TypedError(args) {\n    assert(args, 'TypedError: must specify options');\n    assert(args.type, 'TypedError: must specify options.type');\n    assert(args.message, 'TypedError: must specify options.message');\n\n    assert(!has(args, 'fullType'),\n        'TypedError: fullType field is reserved');\n\n    var message = args.message;\n    if (args.type && !args.name) {\n        var errorName = camelCase(args.type) + 'Error';\n        args.name = errorName[0].toUpperCase() + errorName.substr(1);\n    }\n\n    extend(createError, args);\n    createError._name = args.name;\n\n    return createError;\n\n    function createError(opts) {\n        var result = new Error();\n\n        Object.defineProperty(result, 'type', {\n            value: result.type,\n            enumerable: true,\n            writable: true,\n            configurable: true\n        });\n\n        var options = extend({}, args, opts);\n        if (!options.fullType) {\n            options.fullType = options.type;\n        }\n\n        extend(result, options);\n        if (opts && opts.message) {\n            result.message = template(opts.message, options);\n        } else if (message) {\n            result.message = template(message, options);\n        }\n\n        return result;\n    }\n}\n\nfunction camelCase(str) {\n    return str.replace(isWordBoundary, upperCase);\n}\n\nfunction upperCase(_, x) {\n    return x.toUpperCase();\n}\n\nfunction has(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9lcnJvci90eXBlZC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixlQUFlLG1CQUFPLENBQUMsaUZBQWlCO0FBQ3hDLGFBQWEsbUJBQU8sQ0FBQyx1RUFBZTtBQUNwQyxhQUFhLG1CQUFPLENBQUMsc0JBQVE7O0FBRTdCOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7O0FBRVQsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkU6XFxib3RcXHRyYWRpbmdib3RfZmluYWxcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGVycm9yXFx0eXBlZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciB0ZW1wbGF0ZSA9IHJlcXVpcmUoJ3N0cmluZy10ZW1wbGF0ZScpO1xudmFyIGV4dGVuZCA9IHJlcXVpcmUoJ3h0ZW5kL211dGFibGUnKTtcbnZhciBhc3NlcnQgPSByZXF1aXJlKCdhc3NlcnQnKTtcblxudmFyIGlzV29yZEJvdW5kYXJ5ID0gL1tfLi1dKFxcd3wkKS9nO1xuXG5tb2R1bGUuZXhwb3J0cyA9IFR5cGVkRXJyb3I7XG5cbmZ1bmN0aW9uIFR5cGVkRXJyb3IoYXJncykge1xuICAgIGFzc2VydChhcmdzLCAnVHlwZWRFcnJvcjogbXVzdCBzcGVjaWZ5IG9wdGlvbnMnKTtcbiAgICBhc3NlcnQoYXJncy50eXBlLCAnVHlwZWRFcnJvcjogbXVzdCBzcGVjaWZ5IG9wdGlvbnMudHlwZScpO1xuICAgIGFzc2VydChhcmdzLm1lc3NhZ2UsICdUeXBlZEVycm9yOiBtdXN0IHNwZWNpZnkgb3B0aW9ucy5tZXNzYWdlJyk7XG5cbiAgICBhc3NlcnQoIWhhcyhhcmdzLCAnZnVsbFR5cGUnKSxcbiAgICAgICAgJ1R5cGVkRXJyb3I6IGZ1bGxUeXBlIGZpZWxkIGlzIHJlc2VydmVkJyk7XG5cbiAgICB2YXIgbWVzc2FnZSA9IGFyZ3MubWVzc2FnZTtcbiAgICBpZiAoYXJncy50eXBlICYmICFhcmdzLm5hbWUpIHtcbiAgICAgICAgdmFyIGVycm9yTmFtZSA9IGNhbWVsQ2FzZShhcmdzLnR5cGUpICsgJ0Vycm9yJztcbiAgICAgICAgYXJncy5uYW1lID0gZXJyb3JOYW1lWzBdLnRvVXBwZXJDYXNlKCkgKyBlcnJvck5hbWUuc3Vic3RyKDEpO1xuICAgIH1cblxuICAgIGV4dGVuZChjcmVhdGVFcnJvciwgYXJncyk7XG4gICAgY3JlYXRlRXJyb3IuX25hbWUgPSBhcmdzLm5hbWU7XG5cbiAgICByZXR1cm4gY3JlYXRlRXJyb3I7XG5cbiAgICBmdW5jdGlvbiBjcmVhdGVFcnJvcihvcHRzKSB7XG4gICAgICAgIHZhciByZXN1bHQgPSBuZXcgRXJyb3IoKTtcblxuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkocmVzdWx0LCAndHlwZScsIHtcbiAgICAgICAgICAgIHZhbHVlOiByZXN1bHQudHlwZSxcbiAgICAgICAgICAgIGVudW1lcmFibGU6IHRydWUsXG4gICAgICAgICAgICB3cml0YWJsZTogdHJ1ZSxcbiAgICAgICAgICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZVxuICAgICAgICB9KTtcblxuICAgICAgICB2YXIgb3B0aW9ucyA9IGV4dGVuZCh7fSwgYXJncywgb3B0cyk7XG4gICAgICAgIGlmICghb3B0aW9ucy5mdWxsVHlwZSkge1xuICAgICAgICAgICAgb3B0aW9ucy5mdWxsVHlwZSA9IG9wdGlvbnMudHlwZTtcbiAgICAgICAgfVxuXG4gICAgICAgIGV4dGVuZChyZXN1bHQsIG9wdGlvbnMpO1xuICAgICAgICBpZiAob3B0cyAmJiBvcHRzLm1lc3NhZ2UpIHtcbiAgICAgICAgICAgIHJlc3VsdC5tZXNzYWdlID0gdGVtcGxhdGUob3B0cy5tZXNzYWdlLCBvcHRpb25zKTtcbiAgICAgICAgfSBlbHNlIGlmIChtZXNzYWdlKSB7XG4gICAgICAgICAgICByZXN1bHQubWVzc2FnZSA9IHRlbXBsYXRlKG1lc3NhZ2UsIG9wdGlvbnMpO1xuICAgICAgICB9XG5cbiAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9XG59XG5cbmZ1bmN0aW9uIGNhbWVsQ2FzZShzdHIpIHtcbiAgICByZXR1cm4gc3RyLnJlcGxhY2UoaXNXb3JkQm91bmRhcnksIHVwcGVyQ2FzZSk7XG59XG5cbmZ1bmN0aW9uIHVwcGVyQ2FzZShfLCB4KSB7XG4gICAgcmV0dXJuIHgudG9VcHBlckNhc2UoKTtcbn1cblxuZnVuY3Rpb24gaGFzKG9iaiwga2V5KSB7XG4gICAgcmV0dXJuIE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChvYmosIGtleSk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/error/typed.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/error/wrapped.js":
/*!***************************************!*\
  !*** ./node_modules/error/wrapped.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar extend = __webpack_require__(/*! xtend/mutable */ \"(action-browser)/./node_modules/xtend/mutable.js\");\nvar assert = __webpack_require__(/*! assert */ \"assert\");\n\nvar TypedError = __webpack_require__(/*! ./typed.js */ \"(action-browser)/./node_modules/error/typed.js\");\n\nvar objectToString = Object.prototype.toString;\nvar ERROR_TYPE = '[object Error]';\nvar causeMessageRegex = /\\{causeMessage\\}/g;\nvar origMessageRegex = /\\{origMessage\\}/g;\n\nmodule.exports = WrappedError;\n\nfunction WrappedError(options) {\n    assert(options, 'WrappedError: must specify options');\n    assert(options.type, 'WrappedError: must specify type');\n    assert(options.message, 'WrappedError: must specify message');\n\n    assert(!has(options, 'cause'),\n        'WrappedError: cause field is reserved');\n    assert(!has(options, 'fullType'),\n        'WrappedError: fullType field is reserved');\n    assert(!has(options, 'causeMessage'),\n        'WrappedError: causeMessage field is reserved');\n    assert(!has(options, 'origMessage'),\n        'WrappedError: origMessage field is reserved');\n\n    var createTypedError = TypedError(options);\n    extend(createError, options);\n    createError._name = options.name;\n\n    return createError;\n\n    function createError(cause, opts) {\n        /*eslint max-statements: [2, 25]*/\n        assert(cause, 'an error is required');\n        assert(isError(cause),\n            'WrappedError: first argument must be an error');\n\n        var causeMessage = cause.message;\n        if (causeMessage.indexOf('{causeMessage}') >= 0) {\n            // recover\n            causeMessage = causeMessage.replace(\n                causeMessageRegex,\n                '$INVALID_CAUSE_MESSAGE_LITERAL'\n            );\n        }\n        if (causeMessage.indexOf('{origMessage}') >= 0) {\n            causeMessage = causeMessage.replace(\n                origMessageRegex,\n                '$INVALID_ORIG_MESSAGE_LITERAL'\n            );\n        }\n\n        var nodeCause = false;\n        var errOptions = extend({}, opts, {\n            causeMessage: causeMessage,\n            origMessage: causeMessage\n        });\n\n        if (has(cause, 'code') && !has(errOptions, 'code')) {\n            errOptions.code = cause.code;\n        }\n\n        if (has(cause, 'errno') && !has(errOptions, 'errno')) {\n            errOptions.errno = cause.errno;\n            nodeCause = true;\n        }\n\n        if (has(cause, 'syscall') && !has(errOptions, 'syscall')) {\n            errOptions.syscall = cause.syscall;\n            nodeCause = true;\n        }\n\n        var causeType = cause.type;\n        if (!causeType && nodeCause) {\n            causeType = 'error.wrapped-io.' +\n                (cause.syscall || 'unknown') + '.' +\n                (cause.errno || 'unknown');\n        } else {\n            causeType = 'error.wrapped-unknown';\n        }\n\n        errOptions.fullType = options.type + '~!~' +\n            (cause.fullType || cause.type || causeType);\n\n        var err = createTypedError(errOptions);\n\n        Object.defineProperty(err, 'cause', {\n            value: cause,\n            configurable: true,\n            enumerable: false\n        });\n        return err;\n    }\n}\n\nfunction has(obj, key) {\n    return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nfunction isError(err) {\n    return objectToString.call(err) === ERROR_TYPE;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/error/wrapped.js\n");

/***/ })

};
;