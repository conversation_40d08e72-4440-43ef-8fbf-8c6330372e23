"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/get-port";
exports.ids = ["vendor-chunks/get-port"];
exports.modules = {

/***/ "(action-browser)/./node_modules/get-port/index.js":
/*!****************************************!*\
  !*** ./node_modules/get-port/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nconst net = __webpack_require__(/*! net */ \"net\");\n\nclass Locked extends Error {\n\tconstructor(port) {\n\t\tsuper(`${port} is locked`);\n\t}\n}\n\nconst lockedPorts = {\n\told: new Set(),\n\tyoung: new Set()\n};\n\n// On this interval, the old locked ports are discarded,\n// the young locked ports are moved to old locked ports,\n// and a new young set for locked ports are created.\nconst releaseOldLockedPortsIntervalMs = 1000 * 15;\n\n// Lazily create interval on first use\nlet interval;\n\nconst getAvailablePort = options => new Promise((resolve, reject) => {\n\tconst server = net.createServer();\n\tserver.unref();\n\tserver.on('error', reject);\n\tserver.listen(options, () => {\n\t\tconst {port} = server.address();\n\t\tserver.close(() => {\n\t\t\tresolve(port);\n\t\t});\n\t});\n});\n\nconst portCheckSequence = function * (ports) {\n\tif (ports) {\n\t\tyield * ports;\n\t}\n\n\tyield 0; // Fall back to 0 if anything else failed\n};\n\nmodule.exports = async options => {\n\tlet ports;\n\n\tif (options) {\n\t\tports = typeof options.port === 'number' ? [options.port] : options.port;\n\t}\n\n\tif (interval === undefined) {\n\t\tinterval = setInterval(() => {\n\t\t\tlockedPorts.old = lockedPorts.young;\n\t\t\tlockedPorts.young = new Set();\n\t\t}, releaseOldLockedPortsIntervalMs);\n\n\t\t// Does not exist in some environments (Electron, Jest jsdom env, browser, etc).\n\t\tif (interval.unref) {\n\t\t\tinterval.unref();\n\t\t}\n\t}\n\n\tfor (const port of portCheckSequence(ports)) {\n\t\ttry {\n\t\t\tlet availablePort = await getAvailablePort({...options, port}); // eslint-disable-line no-await-in-loop\n\t\t\twhile (lockedPorts.old.has(availablePort) || lockedPorts.young.has(availablePort)) {\n\t\t\t\tif (port !== 0) {\n\t\t\t\t\tthrow new Locked(port);\n\t\t\t\t}\n\n\t\t\t\tavailablePort = await getAvailablePort({...options, port}); // eslint-disable-line no-await-in-loop\n\t\t\t}\n\n\t\t\tlockedPorts.young.add(availablePort);\n\t\t\treturn availablePort;\n\t\t} catch (error) {\n\t\t\tif (!['EADDRINUSE', 'EACCES'].includes(error.code) && !(error instanceof Locked)) {\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t}\n\t}\n\n\tthrow new Error('No available ports found');\n};\n\nmodule.exports.makeRange = (from, to) => {\n\tif (!Number.isInteger(from) || !Number.isInteger(to)) {\n\t\tthrow new TypeError('`from` and `to` must be integer numbers');\n\t}\n\n\tif (from < 1024 || from > 65535) {\n\t\tthrow new RangeError('`from` must be between 1024 and 65535');\n\t}\n\n\tif (to < 1024 || to > 65536) {\n\t\tthrow new RangeError('`to` must be between 1024 and 65536');\n\t}\n\n\tif (to < from) {\n\t\tthrow new RangeError('`to` must be greater than or equal to `from`');\n\t}\n\n\tconst generator = function * (from, to) {\n\t\tfor (let port = from; port <= to; port++) {\n\t\t\tyield port;\n\t\t}\n\t};\n\n\treturn generator(from, to);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/get-port/index.js\n");

/***/ })

};
;