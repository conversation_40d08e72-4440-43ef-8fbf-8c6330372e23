# Pluto Trading Bot - Backend

This is the backend server for the Pluto 2.0 Trading Bot, a cryptocurrency trading bot that supports multiple trading strategies.

## Features

- RESTful API for managing trading configurations and bots
- Real-time trading with Binance through CCXT
- SQLite database for persistent storage
- JWT Authentication
- Secure API key management with encryption
- Trading strategies:
  - Simple Spot: BUY/SELL based on target price levels
  - Stablecoin Swap: Swap between crypto and stablecoins at target prices
- WebSocket support for real-time updates
- Telegram notifications
- AI-powered trading mode recommendations (requires OpenAI API key)

## Getting Started

### Prerequisites

- Python 3.8+
- Binance account (with API keys for trading)
- (Optional) Telegram Bot token
- (Optional) OpenAI API key for AI-powered recommendations

### Installation

1. Clone the repository and navigate to the backend folder
```bash
git clone https://github.com/yourusername/pluto-trading-bot.git
cd pluto-trading-bot/backend
```

2. Create and activate a virtual environment
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies
```bash
pip install -r requirements.txt
```

4. Create a `.env` file with the following content:
```
# Flask settings
FLASK_DEBUG=False
FLASK_PORT=5000

# Secret keys (CHANGE THESE IN PRODUCTION!)
SECRET_KEY=dev-secret-key-change-in-production
JWT_SECRET_KEY=jwt-secret-key-change-in-production
ENCRYPTION_KEY=change-this-encryption-key-in-production

# Database configuration
DATABASE_URI=sqlite:///pluto.db

# Exchange API settings
BINANCE_TESTNET=True
BINANCE_API_KEY=
BINANCE_API_SECRET=

# AI settings (optional)
OPENAI_API_KEY=

# Telegram Bot (optional)
TELEGRAM_BOT_TOKEN=

# Logging
LOG_LEVEL=INFO
BOT_CHECK_INTERVAL=3
```

5. Run the application
```bash
python run.py
```

## Configuration

Edit the `.env` file to configure:

- `FLASK_DEBUG`: Set to `True` for development
- `FLASK_PORT`: Port for the Flask server (default 5000)
- `SECRET_KEY`: Flask secret key
- `JWT_SECRET_KEY`: Secret key for JWT tokens
- `ENCRYPTION_KEY`: Key for encrypting API keys
- `DATABASE_URI`: SQLite database location
- `BINANCE_TESTNET`: Use Binance testnet (recommended for testing)
- `BINANCE_API_KEY`: Your Binance API key (optional, users can add their own)
- `BINANCE_API_SECRET`: Your Binance API secret (optional)
- `OPENAI_API_KEY`: OpenAI API key for trading recommendations
- `TELEGRAM_BOT_TOKEN`: Telegram bot token for notifications

## API Endpoints

### Authentication

- `POST /auth/register`: Register a new user
- `POST /auth/login`: Login with username/password
- `POST /auth/refresh`: Refresh access token
- `GET /auth/me`: Get current user information

### User API Keys

- `GET /user/apikeys`: Get user's API keys
- `POST /user/apikeys`: Add a new API key
- `PUT /user/apikeys/<key_id>`: Update an API key
- `DELETE /user/apikeys/<key_id>`: Delete an API key

### Trading Configurations

- `GET /trading/config`: Get all configurations
- `GET /trading/config/<config_id>`: Get a specific configuration
- `POST /trading/config`: Create a new configuration
- `PUT /trading/config/<config_id>`: Update a configuration
- `DELETE /trading/config/<config_id>`: Delete a configuration

### Bot Control

- `POST /trading/bot/start/<config_id>`: Start a bot
- `POST /trading/bot/stop/<config_id>`: Stop a bot
- `GET /trading/bot/status/<config_id>`: Get bot status
- `GET /trading/bot/logs/<config_id>`: Get bot logs

### Trading Data

- `GET /trading/history`: Get trade history
- `GET /trading/market-data/<pair>`: Get current market data
- `GET /trading/balances`: Get account balances

### AI Recommendations

- `POST /ai/suggest-mode`: Get AI-recommended trading mode

## Security Notes

- In production, change all secret keys in the `.env` file
- Use HTTPS in production
- Use Binance Testnet for testing before trading with real funds

## License

This project is licensed under the MIT License - see the LICENSE file for details. 