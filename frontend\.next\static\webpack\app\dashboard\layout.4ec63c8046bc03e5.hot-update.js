"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx":
/*!********************************************************!*\
  !*** ./src/components/layout/TradingConfigSidebar.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TradingConfigSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/TargetPriceModal */ \"(app-pages-browser)/./src/components/modals/TargetPriceModal.tsx\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./src/lib/types.tsx\");\n/* harmony import */ var _components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/crypto-input */ \"(app-pages-browser)/./src/components/ui/crypto-input.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/power-off.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Power,PowerOff,RotateCcw!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Define locally to avoid import issues\nconst DEFAULT_QUOTE_CURRENCIES = [\n    \"USDT\",\n    \"USDC\",\n    \"BTC\"\n];\n// Force refresh of stablecoins list\nconst STABLECOINS_LIST = [\n    \"USDC\",\n    \"DAI\",\n    \"TUSD\",\n    \"FDUSD\",\n    \"USDT\",\n    \"EUR\"\n];\n\n\n\nfunction TradingConfigSidebar() {\n    _s();\n    // Safely get trading context with error handling\n    let tradingContext;\n    try {\n        tradingContext = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_2__.useTradingContext)();\n    } catch (error) {\n        console.error('Trading context not available:', error);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n            className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-500\",\n                    children: \"Trading context not available. Please refresh the page.\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 38,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 37,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n            lineNumber: 36,\n            columnNumber: 7\n        }, this);\n    }\n    const { config, dispatch, botSystemStatus, appSettings, setTargetPrices: contextSetTargetPrices } = tradingContext;\n    const isBotActive = botSystemStatus === 'Running';\n    const isBotWarmingUp = botSystemStatus === 'WarmingUp';\n    // AI context removed as requested\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    const [isTargetModalOpen, setIsTargetModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // AI Suggestion form state removed\n    const handleInputChange = (e)=>{\n        const { name, value, type, checked } = e.target;\n        let val;\n        if (type === 'checkbox') {\n            val = checked;\n        } else if (type === 'number') {\n            // Handle empty string or invalid numbers gracefully\n            if (value === '' || value === null || value === undefined) {\n                val = 0;\n            } else {\n                const parsed = parseFloat(value);\n                val = isNaN(parsed) ? 0 : parsed;\n            }\n        } else {\n            val = value;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: val\n            }\n        });\n    };\n    const handleSelectChange = (name, value)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                [name]: value\n            }\n        });\n        if (name === 'crypto1') {\n            // Reset crypto2 if new crypto1 doesn't support current crypto2\n            const validQuotes = _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_QUOTES_SIMPLE[value] || DEFAULT_QUOTE_CURRENCIES || [\n                \"USDT\",\n                \"USDC\",\n                \"BTC\"\n            ];\n            if (!config.crypto2 || !Array.isArray(validQuotes) || !validQuotes.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: validQuotes[0] || 'USDT'\n                    }\n                }); // Ensure crypto2 gets a valid default\n            }\n        }\n    };\n    const handleCrypto1Selection = (crypto)=>{\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto1: crypto\n            }\n        });\n        // Reset crypto2 based on trading mode when crypto1 changes\n        if (config.tradingMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (_lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO1 || [\n                \"BTC\",\n                \"ETH\",\n                \"BNB\",\n                \"SOL\",\n                \"LINK\",\n                \"AVAX\",\n                \"DOT\",\n                \"UNI\",\n                \"NEAR\",\n                \"AAVE\",\n                \"ATOM\",\n                \"VET\",\n                \"RENDER\",\n                \"POL\",\n                \"ALGO\",\n                \"ARB\",\n                \"FET\",\n                \"PAXG\",\n                \"GALA\",\n                \"CRV\",\n                \"COMP\",\n                \"ENJ\"\n            ]).filter((c)=>c !== crypto);\n            if (!allowedCrypto2.includes(config.crypto2) || config.crypto2 === crypto) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = _lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO2 || [\n                \"USDC\",\n                \"DAI\",\n                \"TUSD\",\n                \"FDUSD\",\n                \"USDT\",\n                \"EUR\"\n            ];\n            if (!allowedCrypto2.includes(config.crypto2)) {\n                dispatch({\n                    type: 'SET_CONFIG',\n                    payload: {\n                        crypto2: allowedCrypto2[0]\n                    }\n                });\n            }\n        }\n    };\n    const handleCrypto2Selection = (crypto)=>{\n        // In stablecoin swap mode, ensure crypto2 is different from crypto1\n        if (config.tradingMode === \"StablecoinSwap\" && crypto === config.crypto1) {\n            // Don't allow selecting the same crypto as crypto1 in swap mode\n            return;\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                crypto2: crypto\n            }\n        });\n    };\n    const handleTradingModeChange = (checked)=>{\n        const newMode = checked ? \"StablecoinSwap\" : \"SimpleSpot\";\n        // Reset crypto2 based on the new trading mode\n        let newCrypto2;\n        if (newMode === \"StablecoinSwap\") {\n            // In stablecoin swap mode, crypto2 should be from ALLOWED_CRYPTO1 and different from crypto1\n            const allowedCrypto2 = (_lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO1 || [\n                \"BTC\",\n                \"ETH\",\n                \"BNB\",\n                \"SOL\",\n                \"LINK\",\n                \"AVAX\",\n                \"DOT\",\n                \"UNI\",\n                \"NEAR\",\n                \"AAVE\",\n                \"ATOM\",\n                \"VET\",\n                \"RENDER\",\n                \"POL\",\n                \"ALGO\",\n                \"ARB\",\n                \"FET\",\n                \"PAXG\",\n                \"GALA\",\n                \"CRV\",\n                \"COMP\",\n                \"ENJ\"\n            ]).filter((c)=>c !== config.crypto1);\n            newCrypto2 = allowedCrypto2[0];\n        } else {\n            // In simple spot mode, crypto2 should be from ALLOWED_CRYPTO2\n            const allowedCrypto2 = _lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO2 || [\n                \"USDC\",\n                \"DAI\",\n                \"TUSD\",\n                \"FDUSD\",\n                \"USDT\",\n                \"EUR\"\n            ];\n            newCrypto2 = allowedCrypto2[0];\n        }\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: {\n                tradingMode: newMode,\n                crypto2: newCrypto2\n            }\n        });\n    };\n    const handleIncomeSplitChange = (cryptoKey, value)=>{\n        let percent = parseFloat(value);\n        if (isNaN(percent)) percent = 0;\n        if (percent < 0) percent = 0;\n        if (percent > 100) percent = 100;\n        if (cryptoKey === 'incomeSplitCrypto1Percent') {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto1Percent: percent,\n                    incomeSplitCrypto2Percent: 100 - percent\n                }\n            });\n        } else {\n            dispatch({\n                type: 'SET_CONFIG',\n                payload: {\n                    incomeSplitCrypto2Percent: percent,\n                    incomeSplitCrypto1Percent: 100 - percent\n                }\n            });\n        }\n    };\n    // AI suggestion handler removed\n    // AI suggestion effect removed\n    // Use static crypto lists\n    const crypto1Options = _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS || [];\n    const crypto2Options = config.tradingMode === \"SimpleSpot\" ? _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_QUOTES_SIMPLE[config.crypto1] || DEFAULT_QUOTE_CURRENCIES || [\n        \"USDT\",\n        \"USDC\",\n        \"BTC\"\n    ] : (_lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS || []).filter((c)=>c !== config.crypto1); // Ensure Crypto2 is not same as Crypto1 in Swap mode\n    // Debug log to see what's actually loaded\n    console.log('🔍 DEBUG: AVAILABLE_CRYPTOS length:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS.length);\n    console.log('🔍 DEBUG: crypto1Options length:', crypto1Options.length);\n    console.log('🔍 DEBUG: First 20 cryptos:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === null || _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS === void 0 ? void 0 : _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_CRYPTOS.slice(0, 20));\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO1:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO1);\n    console.log('🔍 DEBUG: ALLOWED_CRYPTO2:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO2);\n    console.log('🔍 DEBUG: AVAILABLE_STABLECOINS:', _lib_types__WEBPACK_IMPORTED_MODULE_12__.AVAILABLE_STABLECOINS);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-full md:w-80 lg:w-96 bg-sidebar text-sidebar-foreground p-4 border-r-2 border-sidebar-border flex flex-col h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-sidebar-primary\",\n                    children: \"Trading Configuration\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 170,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_9__.ScrollArea, {\n                className: \"flex-1 pr-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Mode\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                    id: \"enableStablecoinSwap\",\n                                                    checked: config.tradingMode === \"StablecoinSwap\",\n                                                    onCheckedChange: handleTradingModeChange\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"enableStablecoinSwap\",\n                                                    className: \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n                                                    children: \"Enable Stablecoin Swap Mode\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"Current mode: \",\n                                                config.tradingMode === \"SimpleSpot\" ? \"Simple Spot\" : \"Stablecoin Swap\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this),\n                                        config.tradingMode === \"StablecoinSwap\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"preferredStablecoin\",\n                                                    children: \"Preferred Stablecoin\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                                    name: \"preferredStablecoin\",\n                                                    value: config.preferredStablecoin,\n                                                    onValueChange: (val)=>handleSelectChange(\"preferredStablecoin\", val),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                            id: \"preferredStablecoin\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                                placeholder: \"Select stablecoin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                lineNumber: 197,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                            className: \"max-h-[300px] overflow-y-auto\",\n                                                            children: STABLECOINS_LIST.map((sc)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                                    value: sc,\n                                                                    children: sc\n                                                                }, sc, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 200,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 18\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Trading Pair\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_13__.CryptoInput, {\n                                            label: \"Crypto 1 (Base)\",\n                                            value: config.crypto1,\n                                            allowedCryptos: _lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ],\n                                            onValidCrypto: handleCrypto1Selection,\n                                            placeholder: \"e.g., BTC, ETH, SOL\",\n                                            description: \"Enter the base cryptocurrency symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_crypto_input__WEBPACK_IMPORTED_MODULE_13__.CryptoInput, {\n                                            label: config.tradingMode === \"StablecoinSwap\" ? \"Crypto 2\" : \"Crypto 2 (Stablecoin)\",\n                                            value: config.crypto2,\n                                            allowedCryptos: config.tradingMode === \"StablecoinSwap\" ? (_lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO1 || [\n                                                \"BTC\",\n                                                \"ETH\",\n                                                \"BNB\",\n                                                \"SOL\",\n                                                \"LINK\",\n                                                \"AVAX\",\n                                                \"DOT\",\n                                                \"UNI\",\n                                                \"NEAR\",\n                                                \"AAVE\",\n                                                \"ATOM\",\n                                                \"VET\",\n                                                \"RENDER\",\n                                                \"POL\",\n                                                \"ALGO\",\n                                                \"ARB\",\n                                                \"FET\",\n                                                \"PAXG\",\n                                                \"GALA\",\n                                                \"CRV\",\n                                                \"COMP\",\n                                                \"ENJ\"\n                                            ]).filter((c)=>c !== config.crypto1) : _lib_types__WEBPACK_IMPORTED_MODULE_12__.ALLOWED_CRYPTO2 || [\n                                                \"USDC\",\n                                                \"DAI\",\n                                                \"TUSD\",\n                                                \"FDUSD\",\n                                                \"USDT\",\n                                                \"EUR\"\n                                            ],\n                                            onValidCrypto: handleCrypto2Selection,\n                                            placeholder: config.tradingMode === \"StablecoinSwap\" ? \"e.g., BTC, ETH, SOL\" : \"e.g., USDT, USDC, DAI\",\n                                            description: config.tradingMode === \"StablecoinSwap\" ? \"Enter the second cryptocurrency symbol\" : \"Enter the quote/stablecoin symbol\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.Card, {\n                            className: \"bg-sidebar-accent border-sidebar-border\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardTitle, {\n                                        className: \"text-sidebar-accent-foreground\",\n                                        children: \"Parameters\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_8__.CardContent, {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        [\n                                            {\n                                                name: \"baseBid\",\n                                                label: \"Base Bid (Crypto 2)\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            },\n                                            {\n                                                name: \"multiplier\",\n                                                label: \"Multiplier\",\n                                                type: \"number\",\n                                                step: \"0.001\"\n                                            },\n                                            {\n                                                name: \"numDigits\",\n                                                label: \"Display Digits\",\n                                                type: \"number\",\n                                                step: \"1\"\n                                            },\n                                            {\n                                                name: \"slippagePercent\",\n                                                label: \"Slippage %\",\n                                                type: \"number\",\n                                                step: \"0.01\"\n                                            }\n                                        ].map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: field.name,\n                                                        children: field.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: field.name,\n                                                        name: field.name,\n                                                        type: field.type,\n                                                        value: config[field.name],\n                                                        onChange: handleInputChange,\n                                                        step: field.step,\n                                                        min: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, field.name, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 17\n                                            }, this)),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    children: \"Couple Income % Split (must sum to 100)\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto1Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto1 || \"Crypto 1\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"incomeSplitCrypto1Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto1Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto1Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 265,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                    htmlFor: \"incomeSplitCrypto2Percent\",\n                                                                    className: \"text-xs\",\n                                                                    children: [\n                                                                        config.crypto2 || \"Crypto 2\",\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    id: \"incomeSplitCrypto2Percent\",\n                                                                    type: \"number\",\n                                                                    value: config.incomeSplitCrypto2Percent,\n                                                                    onChange: (e)=>handleIncomeSplitChange('incomeSplitCrypto2Percent', e.target.value),\n                                                                    min: \"0\",\n                                                                    max: \"100\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                                    lineNumber: 269,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                            lineNumber: 267,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                        className: \"mb-4 bg-sidebar-border\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)(\"text-center text-sm font-medium p-2 rounded-md flex items-center justify-center gap-2 border-2 border-border\", isBotActive ? \"bg-green-600 text-primary-foreground\" : \"bg-muted text-muted-foreground\"),\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 129\n                                    }, this),\n                                    \"Bot Status: \",\n                                    isBotActive ? 'Running' : isBotWarmingUp ? 'Warming Up' : 'Stopped'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>setIsTargetModalOpen(true),\n                                className: \"w-full btn-outline-neo\",\n                                children: \"Set Target Prices\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 291,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    if (isBotActive) {\n                                        console.log('🛑 Stopping bot - current status:', botSystemStatus);\n                                        dispatch({\n                                            type: 'SYSTEM_STOP_BOT'\n                                        });\n                                        console.log('🛑 SYSTEM_STOP_BOT dispatched');\n                                    } else {\n                                        console.log('▶️ Starting bot - current status:', botSystemStatus);\n                                        dispatch({\n                                            type: 'SYSTEM_START_BOT_INITIATE'\n                                        });\n                                        console.log('▶️ SYSTEM_START_BOT_INITIATE dispatched');\n                                    }\n                                },\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_15__.cn)('w-full btn-neo', isBotActive || isBotWarmingUp ? 'bg-destructive hover:bg-destructive/90' : 'bg-green-600 hover:bg-green-600/90'),\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    isBotActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 28\n                                    }, this) : isBotWarmingUp ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 77\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 129\n                                    }, this),\n                                    isBotActive ? 'Stop Bot' : isBotWarmingUp ? 'Warming Up...' : 'Start Bot'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>{\n                                    dispatch({\n                                        type: 'SYSTEM_RESET_BOT'\n                                    });\n                                    toast({\n                                        title: \"Bot Reset\",\n                                        description: \"Trading bot has been reset to fresh state. All positions cleared. Saved sessions are preserved.\",\n                                        duration: 4000\n                                    });\n                                },\n                                variant: \"outline\",\n                                className: \"w-full btn-outline-neo\",\n                                disabled: isBotWarmingUp,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Power_PowerOff_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Reset Bot\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_TargetPriceModal__WEBPACK_IMPORTED_MODULE_11__.TargetPriceModal, {\n                isOpen: isTargetModalOpen,\n                onClose: ()=>setIsTargetModalOpen(false),\n                onSetTargetPrices: contextSetTargetPrices\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\layout\\\\TradingConfigSidebar.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n_s(TradingConfigSidebar, \"Cx8udRVa9LCP2mV20ZF0LuBIfNk=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = TradingConfigSidebar;\nvar _c;\n$RefreshReg$(_c, \"TradingConfigSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/layout/TradingConfigSidebar.tsx\n"));

/***/ })

});