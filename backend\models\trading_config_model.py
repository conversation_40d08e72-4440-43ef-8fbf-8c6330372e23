from db import db
from datetime import datetime
from sqlalchemy.orm import relationship
import json

class TradingConfiguration(db.Model):
    __tablename__ = 'trading_configurations'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>Key('users.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    trading_mode = db.Column(db.String(20), nullable=False)  # 'SimpleSpot' or 'StablecoinSwap'
    crypto1 = db.Column(db.String(10), nullable=False)  # Base currency (e.g., BTC, ETH)
    crypto2 = db.Column(db.String(10), nullable=False)  # Quote currency (e.g., USDT)
    base_bid = db.Column(db.Float, nullable=False)
    multiplier = db.Column(db.Float, nullable=False)
    num_digits = db.Column(db.Integer, nullable=False)  # Display precision
    slippage_percent = db.Column(db.Float, nullable=False)
    income_split_crypto1_percent = db.Column(db.Float, nullable=False)
    income_split_crypto2_percent = db.Column(db.Float, nullable=False)
    preferred_stablecoin = db.Column(db.String(10), nullable=True)  # For StablecoinSwap mode
    target_prices = db.Column(db.Text, nullable=True)  # JSON array of target prices
    is_active = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="trading_configs")
    trades = relationship("Trade", back_populates="trading_config", cascade="all, delete-orphan")
    status_logs = relationship("BotStatusLog", back_populates="trading_config", cascade="all, delete-orphan")
    bot_states = relationship("BotState", back_populates="trading_config", cascade="all, delete-orphan")
    
    def __init__(self, user_id, name, trading_mode, crypto1, crypto2, base_bid, 
                 multiplier, num_digits, slippage_percent, income_split_crypto1_percent, 
                 income_split_crypto2_percent, preferred_stablecoin=None, target_prices=None):
        self.user_id = user_id
        self.name = name
        self.trading_mode = trading_mode
        self.crypto1 = crypto1
        self.crypto2 = crypto2
        self.base_bid = base_bid
        self.multiplier = multiplier
        self.num_digits = num_digits
        self.slippage_percent = slippage_percent
        self.income_split_crypto1_percent = income_split_crypto1_percent
        self.income_split_crypto2_percent = income_split_crypto2_percent
        self.preferred_stablecoin = preferred_stablecoin
        self.set_target_prices(target_prices or [])
    
    def get_target_prices(self):
        """Convert stored JSON string to Python list."""
        if not self.target_prices:
            return []
        try:
            return json.loads(self.target_prices)
        except json.JSONDecodeError:
            return []
    
    def set_target_prices(self, prices):
        """Convert Python list to JSON string for storage."""
        self.target_prices = json.dumps(prices)
    
    def to_dict(self):
        """Convert model to dictionary for API responses."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'name': self.name,
            'trading_mode': self.trading_mode,
            'crypto1': self.crypto1,
            'crypto2': self.crypto2,
            'base_bid': self.base_bid,
            'multiplier': self.multiplier,
            'num_digits': self.num_digits,
            'slippage_percent': self.slippage_percent,
            'income_split_crypto1_percent': self.income_split_crypto1_percent,
            'income_split_crypto2_percent': self.income_split_crypto2_percent,
            'preferred_stablecoin': self.preferred_stablecoin,
            'target_prices': self.get_target_prices(),
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
