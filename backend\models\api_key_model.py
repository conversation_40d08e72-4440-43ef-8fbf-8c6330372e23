import sys
import os
# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from db import db
from datetime import datetime
from sqlalchemy.orm import relationship
from auth.crypto import encrypt_data, decrypt_data, EncryptionError, DecryptionError
import logging

logger = logging.getLogger(__name__)

class ExchangeApiKey(db.Model):
    __tablename__ = 'exchange_api_keys'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    exchange_name = db.Column(db.String(50), nullable=False, default='binance') # Default to Binance
    api_key_encrypted = db.Column(db.Text, nullable=False)
    secret_key_encrypted = db.Column(db.Text, nullable=False)
    label = db.Column(db.String(100), nullable=True)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="api_keys")
    
    def __init__(self, user_id, exchange_name, api_key, secret_key, label=None):
        self.user_id = user_id
        self.exchange_name = exchange_name
        self.api_key_encrypted = encrypt_data(api_key)
        self.secret_key_encrypted = encrypt_data(secret_key)
        self.label = label
    
    @property
    def api_key(self):
        """Decrypts and returns the API key."""
        return decrypt_data(self.api_key_encrypted)
    
    @api_key.setter
    def api_key(self, value):
        """Encrypts and sets the API key."""
        self.api_key_encrypted = encrypt_data(value)
    
    @property
    def secret_key(self):
        """Decrypts and returns the secret key."""
        return decrypt_data(self.secret_key_encrypted)
    
    @secret_key.setter
    def secret_key(self, value):
        """Encrypts and sets the secret key."""
        self.secret_key_encrypted = encrypt_data(value)
    
    def to_dict(self, mask_secrets=True):
        """Convert the model to a dictionary."""
        current_api_key = None
        current_secret_key = None
        decryption_successful = True
        try:
            current_api_key = self.api_key
            current_secret_key = self.secret_key
        except DecryptionError as e:
            logger.error(f"Decryption failed for API key ID {self.id}: {str(e)}")
            decryption_successful = False

        if mask_secrets:
            masked_api_key = f"{current_api_key[:4]}{'*' * 8}{current_api_key[-4:]}" if decryption_successful and current_api_key else "DECRYPTION_FAILED"
            masked_secret_key = "********************" if decryption_successful and current_secret_key else "DECRYPTION_FAILED"
            
            return {
                'id': self.id,
                'user_id': self.user_id,
                'exchange_name': self.exchange_name,
                'api_key': masked_api_key,
                'secret_key': masked_secret_key,
                'label': self.label,
                'is_active': self.is_active,
                'created_at': self.created_at.isoformat()
            }
        
        if not decryption_successful:
            logger.critical(f"Decryption failed for API key ID {self.id} when unmasked dict was requested. Cannot proceed.")
            raise DecryptionError(f"Failed to decrypt API key ID {self.id} for internal use.")

        return {
            'id': self.id,
            'user_id': self.user_id,
            'exchange_name': self.exchange_name,
            'api_key': current_api_key,
            'secret_key': current_secret_key,
            'label': self.label,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat()
        } 