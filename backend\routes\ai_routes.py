from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
import logging
import json
import requests
from db import db

# For Gemini API integration
import os
from dotenv import load_dotenv

logger = logging.getLogger(__name__)
ai_bp = Blueprint('ai', __name__, url_prefix='/ai')

# Load environment variables
load_dotenv()

# Check for Gemini API key
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')

@ai_bp.route('/suggest-mode', methods=['POST'])
@jwt_required()
def suggest_trading_mode():
    """Suggest a trading mode based on user input."""
    if not request.is_json:
        return jsonify({"error": "Missing JSON in request"}), 400
    
    data = request.get_json()
    
    # Expected fields for the trading suggestions
    risk_tolerance = data.get('riskTolerance', 'medium')  # 'low', 'medium', 'high'
    preferred_cryptos = data.get('preferredCryptos', [])
    investment_goals = data.get('investmentGoals', '')
    
    try:
        # Use Gemini for trading recommendations if the API key is available
        if GEMINI_API_KEY:
            suggestion = get_gemini_suggestion(risk_tolerance, preferred_cryptos, investment_goals)
        else:
            # Fallback to simple rule-based suggestion if no API key
            suggestion = get_rule_based_suggestion(risk_tolerance, preferred_cryptos, investment_goals)
        
        return jsonify({
            "suggestion": suggestion
        }), 200
    except Exception as e:
        logger.error(f"Error getting AI suggestion: {str(e)}")
        return jsonify({
            "error": "Failed to get trading mode suggestion",
            "message": str(e)
        }), 500


def get_gemini_suggestion(risk_tolerance, preferred_cryptos, investment_goals):
    """Use Gemini API to generate a trading mode suggestion."""
    if not GEMINI_API_KEY:
        raise ValueError("Gemini API key not configured")
    
    headers = {
        "Content-Type": "application/json"
    }
    
    # Format the prompt
    crypto_list = ", ".join(preferred_cryptos) if preferred_cryptos else "None specified"
    
    prompt = f"""
    I need a trading strategy recommendation for a crypto trader with the following parameters:
    
    - Risk Tolerance: {risk_tolerance}
    - Preferred Cryptocurrencies: {crypto_list}
    - Investment Goals: {investment_goals}
    
    Recommend either a "SimpleSpot" trading strategy or a "StablecoinSwap" trading strategy, and explain why.
    Also suggest specific cryptocurrency pairs to trade, target price levels, and an appropriate base bid and multiplier.
    
    Format the response as a JSON with the following fields:
    - recommendedMode: either "SimpleSpot" or "StablecoinSwap"
    - crypto1: recommended base cryptocurrency (e.g., "BTC")
    - crypto2: recommended quote cryptocurrency (e.g., "USDT") 
    - preferredStablecoin: recommended stablecoin if using StablecoinSwap (null if not applicable)
    - baseBid: recommended base bid amount
    - multiplier: recommended multiplier (typically between 1.001 and 1.02)
    - explanation: the reasoning for this recommendation
    
    The response should be valid JSON without any markdown formatting.
    """
    
    data = {
        "contents": [{
            "parts": [{
                "text": prompt
            }]
        }]
    }
    
    response = requests.post(
        f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={GEMINI_API_KEY}",
        headers=headers,
        json=data
    )
    
    if response.status_code != 200:
        logger.error(f"Gemini API error: {response.text}")
        raise ValueError(f"Gemini API error: {response.status_code}")
    
    try:
        result = response.json()
        suggestion_text = result['candidates'][0]['content']['parts'][0]['text'].strip()
        
        # Extract the JSON part from the text
        # First, find the JSON portion (assuming it's enclosed in backticks or not)
        import re
        json_match = re.search(r'```json\s*([\s\S]*?)\s*```', suggestion_text)
        if json_match:
            json_str = json_match.group(1)
        else:
            # Try to extract without markdown formatting
            json_str = suggestion_text
            
        # Clean the string to ensure it's valid JSON
        json_str = json_str.replace('\n', ' ').replace('\r', '')
        
        # Parse the JSON
        suggestion_json = json.loads(json_str)
        return suggestion_json
    except Exception as e:
        logger.error(f"Error parsing Gemini response: {str(e)}")
        # Return a fallback if Gemini returned invalid JSON
        return get_rule_based_suggestion(risk_tolerance, preferred_cryptos, investment_goals)


def get_rule_based_suggestion(risk_tolerance, preferred_cryptos, investment_goals):
    """Simple rule-based suggestion system as a fallback."""
    is_long_term = "long term" in investment_goals.lower() or "hodl" in investment_goals.lower()
    is_short_term = "short term" in investment_goals.lower() or "quick" in investment_goals.lower()
    is_stable = "stable" in investment_goals.lower() or "safe" in investment_goals.lower()
    
    crypto1 = "BTC"  # Default to Bitcoin
    if preferred_cryptos and len(preferred_cryptos) > 0:
        crypto1 = preferred_cryptos[0]
    
    # Default recommendations
    if risk_tolerance.lower() == "low":
        return {
            "recommendedMode": "StablecoinSwap",
            "crypto1": crypto1,
            "crypto2": "USDT",
            "preferredStablecoin": "USDT",
            "baseBid": 100,
            "multiplier": 1.005,
            "explanation": "For low risk tolerance, a StablecoinSwap strategy is recommended as it provides more stability. By swapping between a cryptocurrency and a stablecoin, you can protect against volatility while still capturing profits on price movements."
        }
    elif risk_tolerance.lower() == "high":
        return {
            "recommendedMode": "SimpleSpot",
            "crypto1": crypto1,
            "crypto2": "USDT",
            "preferredStablecoin": None,
            "baseBid": 150,
            "multiplier": 1.015,
            "explanation": "For high risk tolerance, a SimpleSpot strategy with a higher multiplier can generate greater profits. This strategy allows for more aggressive trading on price movements but carries increased risk."
        }
    else:  # medium risk tolerance
        mode = "SimpleSpot" if is_short_term else "StablecoinSwap"
        multiplier = 1.01 if is_short_term else 1.008
        
        return {
            "recommendedMode": mode,
            "crypto1": crypto1,
            "crypto2": "USDT",
            "preferredStablecoin": "USDT" if mode == "StablecoinSwap" else None,
            "baseBid": 125,
            "multiplier": multiplier,
            "explanation": f"For medium risk tolerance with your goals, a {mode} strategy provides a balanced approach. The recommended multiplier and base bid are set to match your risk profile while still providing good profit potential."
        } 