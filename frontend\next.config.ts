import { Configuration as WebpackConfiguration } from 'webpack';

/** @type {import('next').NextConfig} */
const nextConfig = {
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  transpilePackages: ['react-smooth', 'recharts', 'react-transition-group'],
  webpack: (config: WebpackConfiguration, { isServer }: { isServer: boolean }) => {
      // Fixes npm packages that depend on `fs` module
      if (!isServer) {
        config.resolve = config.resolve || {};
        config.resolve.fallback = config.resolve.fallback || {};
        (config.resolve.fallback as any).fs = false;
      }
    return config;
  },
};

export default nextConfig;
