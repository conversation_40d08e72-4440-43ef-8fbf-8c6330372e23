"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2,Volume2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_modals_SessionAlarmModal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/SessionAlarmModal */ \"(app-pages-browser)/./src/components/modals/SessionAlarmModal.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    _s();\n    const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSessionId, setEditingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingName, setEditingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentRuntime, setCurrentRuntime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [alarmModalOpen, setAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAlarmSession, setSelectedAlarmSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            loadSessions();\n            const sessionId = sessionManager.getCurrentSessionId();\n            setCurrentSessionId(sessionId);\n            // Initialize current runtime\n            if (sessionId) {\n                const runtime = sessionManager.getCurrentRuntime(sessionId);\n                setCurrentRuntime(runtime);\n            }\n            // Listen for storage changes to sync sessions across windows\n            const handleStorageChange = {\n                \"SessionManager.useEffect.handleStorageChange\": (event)=>{\n                    if (event.key === 'trading_sessions' && event.newValue) {\n                        loadSessions();\n                        console.log('🔄 Sessions synced from another window');\n                    }\n                }\n            }[\"SessionManager.useEffect.handleStorageChange\"];\n            window.addEventListener('storage', handleStorageChange);\n            return ({\n                \"SessionManager.useEffect\": ()=>{\n                    window.removeEventListener('storage', handleStorageChange);\n                }\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], []);\n    // Update runtime display every 10 seconds for active sessions (reduced to prevent flickering)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            const interval = setInterval({\n                \"SessionManager.useEffect.interval\": ()=>{\n                    if (currentSessionId && currentSessionId !== 'undefined') {\n                        const runtime = sessionManager.getCurrentRuntime(currentSessionId);\n                        setCurrentRuntime(runtime);\n                    // Removed console log to reduce noise\n                    }\n                }\n            }[\"SessionManager.useEffect.interval\"], 10000); // Update every 10 seconds to reduce flickering\n            return ({\n                \"SessionManager.useEffect\": ()=>clearInterval(interval)\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], [\n        currentSessionId,\n        sessionManager\n    ]);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        setSessions(allSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const handleSaveCurrentSession = async ()=>{\n        console.log('💾 Save button clicked - currentSessionId:', currentSessionId);\n        if (!currentSessionId || currentSessionId === 'undefined') {\n            console.error('❌ Invalid session ID:', currentSessionId);\n            toast({\n                title: \"Error\",\n                description: \"No valid session to save. Please start trading first.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            // Check if there's already a saved version of this session\n            const currentSession = sessionManager.loadSession(currentSessionId);\n            if (!currentSession) {\n                toast({\n                    title: \"Error\",\n                    description: \"Current session not found\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Look for existing saved session with same base name (only manually saved ones)\n            const allSessions = sessionManager.getAllSessions();\n            const baseName = currentSession.name.replace(/ \\((Saved|AutoSaved).*\\)$/, ''); // Remove existing timestamp\n            const existingSavedSession = allSessions.find((s)=>s.id !== currentSessionId && s.name.startsWith(baseName) && s.name.includes('(Saved') && // Only look for manually saved sessions\n                !s.isActive // Only look in inactive sessions\n            );\n            let targetSessionId;\n            let savedName;\n            if (existingSavedSession) {\n                // Update existing saved session - update the timestamp to show latest save\n                targetSessionId = existingSavedSession.id;\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                console.log(\"\\uD83D\\uDCDD Updating existing saved session: \".concat(savedName));\n            } else {\n                // Create new saved session with timestamp\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                targetSessionId = await sessionManager.createNewSession(savedName, config);\n                console.log(\"\\uD83D\\uDCBE Creating new saved session: \".concat(savedName));\n            }\n            // Get current runtime from the active session\n            const currentRuntime = sessionManager.getCurrentRuntime(currentSessionId);\n            // Update the session name if it's an existing session\n            if (existingSavedSession) {\n                sessionManager.renameSession(targetSessionId, savedName);\n            }\n            // Save/update the session with current data and runtime\n            const success = sessionManager.saveSession(targetSessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, false, currentRuntime // Pass the current runtime to the saved session\n            );\n            // DO NOT deactivate the current session - keep it running!\n            // The current session should remain active for continued trading\n            if (success) {\n                loadSessions();\n                toast({\n                    title: \"Session Saved\",\n                    description: existingSavedSession ? \"Save checkpoint updated (Runtime: \".concat(formatRuntime(currentRuntime), \")\") : \"Session saved as checkpoint (Runtime: \".concat(formatRuntime(currentRuntime), \")\")\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to save session\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error saving session:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save session\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleLoadSession = (sessionId)=>{\n        console.log('📂 Loading session:', sessionId);\n        if (!sessionId || sessionId === 'undefined') {\n            console.error('❌ Invalid session ID for loading:', sessionId);\n            toast({\n                title: \"Error\",\n                description: \"Invalid session ID\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const session = sessionManager.loadSession(sessionId);\n        console.log('📂 Session loaded from manager:', session ? 'Success' : 'Failed');\n        if (!session) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to load session - session not found\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Load session data into context\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: session.config\n        });\n        dispatch({\n            type: 'SET_TARGET_PRICE_ROWS',\n            payload: session.targetPriceRows\n        });\n        dispatch({\n            type: 'CLEAR_ORDER_HISTORY'\n        });\n        session.orderHistory.forEach((entry)=>{\n            dispatch({\n                type: 'ADD_ORDER_HISTORY_ENTRY',\n                payload: entry\n            });\n        });\n        dispatch({\n            type: 'SET_MARKET_PRICE',\n            payload: session.currentMarketPrice\n        });\n        dispatch({\n            type: 'SET_BALANCES',\n            payload: {\n                crypto1: session.crypto1Balance,\n                crypto2: session.crypto2Balance\n            }\n        });\n        sessionManager.setCurrentSession(sessionId);\n        setCurrentSessionId(sessionId);\n        loadSessions();\n        toast({\n            title: \"Session Loaded\",\n            description: 'Session \"'.concat(session.name, '\" has been loaded. Redirecting to dashboard...')\n        });\n        // Navigate to dashboard after loading session (don't start bot automatically)\n        setTimeout(()=>{\n            router.push('/dashboard');\n        }, 1000);\n    };\n    const handleDeleteSession = (sessionId)=>{\n        console.log('🗑️ Deleting session:', sessionId);\n        if (!sessionId || sessionId === 'undefined') {\n            console.error('❌ Invalid session ID for deletion:', sessionId);\n            toast({\n                title: \"Error\",\n                description: \"Invalid session ID\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const success = sessionManager.deleteSession(sessionId);\n        console.log('🗑️ Session deletion result:', success ? 'Success' : 'Failed');\n        if (success) {\n            if (currentSessionId === sessionId) {\n                setCurrentSessionId(null);\n            }\n            loadSessions();\n            toast({\n                title: \"Session Deleted\",\n                description: \"Session has been deleted successfully\"\n            });\n        } else {\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete session\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleRenameSession = (sessionId)=>{\n        if (!editingName.trim()) return;\n        const success = sessionManager.renameSession(sessionId, editingName.trim());\n        if (success) {\n            setEditingSessionId(null);\n            setEditingName('');\n            loadSessions();\n            toast({\n                title: \"Session Renamed\",\n                description: \"Session has been renamed successfully\"\n            });\n        }\n    };\n    const handleExportSession = (sessionId)=>{\n        const csvContent = sessionManager.exportSessionToCSV(sessionId);\n        if (!csvContent) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to export session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const session = sessionManager.loadSession(sessionId);\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat((session === null || session === void 0 ? void 0 : session.name) || 'session', \"_\").concat(new Date().toISOString().split('T')[0], \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        toast({\n            title: \"Export Complete\",\n            description: \"Session data has been exported to CSV\"\n        });\n    };\n    const handleOpenAlarmModal = (sessionId, sessionName)=>{\n        setSelectedAlarmSession({\n            id: sessionId,\n            name: sessionName\n        });\n        setAlarmModalOpen(true);\n    };\n    const handleCloseAlarmModal = ()=>{\n        setAlarmModalOpen(false);\n        setSelectedAlarmSession(null);\n    };\n    const formatRuntime = (runtime)=>{\n        if (!runtime || runtime < 0) return '0s';\n        const totalSeconds = Math.floor(runtime / 1000);\n        const hours = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        if (hours > 0) {\n            return \"\".concat(hours, \"h \").concat(minutes, \"m \").concat(seconds, \"s\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"m \").concat(seconds, \"s\");\n        } else {\n            return \"\".concat(seconds, \"s\");\n        }\n    };\n    const getCurrentSession = ()=>{\n        return sessions.find((s)=>s.id === currentSessionId);\n    };\n    const getActiveSessions = ()=>{\n        // Only show sessions that are actually running (bot is active and it's the current session)\n        if (!currentSessionId || botSystemStatus !== 'Running') {\n            console.log('🔍 No active sessions - currentSessionId:', currentSessionId, 'botSystemStatus:', botSystemStatus);\n            return [];\n        }\n        const activeSessions = sessions.filter((s)=>{\n            const isCurrentAndActive = s.id === currentSessionId && s.isActive;\n            console.log(\"\\uD83D\\uDD0D Session \".concat(s.name, \": isActive=\").concat(s.isActive, \", isCurrent=\").concat(s.id === currentSessionId, \", result=\").concat(isCurrentAndActive));\n            return isCurrentAndActive;\n        });\n        console.log('🔍 Active sessions found:', activeSessions.length);\n        return activeSessions;\n    };\n    const getPastSessions = ()=>{\n        // Show all sessions that are NOT currently active/running\n        const pastSessions = sessions.filter((s)=>{\n            const isCurrentlyActive = s.id === currentSessionId && s.isActive && botSystemStatus === 'Running';\n            return !isCurrentlyActive;\n        });\n        console.log('🔍 Past sessions found:', pastSessions.length);\n        return pastSessions;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 13\n                                }, this),\n                                \"Active Sessions\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: getActiveSessions().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Active Status\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Runtime\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: getActiveSessions().map((session, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: editingName,\n                                                                onChange: (e)=>setEditingName(e.target.value),\n                                                                onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                className: \"text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleRenameSession(session.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 429,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: session.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"ghost\",\n                                                                onClick: ()=>{\n                                                                    setEditingSessionId(session.id);\n                                                                    setEditingName(session.name);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"default\",\n                                                        children: \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: session.id === currentSessionId ? formatRuntime(currentRuntime) : formatRuntime(session.runtime)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: session.id === currentSessionId ? // Current session - show Save button (session is active/loaded)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: handleSaveCurrentSession,\n                                                                size: \"sm\",\n                                                                className: \"btn-neo\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"mr-2 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"Save\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 463,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 468,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 467,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenAlarmModal(session.id, session.name),\n                                                                title: \"Configure Alarms\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 470,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 25\n                                                    }, this) : // Other sessions - show Load button (session is saved)\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenAlarmModal(session.id, session.name),\n                                                                title: \"Configure Alarms\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, \"active-\".concat(session.id, \"-\").concat(index), true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 406,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No active session\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs\",\n                                    children: \"Start trading to create a session automatically\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 404,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 397,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 519,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Past Sessions (\",\n                                    getInactiveSessions().length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: [\n                                    \"Auto-saved: \",\n                                    getInactiveSessions().length,\n                                    \" | Manual: 0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 517,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                            className: \"h-[400px]\",\n                            children: getInactiveSessions().length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-muted-foreground py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 530,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No saved sessions yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: \"Save your current session to get started.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 532,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 529,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Session Name\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Active Status\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Total Runtime\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: getInactiveSessions().map((session, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    value: editingName,\n                                                                    onChange: (e)=>setEditingName(e.target.value),\n                                                                    onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                    className: \"text-sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 551,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleRenameSession(session.id),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 558,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 557,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: session.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>{\n                                                                        setEditingSessionId(session.id);\n                                                                        setEditingName(session.name);\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 572,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 564,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            children: \"Inactive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: formatRuntime(sessionManager.getCurrentRuntime(session.id))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 590,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 593,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenAlarmModal(session.id, session.name),\n                                                                title: \"Configure Alarms\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 601,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteSession(session.id),\n                                                                title: \"Delete Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_Volume2_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 604,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, \"inactive-\".concat(session.id, \"-\").concat(index), true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 545,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 527,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 516,\n                columnNumber: 7\n            }, this),\n            selectedAlarmSession && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_SessionAlarmModal__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                isOpen: alarmModalOpen,\n                onClose: handleCloseAlarmModal,\n                sessionId: selectedAlarmSession.id,\n                sessionName: selectedAlarmSession.name\n            }, void 0, false, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 618,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n        lineNumber: 395,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"lTtemk6bI4J/UVkffUm/7GBSG0Q=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_11__.useRouter\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SessionManager.tsx\n"));

/***/ })

});