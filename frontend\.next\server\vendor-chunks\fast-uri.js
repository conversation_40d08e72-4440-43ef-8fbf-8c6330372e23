"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-uri";
exports.ids = ["vendor-chunks/fast-uri"];
exports.modules = {

/***/ "(action-browser)/./node_modules/fast-uri/index.js":
/*!****************************************!*\
  !*** ./node_modules/fast-uri/index.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { normalizeIPv6, normalizeIPv4, removeDotSegments, recomposeAuthority, normalizeComponentEncoding } = __webpack_require__(/*! ./lib/utils */ \"(action-browser)/./node_modules/fast-uri/lib/utils.js\")\nconst SCHEMES = __webpack_require__(/*! ./lib/schemes */ \"(action-browser)/./node_modules/fast-uri/lib/schemes.js\")\n\nfunction normalize (uri, options) {\n  if (typeof uri === 'string') {\n    uri = serialize(parse(uri, options), options)\n  } else if (typeof uri === 'object') {\n    uri = parse(serialize(uri, options), options)\n  }\n  return uri\n}\n\nfunction resolve (baseURI, relativeURI, options) {\n  const schemelessOptions = Object.assign({ scheme: 'null' }, options)\n  const resolved = resolveComponents(parse(baseURI, schemelessOptions), parse(relativeURI, schemelessOptions), schemelessOptions, true)\n  return serialize(resolved, { ...schemelessOptions, skipEscape: true })\n}\n\nfunction resolveComponents (base, relative, options, skipNormalization) {\n  const target = {}\n  if (!skipNormalization) {\n    base = parse(serialize(base, options), options) // normalize base components\n    relative = parse(serialize(relative, options), options) // normalize relative components\n  }\n  options = options || {}\n\n  if (!options.tolerant && relative.scheme) {\n    target.scheme = relative.scheme\n    // target.authority = relative.authority;\n    target.userinfo = relative.userinfo\n    target.host = relative.host\n    target.port = relative.port\n    target.path = removeDotSegments(relative.path || '')\n    target.query = relative.query\n  } else {\n    if (relative.userinfo !== undefined || relative.host !== undefined || relative.port !== undefined) {\n      // target.authority = relative.authority;\n      target.userinfo = relative.userinfo\n      target.host = relative.host\n      target.port = relative.port\n      target.path = removeDotSegments(relative.path || '')\n      target.query = relative.query\n    } else {\n      if (!relative.path) {\n        target.path = base.path\n        if (relative.query !== undefined) {\n          target.query = relative.query\n        } else {\n          target.query = base.query\n        }\n      } else {\n        if (relative.path.charAt(0) === '/') {\n          target.path = removeDotSegments(relative.path)\n        } else {\n          if ((base.userinfo !== undefined || base.host !== undefined || base.port !== undefined) && !base.path) {\n            target.path = '/' + relative.path\n          } else if (!base.path) {\n            target.path = relative.path\n          } else {\n            target.path = base.path.slice(0, base.path.lastIndexOf('/') + 1) + relative.path\n          }\n          target.path = removeDotSegments(target.path)\n        }\n        target.query = relative.query\n      }\n      // target.authority = base.authority;\n      target.userinfo = base.userinfo\n      target.host = base.host\n      target.port = base.port\n    }\n    target.scheme = base.scheme\n  }\n\n  target.fragment = relative.fragment\n\n  return target\n}\n\nfunction equal (uriA, uriB, options) {\n  if (typeof uriA === 'string') {\n    uriA = unescape(uriA)\n    uriA = serialize(normalizeComponentEncoding(parse(uriA, options), true), { ...options, skipEscape: true })\n  } else if (typeof uriA === 'object') {\n    uriA = serialize(normalizeComponentEncoding(uriA, true), { ...options, skipEscape: true })\n  }\n\n  if (typeof uriB === 'string') {\n    uriB = unescape(uriB)\n    uriB = serialize(normalizeComponentEncoding(parse(uriB, options), true), { ...options, skipEscape: true })\n  } else if (typeof uriB === 'object') {\n    uriB = serialize(normalizeComponentEncoding(uriB, true), { ...options, skipEscape: true })\n  }\n\n  return uriA.toLowerCase() === uriB.toLowerCase()\n}\n\nfunction serialize (cmpts, opts) {\n  const components = {\n    host: cmpts.host,\n    scheme: cmpts.scheme,\n    userinfo: cmpts.userinfo,\n    port: cmpts.port,\n    path: cmpts.path,\n    query: cmpts.query,\n    nid: cmpts.nid,\n    nss: cmpts.nss,\n    uuid: cmpts.uuid,\n    fragment: cmpts.fragment,\n    reference: cmpts.reference,\n    resourceName: cmpts.resourceName,\n    secure: cmpts.secure,\n    error: ''\n  }\n  const options = Object.assign({}, opts)\n  const uriTokens = []\n\n  // find scheme handler\n  const schemeHandler = SCHEMES[(options.scheme || components.scheme || '').toLowerCase()]\n\n  // perform scheme specific serialization\n  if (schemeHandler && schemeHandler.serialize) schemeHandler.serialize(components, options)\n\n  if (components.path !== undefined) {\n    if (!options.skipEscape) {\n      components.path = escape(components.path)\n\n      if (components.scheme !== undefined) {\n        components.path = components.path.split('%3A').join(':')\n      }\n    } else {\n      components.path = unescape(components.path)\n    }\n  }\n\n  if (options.reference !== 'suffix' && components.scheme) {\n    uriTokens.push(components.scheme, ':')\n  }\n\n  const authority = recomposeAuthority(components)\n  if (authority !== undefined) {\n    if (options.reference !== 'suffix') {\n      uriTokens.push('//')\n    }\n\n    uriTokens.push(authority)\n\n    if (components.path && components.path.charAt(0) !== '/') {\n      uriTokens.push('/')\n    }\n  }\n  if (components.path !== undefined) {\n    let s = components.path\n\n    if (!options.absolutePath && (!schemeHandler || !schemeHandler.absolutePath)) {\n      s = removeDotSegments(s)\n    }\n\n    if (authority === undefined) {\n      s = s.replace(/^\\/\\//u, '/%2F') // don't allow the path to start with \"//\"\n    }\n\n    uriTokens.push(s)\n  }\n\n  if (components.query !== undefined) {\n    uriTokens.push('?', components.query)\n  }\n\n  if (components.fragment !== undefined) {\n    uriTokens.push('#', components.fragment)\n  }\n  return uriTokens.join('')\n}\n\nconst hexLookUp = Array.from({ length: 127 }, (_v, k) => /[^!\"$&'()*+,\\-.;=_`a-z{}~]/u.test(String.fromCharCode(k)))\n\nfunction nonSimpleDomain (value) {\n  let code = 0\n  for (let i = 0, len = value.length; i < len; ++i) {\n    code = value.charCodeAt(i)\n    if (code > 126 || hexLookUp[code]) {\n      return true\n    }\n  }\n  return false\n}\n\nconst URI_PARSE = /^(?:([^#/:?]+):)?(?:\\/\\/((?:([^#/?@]*)@)?(\\[[^#/?\\]]+\\]|[^#/:?]*)(?::(\\d*))?))?([^#?]*)(?:\\?([^#]*))?(?:#((?:.|[\\n\\r])*))?/u\n\nfunction parse (uri, opts) {\n  const options = Object.assign({}, opts)\n  const parsed = {\n    scheme: undefined,\n    userinfo: undefined,\n    host: '',\n    port: undefined,\n    path: '',\n    query: undefined,\n    fragment: undefined\n  }\n  const gotEncoding = uri.indexOf('%') !== -1\n  let isIP = false\n  if (options.reference === 'suffix') uri = (options.scheme ? options.scheme + ':' : '') + '//' + uri\n\n  const matches = uri.match(URI_PARSE)\n\n  if (matches) {\n    // store each component\n    parsed.scheme = matches[1]\n    parsed.userinfo = matches[3]\n    parsed.host = matches[4]\n    parsed.port = parseInt(matches[5], 10)\n    parsed.path = matches[6] || ''\n    parsed.query = matches[7]\n    parsed.fragment = matches[8]\n\n    // fix port number\n    if (isNaN(parsed.port)) {\n      parsed.port = matches[5]\n    }\n    if (parsed.host) {\n      const ipv4result = normalizeIPv4(parsed.host)\n      if (ipv4result.isIPV4 === false) {\n        const ipv6result = normalizeIPv6(ipv4result.host)\n        parsed.host = ipv6result.host.toLowerCase()\n        isIP = ipv6result.isIPV6\n      } else {\n        parsed.host = ipv4result.host\n        isIP = true\n      }\n    }\n    if (parsed.scheme === undefined && parsed.userinfo === undefined && parsed.host === undefined && parsed.port === undefined && parsed.query === undefined && !parsed.path) {\n      parsed.reference = 'same-document'\n    } else if (parsed.scheme === undefined) {\n      parsed.reference = 'relative'\n    } else if (parsed.fragment === undefined) {\n      parsed.reference = 'absolute'\n    } else {\n      parsed.reference = 'uri'\n    }\n\n    // check for reference errors\n    if (options.reference && options.reference !== 'suffix' && options.reference !== parsed.reference) {\n      parsed.error = parsed.error || 'URI is not a ' + options.reference + ' reference.'\n    }\n\n    // find scheme handler\n    const schemeHandler = SCHEMES[(options.scheme || parsed.scheme || '').toLowerCase()]\n\n    // check if scheme can't handle IRIs\n    if (!options.unicodeSupport && (!schemeHandler || !schemeHandler.unicodeSupport)) {\n      // if host component is a domain name\n      if (parsed.host && (options.domainHost || (schemeHandler && schemeHandler.domainHost)) && isIP === false && nonSimpleDomain(parsed.host)) {\n        // convert Unicode IDN -> ASCII IDN\n        try {\n          parsed.host = URL.domainToASCII(parsed.host.toLowerCase())\n        } catch (e) {\n          parsed.error = parsed.error || \"Host's domain name can not be converted to ASCII: \" + e\n        }\n      }\n      // convert IRI -> URI\n    }\n\n    if (!schemeHandler || (schemeHandler && !schemeHandler.skipNormalize)) {\n      if (gotEncoding && parsed.scheme !== undefined) {\n        parsed.scheme = unescape(parsed.scheme)\n      }\n      if (gotEncoding && parsed.host !== undefined) {\n        parsed.host = unescape(parsed.host)\n      }\n      if (parsed.path) {\n        parsed.path = escape(unescape(parsed.path))\n      }\n      if (parsed.fragment) {\n        parsed.fragment = encodeURI(decodeURIComponent(parsed.fragment))\n      }\n    }\n\n    // perform scheme specific parsing\n    if (schemeHandler && schemeHandler.parse) {\n      schemeHandler.parse(parsed, options)\n    }\n  } else {\n    parsed.error = parsed.error || 'URI can not be parsed.'\n  }\n  return parsed\n}\n\nconst fastUri = {\n  SCHEMES,\n  normalize,\n  resolve,\n  resolveComponents,\n  equal,\n  serialize,\n  parse\n}\n\nmodule.exports = fastUri\nmodule.exports[\"default\"] = fastUri\nmodule.exports.fastUri = fastUri\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fast-uri/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/fast-uri/lib/schemes.js":
/*!**********************************************!*\
  !*** ./node_modules/fast-uri/lib/schemes.js ***!
  \**********************************************/
/***/ ((module) => {

eval("\n\nconst UUID_REG = /^[\\da-f]{8}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{4}-[\\da-f]{12}$/iu\nconst URN_REG = /([\\da-z][\\d\\-a-z]{0,31}):((?:[\\w!$'()*+,\\-.:;=@]|%[\\da-f]{2})+)/iu\n\nfunction isSecure (wsComponents) {\n  return typeof wsComponents.secure === 'boolean' ? wsComponents.secure : String(wsComponents.scheme).toLowerCase() === 'wss'\n}\n\nfunction httpParse (components) {\n  if (!components.host) {\n    components.error = components.error || 'HTTP URIs must have a host.'\n  }\n\n  return components\n}\n\nfunction httpSerialize (components) {\n  const secure = String(components.scheme).toLowerCase() === 'https'\n\n  // normalize the default port\n  if (components.port === (secure ? 443 : 80) || components.port === '') {\n    components.port = undefined\n  }\n\n  // normalize the empty path\n  if (!components.path) {\n    components.path = '/'\n  }\n\n  // NOTE: We do not parse query strings for HTTP URIs\n  // as WWW Form Url Encoded query strings are part of the HTML4+ spec,\n  // and not the HTTP spec.\n\n  return components\n}\n\nfunction wsParse (wsComponents) {\n// indicate if the secure flag is set\n  wsComponents.secure = isSecure(wsComponents)\n\n  // construct resouce name\n  wsComponents.resourceName = (wsComponents.path || '/') + (wsComponents.query ? '?' + wsComponents.query : '')\n  wsComponents.path = undefined\n  wsComponents.query = undefined\n\n  return wsComponents\n}\n\nfunction wsSerialize (wsComponents) {\n// normalize the default port\n  if (wsComponents.port === (isSecure(wsComponents) ? 443 : 80) || wsComponents.port === '') {\n    wsComponents.port = undefined\n  }\n\n  // ensure scheme matches secure flag\n  if (typeof wsComponents.secure === 'boolean') {\n    wsComponents.scheme = (wsComponents.secure ? 'wss' : 'ws')\n    wsComponents.secure = undefined\n  }\n\n  // reconstruct path from resource name\n  if (wsComponents.resourceName) {\n    const [path, query] = wsComponents.resourceName.split('?')\n    wsComponents.path = (path && path !== '/' ? path : undefined)\n    wsComponents.query = query\n    wsComponents.resourceName = undefined\n  }\n\n  // forbid fragment component\n  wsComponents.fragment = undefined\n\n  return wsComponents\n}\n\nfunction urnParse (urnComponents, options) {\n  if (!urnComponents.path) {\n    urnComponents.error = 'URN can not be parsed'\n    return urnComponents\n  }\n  const matches = urnComponents.path.match(URN_REG)\n  if (matches) {\n    const scheme = options.scheme || urnComponents.scheme || 'urn'\n    urnComponents.nid = matches[1].toLowerCase()\n    urnComponents.nss = matches[2]\n    const urnScheme = `${scheme}:${options.nid || urnComponents.nid}`\n    const schemeHandler = SCHEMES[urnScheme]\n    urnComponents.path = undefined\n\n    if (schemeHandler) {\n      urnComponents = schemeHandler.parse(urnComponents, options)\n    }\n  } else {\n    urnComponents.error = urnComponents.error || 'URN can not be parsed.'\n  }\n\n  return urnComponents\n}\n\nfunction urnSerialize (urnComponents, options) {\n  const scheme = options.scheme || urnComponents.scheme || 'urn'\n  const nid = urnComponents.nid.toLowerCase()\n  const urnScheme = `${scheme}:${options.nid || nid}`\n  const schemeHandler = SCHEMES[urnScheme]\n\n  if (schemeHandler) {\n    urnComponents = schemeHandler.serialize(urnComponents, options)\n  }\n\n  const uriComponents = urnComponents\n  const nss = urnComponents.nss\n  uriComponents.path = `${nid || options.nid}:${nss}`\n\n  options.skipEscape = true\n  return uriComponents\n}\n\nfunction urnuuidParse (urnComponents, options) {\n  const uuidComponents = urnComponents\n  uuidComponents.uuid = uuidComponents.nss\n  uuidComponents.nss = undefined\n\n  if (!options.tolerant && (!uuidComponents.uuid || !UUID_REG.test(uuidComponents.uuid))) {\n    uuidComponents.error = uuidComponents.error || 'UUID is not valid.'\n  }\n\n  return uuidComponents\n}\n\nfunction urnuuidSerialize (uuidComponents) {\n  const urnComponents = uuidComponents\n  // normalize UUID\n  urnComponents.nss = (uuidComponents.uuid || '').toLowerCase()\n  return urnComponents\n}\n\nconst http = {\n  scheme: 'http',\n  domainHost: true,\n  parse: httpParse,\n  serialize: httpSerialize\n}\n\nconst https = {\n  scheme: 'https',\n  domainHost: http.domainHost,\n  parse: httpParse,\n  serialize: httpSerialize\n}\n\nconst ws = {\n  scheme: 'ws',\n  domainHost: true,\n  parse: wsParse,\n  serialize: wsSerialize\n}\n\nconst wss = {\n  scheme: 'wss',\n  domainHost: ws.domainHost,\n  parse: ws.parse,\n  serialize: ws.serialize\n}\n\nconst urn = {\n  scheme: 'urn',\n  parse: urnParse,\n  serialize: urnSerialize,\n  skipNormalize: true\n}\n\nconst urnuuid = {\n  scheme: 'urn:uuid',\n  parse: urnuuidParse,\n  serialize: urnuuidSerialize,\n  skipNormalize: true\n}\n\nconst SCHEMES = {\n  http,\n  https,\n  ws,\n  wss,\n  urn,\n  'urn:uuid': urnuuid\n}\n\nmodule.exports = SCHEMES\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fast-uri/lib/schemes.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/fast-uri/lib/scopedChars.js":
/*!**************************************************!*\
  !*** ./node_modules/fast-uri/lib/scopedChars.js ***!
  \**************************************************/
/***/ ((module) => {

eval("\n\nconst HEX = {\n  0: 0,\n  1: 1,\n  2: 2,\n  3: 3,\n  4: 4,\n  5: 5,\n  6: 6,\n  7: 7,\n  8: 8,\n  9: 9,\n  a: 10,\n  A: 10,\n  b: 11,\n  B: 11,\n  c: 12,\n  C: 12,\n  d: 13,\n  D: 13,\n  e: 14,\n  E: 14,\n  f: 15,\n  F: 15\n}\n\nmodule.exports = {\n  HEX\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mYXN0LXVyaS9saWIvc2NvcGVkQ2hhcnMuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRTpcXGJvdFxcdHJhZGluZ2JvdF9maW5hbFxcZnJvbnRlbmRcXG5vZGVfbW9kdWxlc1xcZmFzdC11cmlcXGxpYlxcc2NvcGVkQ2hhcnMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbmNvbnN0IEhFWCA9IHtcbiAgMDogMCxcbiAgMTogMSxcbiAgMjogMixcbiAgMzogMyxcbiAgNDogNCxcbiAgNTogNSxcbiAgNjogNixcbiAgNzogNyxcbiAgODogOCxcbiAgOTogOSxcbiAgYTogMTAsXG4gIEE6IDEwLFxuICBiOiAxMSxcbiAgQjogMTEsXG4gIGM6IDEyLFxuICBDOiAxMixcbiAgZDogMTMsXG4gIEQ6IDEzLFxuICBlOiAxNCxcbiAgRTogMTQsXG4gIGY6IDE1LFxuICBGOiAxNVxufVxuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgSEVYXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fast-uri/lib/scopedChars.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/fast-uri/lib/utils.js":
/*!********************************************!*\
  !*** ./node_modules/fast-uri/lib/utils.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst { HEX } = __webpack_require__(/*! ./scopedChars */ \"(action-browser)/./node_modules/fast-uri/lib/scopedChars.js\")\n\nconst IPV4_REG = /^(?:(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|1\\d{2}|[1-9]\\d|\\d)$/u\n\nfunction normalizeIPv4 (host) {\n  if (findToken(host, '.') < 3) { return { host, isIPV4: false } }\n  const matches = host.match(IPV4_REG) || []\n  const [address] = matches\n  if (address) {\n    return { host: stripLeadingZeros(address, '.'), isIPV4: true }\n  } else {\n    return { host, isIPV4: false }\n  }\n}\n\n/**\n * @param {string[]} input\n * @param {boolean} [keepZero=false]\n * @returns {string|undefined}\n */\nfunction stringArrayToHexStripped (input, keepZero = false) {\n  let acc = ''\n  let strip = true\n  for (const c of input) {\n    if (HEX[c] === undefined) return undefined\n    if (c !== '0' && strip === true) strip = false\n    if (!strip) acc += c\n  }\n  if (keepZero && acc.length === 0) acc = '0'\n  return acc\n}\n\nfunction getIPV6 (input) {\n  let tokenCount = 0\n  const output = { error: false, address: '', zone: '' }\n  const address = []\n  const buffer = []\n  let isZone = false\n  let endipv6Encountered = false\n  let endIpv6 = false\n\n  function consume () {\n    if (buffer.length) {\n      if (isZone === false) {\n        const hex = stringArrayToHexStripped(buffer)\n        if (hex !== undefined) {\n          address.push(hex)\n        } else {\n          output.error = true\n          return false\n        }\n      }\n      buffer.length = 0\n    }\n    return true\n  }\n\n  for (let i = 0; i < input.length; i++) {\n    const cursor = input[i]\n    if (cursor === '[' || cursor === ']') { continue }\n    if (cursor === ':') {\n      if (endipv6Encountered === true) {\n        endIpv6 = true\n      }\n      if (!consume()) { break }\n      tokenCount++\n      address.push(':')\n      if (tokenCount > 7) {\n        // not valid\n        output.error = true\n        break\n      }\n      if (i - 1 >= 0 && input[i - 1] === ':') {\n        endipv6Encountered = true\n      }\n      continue\n    } else if (cursor === '%') {\n      if (!consume()) { break }\n      // switch to zone detection\n      isZone = true\n    } else {\n      buffer.push(cursor)\n      continue\n    }\n  }\n  if (buffer.length) {\n    if (isZone) {\n      output.zone = buffer.join('')\n    } else if (endIpv6) {\n      address.push(buffer.join(''))\n    } else {\n      address.push(stringArrayToHexStripped(buffer))\n    }\n  }\n  output.address = address.join('')\n  return output\n}\n\nfunction normalizeIPv6 (host) {\n  if (findToken(host, ':') < 2) { return { host, isIPV6: false } }\n  const ipv6 = getIPV6(host)\n\n  if (!ipv6.error) {\n    let newHost = ipv6.address\n    let escapedHost = ipv6.address\n    if (ipv6.zone) {\n      newHost += '%' + ipv6.zone\n      escapedHost += '%25' + ipv6.zone\n    }\n    return { host: newHost, escapedHost, isIPV6: true }\n  } else {\n    return { host, isIPV6: false }\n  }\n}\n\nfunction stripLeadingZeros (str, token) {\n  let out = ''\n  let skip = true\n  const l = str.length\n  for (let i = 0; i < l; i++) {\n    const c = str[i]\n    if (c === '0' && skip) {\n      if ((i + 1 <= l && str[i + 1] === token) || i + 1 === l) {\n        out += c\n        skip = false\n      }\n    } else {\n      if (c === token) {\n        skip = true\n      } else {\n        skip = false\n      }\n      out += c\n    }\n  }\n  return out\n}\n\nfunction findToken (str, token) {\n  let ind = 0\n  for (let i = 0; i < str.length; i++) {\n    if (str[i] === token) ind++\n  }\n  return ind\n}\n\nconst RDS1 = /^\\.\\.?\\//u\nconst RDS2 = /^\\/\\.(?:\\/|$)/u\nconst RDS3 = /^\\/\\.\\.(?:\\/|$)/u\nconst RDS5 = /^\\/?(?:.|\\n)*?(?=\\/|$)/u\n\nfunction removeDotSegments (input) {\n  const output = []\n\n  while (input.length) {\n    if (input.match(RDS1)) {\n      input = input.replace(RDS1, '')\n    } else if (input.match(RDS2)) {\n      input = input.replace(RDS2, '/')\n    } else if (input.match(RDS3)) {\n      input = input.replace(RDS3, '/')\n      output.pop()\n    } else if (input === '.' || input === '..') {\n      input = ''\n    } else {\n      const im = input.match(RDS5)\n      if (im) {\n        const s = im[0]\n        input = input.slice(s.length)\n        output.push(s)\n      } else {\n        throw new Error('Unexpected dot segment condition')\n      }\n    }\n  }\n  return output.join('')\n}\n\nfunction normalizeComponentEncoding (components, esc) {\n  const func = esc !== true ? escape : unescape\n  if (components.scheme !== undefined) {\n    components.scheme = func(components.scheme)\n  }\n  if (components.userinfo !== undefined) {\n    components.userinfo = func(components.userinfo)\n  }\n  if (components.host !== undefined) {\n    components.host = func(components.host)\n  }\n  if (components.path !== undefined) {\n    components.path = func(components.path)\n  }\n  if (components.query !== undefined) {\n    components.query = func(components.query)\n  }\n  if (components.fragment !== undefined) {\n    components.fragment = func(components.fragment)\n  }\n  return components\n}\n\nfunction recomposeAuthority (components) {\n  const uriTokens = []\n\n  if (components.userinfo !== undefined) {\n    uriTokens.push(components.userinfo)\n    uriTokens.push('@')\n  }\n\n  if (components.host !== undefined) {\n    let host = unescape(components.host)\n    const ipV4res = normalizeIPv4(host)\n\n    if (ipV4res.isIPV4) {\n      host = ipV4res.host\n    } else {\n      const ipV6res = normalizeIPv6(ipV4res.host)\n      if (ipV6res.isIPV6 === true) {\n        host = `[${ipV6res.escapedHost}]`\n      } else {\n        host = components.host\n      }\n    }\n    uriTokens.push(host)\n  }\n\n  if (typeof components.port === 'number' || typeof components.port === 'string') {\n    uriTokens.push(':')\n    uriTokens.push(String(components.port))\n  }\n\n  return uriTokens.length ? uriTokens.join('') : undefined\n};\n\nmodule.exports = {\n  recomposeAuthority,\n  normalizeComponentEncoding,\n  removeDotSegments,\n  normalizeIPv4,\n  normalizeIPv6,\n  stringArrayToHexStripped\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/fast-uri/lib/utils.js\n");

/***/ })

};
;