nohup: ignoring input

> nextn@0.1.0 dev
> next dev -p 9002

   ▲ Next.js 15.2.3
   - Local:        http://localhost:9002
   - Network:      http://************:9002
   - Environments: .env.local, .env

 ✓ Starting...
Attention: Next.js now collects completely anonymous telemetry regarding usage.
This information is used to shape Next.js' roadmap and prioritize features.
You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
https://nextjs.org/telemetry

 ✓ Ready in 1661ms
 ○ Compiling / ...
 ⚠ ./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
Module not found: Can't resolve '@opentelemetry/exporter-jaeger' in '/home/<USER>/trading_bot_project/tradingbot_final/frontend/node_modules/@opentelemetry/sdk-node/build/src'

Import trace for requested module:
./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
./node_modules/@opentelemetry/sdk-node/build/src/sdk.js
./node_modules/@opentelemetry/sdk-node/build/src/index.js
./node_modules/@genkit-ai/core/lib/tracing.js
./node_modules/genkit/lib/tracing.js
./node_modules/@genkit-ai/googleai/lib/gemini.js
./node_modules/@genkit-ai/googleai/lib/index.mjs
./src/ai/genkit.ts
./src/ai/flows/trading-mode-suggestion.ts
API Base URL: http://localhost:5000
 GET / 200 in 13861ms
 ⚠ ./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
Module not found: Can't resolve '@opentelemetry/exporter-jaeger' in '/home/<USER>/trading_bot_project/tradingbot_final/frontend/node_modules/@opentelemetry/sdk-node/build/src'

Import trace for requested module:
./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
./node_modules/@opentelemetry/sdk-node/build/src/sdk.js
./node_modules/@opentelemetry/sdk-node/build/src/index.js
./node_modules/@genkit-ai/core/lib/tracing.js
./node_modules/genkit/lib/tracing.js
./node_modules/@genkit-ai/googleai/lib/gemini.js
./node_modules/@genkit-ai/googleai/lib/index.mjs
./src/ai/genkit.ts
./src/ai/flows/trading-mode-suggestion.ts
 ⚠ ./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
Module not found: Can't resolve '@opentelemetry/exporter-jaeger' in '/home/<USER>/trading_bot_project/tradingbot_final/frontend/node_modules/@opentelemetry/sdk-node/build/src'

Import trace for requested module:
./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
./node_modules/@opentelemetry/sdk-node/build/src/sdk.js
./node_modules/@opentelemetry/sdk-node/build/src/index.js
./node_modules/@genkit-ai/core/lib/tracing.js
./node_modules/genkit/lib/tracing.js
./node_modules/@genkit-ai/googleai/lib/gemini.js
./node_modules/@genkit-ai/googleai/lib/index.mjs
./src/ai/genkit.ts
./src/ai/flows/trading-mode-suggestion.ts
 GET /login 200 in 3584ms
API Base URL: http://localhost:5000
 GET /login 200 in 129ms
 GET /login 200 in 43ms
 GET /login 200 in 50ms
 GET /login 200 in 44ms
 ⚠ ./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
Module not found: Can't resolve '@opentelemetry/exporter-jaeger' in '/home/<USER>/trading_bot_project/tradingbot_final/frontend/node_modules/@opentelemetry/sdk-node/build/src'

Import trace for requested module:
./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
./node_modules/@opentelemetry/sdk-node/build/src/sdk.js
./node_modules/@opentelemetry/sdk-node/build/src/index.js
./node_modules/@genkit-ai/core/lib/tracing.js
./node_modules/genkit/lib/tracing.js
./node_modules/@genkit-ai/googleai/lib/gemini.js
./node_modules/@genkit-ai/googleai/lib/index.mjs
./src/ai/genkit.ts
./src/ai/flows/trading-mode-suggestion.ts
 ⚠ ./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
Module not found: Can't resolve '@opentelemetry/exporter-jaeger' in '/home/<USER>/trading_bot_project/tradingbot_final/frontend/node_modules/@opentelemetry/sdk-node/build/src'

Import trace for requested module:
./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
./node_modules/@opentelemetry/sdk-node/build/src/sdk.js
./node_modules/@opentelemetry/sdk-node/build/src/index.js
./node_modules/@genkit-ai/core/lib/tracing.js
./node_modules/genkit/lib/tracing.js
./node_modules/@genkit-ai/googleai/lib/gemini.js
./node_modules/@genkit-ai/googleai/lib/index.mjs
./src/ai/genkit.ts
./src/ai/flows/trading-mode-suggestion.ts
 GET /dashboard 200 in 3625ms
 GET /dashboard 200 in 37ms
API Base URL: http://localhost:5000
 GET /dashboard 200 in 189ms
 ⚠ ./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
Module not found: Can't resolve '@opentelemetry/exporter-jaeger' in '/home/<USER>/trading_bot_project/tradingbot_final/frontend/node_modules/@opentelemetry/sdk-node/build/src'

Import trace for requested module:
./node_modules/@opentelemetry/sdk-node/build/src/TracerProviderWithEnvExporter.js
./node_modules/@opentelemetry/sdk-node/build/src/sdk.js
./node_modules/@opentelemetry/sdk-node/build/src/index.js
./node_modules/@genkit-ai/core/lib/tracing.js
./node_modules/genkit/lib/tracing.js
./node_modules/@genkit-ai/googleai/lib/gemini.js
./node_modules/@genkit-ai/googleai/lib/index.mjs
./src/ai/genkit.ts
./src/ai/flows/trading-mode-suggestion.ts
 ⚠ Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
 ⚠ Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
API Base URL: http://localhost:5000
 GET /dashboard 200 in 276ms
 GET /dashboard 200 in 40ms
 GET /dashboard 200 in 44ms
