from db import db
from datetime import datetime
from sqlalchemy.orm import relationship

class Trade(db.Model):
    """Model to store trade history."""
    __tablename__ = 'trades'
    __table_args__ = {'extend_existing': True}
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.In<PERSON>ger, db.ForeignKey('users.id'), nullable=False)
    config_id = db.Column(db.Integer, db.ForeignKey('trading_configurations.id'), nullable=False)
    session_id = db.Column(db.Integer, db.ForeignKey('trading_sessions.id'), nullable=True)  # Link to trading session
    exchange_order_id = db.Column(db.String(100), nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    pair = db.Column(db.String(20), nullable=False)  # e.g., BTC/USDT
    crypto1 = db.Column(db.String(10), nullable=False)  # Base currency
    crypto2 = db.Column(db.String(10), nullable=False)  # Quote currency
    order_type = db.Column(db.String(10), nullable=False)  # 'BUY' or 'SELL'
    amount_crypto1 = db.Column(db.Float, nullable=False)  # Amount of base currency
    avg_price = db.Column(db.Float, nullable=False)  # Execution price
    value_crypto2 = db.Column(db.Float, nullable=False)  # Total value in quote currency
    fees = db.Column(db.Float, nullable=True)  # Fees paid for the trade
    realized_pnl_crypto2 = db.Column(db.Float, nullable=True)  # For SELL orders, profit/loss in crypto2
    target_price_id = db.Column(db.String(36), nullable=True)  # UUID of the target price that triggered this trade
    
    # Relationships
    user = relationship("User", back_populates="trades")
    trading_config = relationship("TradingConfiguration", back_populates="trades")
    trading_session = relationship("TradingSession", back_populates="trades")
    
    def __init__(self, user_id, config_id, pair, crypto1, crypto2, order_type,
                 amount_crypto1, avg_price, value_crypto2, exchange_order_id=None,
                 fees=None, realized_pnl_crypto2=None, target_price_id=None, session_id=None):
        self.user_id = user_id
        self.config_id = config_id
        self.session_id = session_id
        self.exchange_order_id = exchange_order_id
        self.pair = pair
        self.crypto1 = crypto1
        self.crypto2 = crypto2
        self.order_type = order_type
        self.amount_crypto1 = amount_crypto1
        self.avg_price = avg_price
        self.value_crypto2 = value_crypto2
        self.fees = fees
        self.realized_pnl_crypto2 = realized_pnl_crypto2
        self.target_price_id = target_price_id
    
    def to_dict(self):
        """Convert model to dictionary for API responses."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'config_id': self.config_id,
            'exchange_order_id': self.exchange_order_id,
            'timestamp': self.timestamp.isoformat(),
            'pair': self.pair,
            'crypto1': self.crypto1,
            'crypto2': self.crypto2,
            'order_type': self.order_type,
            'amount_crypto1': self.amount_crypto1,
            'avg_price': self.avg_price,
            'value_crypto2': self.value_crypto2,
            'fees': self.fees,
            'realized_pnl_crypto2': self.realized_pnl_crypto2,
            'target_price_id': self.target_price_id
        } 