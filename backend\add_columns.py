#!/usr/bin/env python3
"""
Simple script to add the missing cooldown tracking columns to the database.
This uses direct SQLite commands to add the columns.
"""

import sqlite3
import os
import sys

def find_database():
    """Find the SQLite database file."""
    possible_paths = [
        'instance/pluto.db',  # Found this one!
        'instance/trading_bot.db',
        'instance/app.db',
        'pluto.db',
        'trading_bot.db',
        'app.db',
        '../instance/pluto.db',
        '../instance/trading_bot.db',
        '../instance/app.db'
    ]

    for path in possible_paths:
        if os.path.exists(path):
            return path

    return None

def add_cooldown_columns():
    """Add the cooldown tracking columns to the database."""

    db_path = find_database()
    if not db_path:
        print("❌ Database file not found!")
        print("Searched in:")
        for path in ['instance/trading_bot.db', 'instance/app.db', 'trading_bot.db', 'app.db']:
            print(f"  - {path}")
        return False

    print(f"📁 Found database: {db_path}")

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Check current schema
        cursor.execute("PRAGMA table_info(bot_states)")
        columns = [row[1] for row in cursor.fetchall()]
        print(f"📊 Current columns: {columns}")

        # Add last_global_buy_timestamp if missing
        if 'last_global_buy_timestamp' not in columns:
            print("➕ Adding last_global_buy_timestamp column...")
            cursor.execute("ALTER TABLE bot_states ADD COLUMN last_global_buy_timestamp REAL DEFAULT 0.0")
            print("✅ Added last_global_buy_timestamp")
        else:
            print("✅ last_global_buy_timestamp already exists")

        # Add last_sell_timestamp_per_counter if missing
        if 'last_sell_timestamp_per_counter' not in columns:
            print("➕ Adding last_sell_timestamp_per_counter column...")
            cursor.execute("ALTER TABLE bot_states ADD COLUMN last_sell_timestamp_per_counter TEXT DEFAULT '{}'")
            print("✅ Added last_sell_timestamp_per_counter")
        else:
            print("✅ last_sell_timestamp_per_counter already exists")

        # Initialize existing records
        print("🔄 Initializing existing records...")
        cursor.execute("UPDATE bot_states SET last_global_buy_timestamp = 0.0 WHERE last_global_buy_timestamp IS NULL")
        cursor.execute("UPDATE bot_states SET last_sell_timestamp_per_counter = '{}' WHERE last_sell_timestamp_per_counter IS NULL OR last_sell_timestamp_per_counter = ''")

        conn.commit()

        # Verify
        cursor.execute("PRAGMA table_info(bot_states)")
        new_columns = [row[1] for row in cursor.fetchall()]
        print(f"📊 Updated columns: {new_columns}")

        conn.close()

        print("✅ Database migration completed successfully!")
        print("🎯 Crystal clear cooldown tracking is now ready!")
        return True

    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Adding cooldown tracking columns to database...")
    success = add_cooldown_columns()
    if success:
        print("\n🚀 You can now restart the backend!")
        print("The bot will now support:")
        print("  - ⏰ Global buy cooldown (5 seconds)")
        print("  - 📊 Per-counter sell cooldown (60 seconds)")
        print("  - 🎯 Sequential buying when multiple targets are in range")
    else:
        print("\n❌ Migration failed. Please check the error above.")
