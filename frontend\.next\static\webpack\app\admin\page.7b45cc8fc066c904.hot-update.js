"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./src/components/admin/SessionManager.tsx":
/*!*************************************************!*\
  !*** ./src/components/admin/SessionManager.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pen.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/folder-open.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,Clock,Download,Edit2,FileText,FolderOpen,Save,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/session-manager */ \"(app-pages-browser)/./src/lib/session-manager.ts\");\n/* harmony import */ var _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/TradingContext */ \"(app-pages-browser)/./src/contexts/TradingContext.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ SessionManager auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction SessionManager() {\n    _s();\n    const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = (0,_contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingSessionId, setEditingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingName, setEditingName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [currentRuntime, setCurrentRuntime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [alarmModalOpen, setAlarmModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAlarmSession, setSelectedAlarmSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const sessionManager = _lib_session_manager__WEBPACK_IMPORTED_MODULE_7__.SessionManager.getInstance();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            loadSessions();\n            const sessionId = sessionManager.getCurrentSessionId();\n            setCurrentSessionId(sessionId);\n            // Initialize current runtime\n            if (sessionId) {\n                const runtime = sessionManager.getCurrentRuntime(sessionId);\n                setCurrentRuntime(runtime);\n            }\n            // Listen for storage changes to sync sessions across windows\n            const handleStorageChange = {\n                \"SessionManager.useEffect.handleStorageChange\": (event)=>{\n                    if (event.key === 'trading_sessions' && event.newValue) {\n                        loadSessions();\n                        console.log('🔄 Sessions synced from another window');\n                    }\n                }\n            }[\"SessionManager.useEffect.handleStorageChange\"];\n            window.addEventListener('storage', handleStorageChange);\n            return ({\n                \"SessionManager.useEffect\": ()=>{\n                    window.removeEventListener('storage', handleStorageChange);\n                }\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], []);\n    // Update runtime display every second for active sessions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SessionManager.useEffect\": ()=>{\n            const interval = setInterval({\n                \"SessionManager.useEffect.interval\": ()=>{\n                    if (currentSessionId) {\n                        const runtime = sessionManager.getCurrentRuntime(currentSessionId);\n                        setCurrentRuntime(runtime);\n                        console.log(\"⏱️ Runtime update: \".concat(formatRuntime(runtime), \" for session \").concat(currentSessionId));\n                    }\n                }\n            }[\"SessionManager.useEffect.interval\"], 5000); // Update every 5 seconds instead of 1 second\n            return ({\n                \"SessionManager.useEffect\": ()=>clearInterval(interval)\n            })[\"SessionManager.useEffect\"];\n        }\n    }[\"SessionManager.useEffect\"], [\n        currentSessionId,\n        sessionManager\n    ]);\n    const loadSessions = ()=>{\n        const allSessions = sessionManager.getAllSessions();\n        setSessions(allSessions.sort((a, b)=>b.lastModified - a.lastModified));\n    };\n    const handleSaveCurrentSession = async ()=>{\n        if (!currentSessionId) {\n            toast({\n                title: \"Error\",\n                description: \"No active session to save\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        try {\n            // Check if there's already a saved version of this session\n            const currentSession = sessionManager.loadSession(currentSessionId);\n            if (!currentSession) {\n                toast({\n                    title: \"Error\",\n                    description: \"Current session not found\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Look for existing saved session with same base name (only manually saved ones)\n            const allSessions = sessionManager.getAllSessions();\n            const baseName = currentSession.name.replace(/ \\((Saved|AutoSaved).*\\)$/, ''); // Remove existing timestamp\n            const existingSavedSession = allSessions.find((s)=>s.id !== currentSessionId && s.name.startsWith(baseName) && s.name.includes('(Saved') && // Only look for manually saved sessions\n                !s.isActive // Only look in inactive sessions\n            );\n            let targetSessionId;\n            let savedName;\n            if (existingSavedSession) {\n                // Update existing saved session - update the timestamp to show latest save\n                targetSessionId = existingSavedSession.id;\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                console.log(\"\\uD83D\\uDCDD Updating existing saved session: \".concat(savedName));\n            } else {\n                // Create new saved session with timestamp\n                const timestamp = new Date().toLocaleString('en-US', {\n                    month: 'short',\n                    day: 'numeric',\n                    hour: '2-digit',\n                    minute: '2-digit',\n                    hour12: false\n                });\n                savedName = \"\".concat(baseName, \" (Saved \").concat(timestamp, \")\");\n                targetSessionId = await sessionManager.createNewSession(savedName, config);\n                console.log(\"\\uD83D\\uDCBE Creating new saved session: \".concat(savedName));\n            }\n            // Get current runtime from the active session\n            const currentRuntime = sessionManager.getCurrentRuntime(currentSessionId);\n            // Update the session name if it's an existing session\n            if (existingSavedSession) {\n                sessionManager.renameSession(targetSessionId, savedName);\n            }\n            // Save/update the session with current data and runtime\n            const success = sessionManager.saveSession(targetSessionId, config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, false, currentRuntime // Pass the current runtime to the saved session\n            );\n            // DO NOT deactivate the current session - keep it running!\n            // The current session should remain active for continued trading\n            if (success) {\n                loadSessions();\n                toast({\n                    title: \"Session Saved\",\n                    description: existingSavedSession ? \"Save checkpoint updated (Runtime: \".concat(formatRuntime(currentRuntime), \")\") : \"Session saved as checkpoint (Runtime: \".concat(formatRuntime(currentRuntime), \")\")\n                });\n            } else {\n                toast({\n                    title: \"Error\",\n                    description: \"Failed to save session\",\n                    variant: \"destructive\"\n                });\n            }\n        } catch (error) {\n            console.error('Error saving session:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to save session\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleLoadSession = (sessionId)=>{\n        const session = sessionManager.loadSession(sessionId);\n        if (!session) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to load session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Load session data into context\n        dispatch({\n            type: 'SET_CONFIG',\n            payload: session.config\n        });\n        dispatch({\n            type: 'SET_TARGET_PRICE_ROWS',\n            payload: session.targetPriceRows\n        });\n        dispatch({\n            type: 'CLEAR_ORDER_HISTORY'\n        });\n        session.orderHistory.forEach((entry)=>{\n            dispatch({\n                type: 'ADD_ORDER_HISTORY_ENTRY',\n                payload: entry\n            });\n        });\n        dispatch({\n            type: 'SET_MARKET_PRICE',\n            payload: session.currentMarketPrice\n        });\n        dispatch({\n            type: 'SET_BALANCES',\n            payload: {\n                crypto1: session.crypto1Balance,\n                crypto2: session.crypto2Balance\n            }\n        });\n        sessionManager.setCurrentSession(sessionId);\n        setCurrentSessionId(sessionId);\n        loadSessions();\n        toast({\n            title: \"Session Loaded\",\n            description: 'Session \"'.concat(session.name, '\" has been loaded')\n        });\n    };\n    const handleDeleteSession = (sessionId)=>{\n        const success = sessionManager.deleteSession(sessionId);\n        if (success) {\n            if (currentSessionId === sessionId) {\n                setCurrentSessionId(null);\n            }\n            loadSessions();\n            toast({\n                title: \"Session Deleted\",\n                description: \"Session has been deleted successfully\"\n            });\n        }\n    };\n    const handleRenameSession = (sessionId)=>{\n        if (!editingName.trim()) return;\n        const success = sessionManager.renameSession(sessionId, editingName.trim());\n        if (success) {\n            setEditingSessionId(null);\n            setEditingName('');\n            loadSessions();\n            toast({\n                title: \"Session Renamed\",\n                description: \"Session has been renamed successfully\"\n            });\n        }\n    };\n    const handleExportSession = (sessionId)=>{\n        const csvContent = sessionManager.exportSessionToCSV(sessionId);\n        if (!csvContent) {\n            toast({\n                title: \"Error\",\n                description: \"Failed to export session\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const session = sessionManager.loadSession(sessionId);\n        const blob = new Blob([\n            csvContent\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const link = document.createElement('a');\n        const url = URL.createObjectURL(blob);\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"\".concat((session === null || session === void 0 ? void 0 : session.name) || 'session', \"_\").concat(new Date().toISOString().split('T')[0], \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        toast({\n            title: \"Export Complete\",\n            description: \"Session data has been exported to CSV\"\n        });\n    };\n    const handleOpenAlarmModal = (sessionId, sessionName)=>{\n        setSelectedAlarmSession({\n            id: sessionId,\n            name: sessionName\n        });\n        setAlarmModalOpen(true);\n    };\n    const handleCloseAlarmModal = ()=>{\n        setAlarmModalOpen(false);\n        setSelectedAlarmSession(null);\n    };\n    const formatRuntime = (runtime)=>{\n        if (!runtime || runtime < 0) return '0s';\n        const totalSeconds = Math.floor(runtime / 1000);\n        const hours = Math.floor(totalSeconds / 3600);\n        const minutes = Math.floor(totalSeconds % 3600 / 60);\n        const seconds = totalSeconds % 60;\n        if (hours > 0) {\n            return \"\".concat(hours, \"h \").concat(minutes, \"m \").concat(seconds, \"s\");\n        } else if (minutes > 0) {\n            return \"\".concat(minutes, \"m \").concat(seconds, \"s\");\n        } else {\n            return \"\".concat(seconds, \"s\");\n        }\n    };\n    const getCurrentSession = ()=>{\n        return sessions.find((s)=>s.id === currentSessionId);\n    };\n    const getActiveSessions = ()=>{\n        return sessions.filter((s)=>s.isActive);\n    };\n    const getInactiveSessions = ()=>{\n        return sessions.filter((s)=>!s.isActive);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 13\n                                }, this),\n                                \"Current Sessions\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: getActiveSessions().length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Session Name\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Active Status\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Runtime\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"Actions\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: getActiveSessions().map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                value: editingName,\n                                                                onChange: (e)=>setEditingName(e.target.value),\n                                                                onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                className: \"text-sm\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                onClick: ()=>handleRenameSession(session.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 365,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: session.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 370,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"ghost\",\n                                                                onClick: ()=>{\n                                                                    setEditingSessionId(session.id);\n                                                                    setEditingName(session.name);\n                                                                },\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 379,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 371,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"default\",\n                                                        children: \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm\",\n                                                    children: session.id === currentSessionId ? formatRuntime(currentRuntime) : formatRuntime(session.runtime)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: session.id === currentSessionId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                        onClick: handleSaveCurrentSession,\n                                                        size: \"sm\",\n                                                        className: \"btn-neo\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"mr-2 h-3 w-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"Save\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 407,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 406,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, session.id, true, {\n                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"No active session\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs\",\n                                    children: \"Start trading to create a session automatically\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 417,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-card-foreground/5 border-border border-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Past Sessions (\",\n                                    getInactiveSessions().length,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: [\n                                    \"Auto-saved: \",\n                                    getInactiveSessions().length,\n                                    \" | Manual: 0\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_6__.ScrollArea, {\n                            className: \"h-[400px]\",\n                            children: getInactiveSessions().length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-muted-foreground py-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"h-12 w-12 mx-auto mb-4 opacity-50\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"No saved sessions yet.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs\",\n                                        children: \"Save your current session to get started.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Session Name\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Active Status\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Total Runtime\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: getInactiveSessions().map((session)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: editingSessionId === session.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2 flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                                    value: editingName,\n                                                                    onChange: (e)=>setEditingName(e.target.value),\n                                                                    onKeyPress: (e)=>e.key === 'Enter' && handleRenameSession(session.id),\n                                                                    className: \"text-sm\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    onClick: ()=>handleRenameSession(session.id),\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 471,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 463,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: session.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 476,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>{\n                                                                        setEditingSessionId(session.id);\n                                                                        setEditingName(session.name);\n                                                                    },\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                        lineNumber: 485,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 477,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                            variant: \"secondary\",\n                                                            children: \"Inactive\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm\",\n                                                        children: formatRuntime(sessionManager.getCurrentRuntime(session.id))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 497,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleLoadSession(session.id),\n                                                                title: \"Load Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 503,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleExportSession(session.id),\n                                                                title: \"Export Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteSession(session.id),\n                                                                title: \"Delete Session\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_Clock_Download_Edit2_FileText_FolderOpen_Save_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                    lineNumber: 509,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                        lineNumber: 501,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, session.id, true, {\n                                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                        lineNumber: 439,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n                lineNumber: 429,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\bot\\\\tradingbot_final\\\\frontend\\\\src\\\\components\\\\admin\\\\SessionManager.tsx\",\n        lineNumber: 331,\n        columnNumber: 5\n    }, this);\n}\n_s(SessionManager, \"L6FUFDQbKwrSFucr8UkAOpKzGbs=\", false, function() {\n    return [\n        _contexts_TradingContext__WEBPACK_IMPORTED_MODULE_8__.useTradingContext,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast\n    ];\n});\n_c = SessionManager;\nvar _c;\n$RefreshReg$(_c, \"SessionManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/SessionManager.tsx\n"));

/***/ })

});