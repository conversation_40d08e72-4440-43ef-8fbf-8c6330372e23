"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  Save,
  FolderOpen,
  Trash2,
  Download,
  Upload,
  Edit2,
  Clock,
  TrendingUp,
  Activity,
  FileText,
  Volume2
} from 'lucide-react';
import { SessionManager as SessionManagerService } from '@/lib/session-manager';
import { useTradingContext } from '@/contexts/TradingContext';
import { useToast } from '@/hooks/use-toast';
import type { SessionMetadata } from '@/lib/types';
import { format } from 'date-fns';
import SessionAlarmModal from '@/components/modals/SessionAlarmModal';

export function SessionManager() {
  const { config, targetPriceRows, orderHistory, currentMarketPrice, crypto1Balance, crypto2Balance, stablecoinBalance, botSystemStatus, dispatch } = useTradingContext();
  const { toast } = useToast();
  const [sessions, setSessions] = useState<SessionMetadata[]>([]);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [editingSessionId, setEditingSessionId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [currentRuntime, setCurrentRuntime] = useState<number>(0);
  const [alarmModalOpen, setAlarmModalOpen] = useState(false);
  const [selectedAlarmSession, setSelectedAlarmSession] = useState<{ id: string; name: string } | null>(null);
  const sessionManager = SessionManagerService.getInstance();

  useEffect(() => {
    loadSessions();
    const sessionId = sessionManager.getCurrentSessionId();
    setCurrentSessionId(sessionId);

    // Initialize current runtime
    if (sessionId) {
      const runtime = sessionManager.getCurrentRuntime(sessionId);
      setCurrentRuntime(runtime);
    }

    // Listen for storage changes to sync sessions across windows
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'trading_sessions' && event.newValue) {
        loadSessions();
        console.log('🔄 Sessions synced from another window');
      }
    };

    window.addEventListener('storage', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // Update runtime display every second for active sessions
  useEffect(() => {
    const interval = setInterval(() => {
      if (currentSessionId) {
        const runtime = sessionManager.getCurrentRuntime(currentSessionId);
        setCurrentRuntime(runtime);
        console.log(`⏱️ Runtime update: ${formatRuntime(runtime)} for session ${currentSessionId}`);
      }
    }, 5000); // Update every 5 seconds instead of 1 second

    return () => clearInterval(interval);
  }, [currentSessionId, sessionManager]);

  const loadSessions = () => {
    const allSessions = sessionManager.getAllSessions();
    setSessions(allSessions.sort((a, b) => b.lastModified - a.lastModified));
  };



  const handleSaveCurrentSession = async () => {
    if (!currentSessionId) {
      toast({
        title: "Error",
        description: "No active session to save",
        variant: "destructive"
      });
      return;
    }

    try {
      // Check if there's already a saved version of this session
      const currentSession = sessionManager.loadSession(currentSessionId);
      if (!currentSession) {
        toast({
          title: "Error",
          description: "Current session not found",
          variant: "destructive"
        });
        return;
      }

      // Look for existing saved session with same base name (only manually saved ones)
      const allSessions = sessionManager.getAllSessions();
      const baseName = currentSession.name.replace(/ \((Saved|AutoSaved).*\)$/, ''); // Remove existing timestamp
      const existingSavedSession = allSessions.find(s =>
        s.id !== currentSessionId &&
        s.name.startsWith(baseName) &&
        s.name.includes('(Saved') && // Only look for manually saved sessions
        !s.isActive // Only look in inactive sessions
      );

      let targetSessionId: string;
      let savedName: string;

      if (existingSavedSession) {
        // Update existing saved session - update the timestamp to show latest save
        targetSessionId = existingSavedSession.id;
        const timestamp = new Date().toLocaleString('en-US', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        });
        savedName = `${baseName} (Saved ${timestamp})`;
        console.log(`📝 Updating existing saved session: ${savedName}`);
      } else {
        // Create new saved session with timestamp
        const timestamp = new Date().toLocaleString('en-US', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          hour12: false
        });
        savedName = `${baseName} (Saved ${timestamp})`;
        targetSessionId = await sessionManager.createNewSession(savedName, config);
        console.log(`💾 Creating new saved session: ${savedName}`);
      }

      // Get current runtime from the active session
      const currentRuntime = sessionManager.getCurrentRuntime(currentSessionId);

      // Update the session name if it's an existing session
      if (existingSavedSession) {
        sessionManager.renameSession(targetSessionId, savedName);
      }

      // Save/update the session with current data and runtime
      const success = sessionManager.saveSession(
        targetSessionId,
        config,
        targetPriceRows,
        orderHistory,
        currentMarketPrice,
        crypto1Balance,
        crypto2Balance,
        stablecoinBalance,
        false, // Saved session is not active
        currentRuntime // Pass the current runtime to the saved session
      );

      // DO NOT deactivate the current session - keep it running!
      // The current session should remain active for continued trading

      if (success) {
        loadSessions();
        toast({
          title: "Session Saved",
          description: existingSavedSession
            ? `Save checkpoint updated (Runtime: ${formatRuntime(currentRuntime)})`
            : `Session saved as checkpoint (Runtime: ${formatRuntime(currentRuntime)})`,
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to save session",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('Error saving session:', error);
      toast({
        title: "Error",
        description: "Failed to save session",
        variant: "destructive"
      });
    }
  };

  const handleLoadSession = (sessionId: string) => {
    const session = sessionManager.loadSession(sessionId);
    if (!session) {
      toast({
        title: "Error",
        description: "Failed to load session",
        variant: "destructive"
      });
      return;
    }

    // Load session data into context
    dispatch({ type: 'SET_CONFIG', payload: session.config });
    dispatch({ type: 'SET_TARGET_PRICE_ROWS', payload: session.targetPriceRows });
    dispatch({ type: 'CLEAR_ORDER_HISTORY' });
    session.orderHistory.forEach(entry => {
      dispatch({ type: 'ADD_ORDER_HISTORY_ENTRY', payload: entry });
    });
    dispatch({ type: 'SET_MARKET_PRICE', payload: session.currentMarketPrice });
    dispatch({ type: 'SET_BALANCES', payload: { 
      crypto1: session.crypto1Balance, 
      crypto2: session.crypto2Balance 
    }});

    sessionManager.setCurrentSession(sessionId);
    setCurrentSessionId(sessionId);
    loadSessions();

    toast({
      title: "Session Loaded",
      description: `Session "${session.name}" has been loaded`,
    });
  };

  const handleDeleteSession = (sessionId: string) => {
    const success = sessionManager.deleteSession(sessionId);
    if (success) {
      if (currentSessionId === sessionId) {
        setCurrentSessionId(null);
      }
      loadSessions();
      toast({
        title: "Session Deleted",
        description: "Session has been deleted successfully",
      });
    }
  };

  const handleRenameSession = (sessionId: string) => {
    if (!editingName.trim()) return;
    
    const success = sessionManager.renameSession(sessionId, editingName.trim());
    if (success) {
      setEditingSessionId(null);
      setEditingName('');
      loadSessions();
      toast({
        title: "Session Renamed",
        description: "Session has been renamed successfully",
      });
    }
  };

  const handleExportSession = (sessionId: string) => {
    const csvContent = sessionManager.exportSessionToCSV(sessionId);
    if (!csvContent) {
      toast({
        title: "Error",
        description: "Failed to export session",
        variant: "destructive"
      });
      return;
    }

    const session = sessionManager.loadSession(sessionId);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${session?.name || 'session'}_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "Export Complete",
      description: "Session data has been exported to CSV",
    });
  };

  const handleOpenAlarmModal = (sessionId: string, sessionName: string) => {
    setSelectedAlarmSession({ id: sessionId, name: sessionName });
    setAlarmModalOpen(true);
  };

  const handleCloseAlarmModal = () => {
    setAlarmModalOpen(false);
    setSelectedAlarmSession(null);
  };

  const formatRuntime = (runtime?: number) => {
    if (!runtime || runtime < 0) return '0s';

    const totalSeconds = Math.floor(runtime / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  };

  const getCurrentSession = () => {
    return sessions.find(s => s.id === currentSessionId);
  };

  const getActiveSessions = () => {
    return sessions.filter(s => s.isActive);
  };

  const getInactiveSessions = () => {
    return sessions.filter(s => !s.isActive);
  };

  return (
    <div className="space-y-6">
      {/* Current Sessions */}
      <Card className="bg-card-foreground/5 border-border border-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Current Sessions
          </CardTitle>
        </CardHeader>
        <CardContent>
          {getActiveSessions().length > 0 ? (
            <div className="space-y-4">
              {/* Table Header */}
              <div className="grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground">
                <div>Session Name</div>
                <div>Active Status</div>
                <div>Runtime</div>
                <div>Actions</div>
              </div>

              {/* Active Sessions Rows */}
              <div className="space-y-2">
                {getActiveSessions().map((session) => (
                  <div key={session.id} className="grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0">
                    <div className="flex items-center gap-2">
                      {editingSessionId === session.id ? (
                        <div className="flex gap-2 flex-1">
                          <Input
                            value={editingName}
                            onChange={(e) => setEditingName(e.target.value)}
                            onKeyPress={(e) => e.key === 'Enter' && handleRenameSession(session.id)}
                            className="text-sm"
                          />
                          <Button size="sm" onClick={() => handleRenameSession(session.id)}>
                            <Save className="h-3 w-3" />
                          </Button>
                        </div>
                      ) : (
                        <>
                          <span className="font-medium">{session.name}</span>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => {
                              setEditingSessionId(session.id);
                              setEditingName(session.name);
                            }}
                          >
                            <Edit2 className="h-3 w-3" />
                          </Button>
                        </>
                      )}
                    </div>

                    <div>
                      <Badge variant="default">
                        Active
                      </Badge>
                    </div>

                    <div className="text-sm">
                      {session.id === currentSessionId ? formatRuntime(currentRuntime) : formatRuntime(session.runtime)}
                    </div>

                    <div>
                      {session.id === currentSessionId ? (
                        <div className="flex gap-1">
                          <Button onClick={handleSaveCurrentSession} size="sm" className="btn-neo">
                            <Save className="mr-2 h-3 w-3" />
                            Save
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleOpenAlarmModal(session.id, session.name)}
                            title="Configure Alarms"
                          >
                            <Volume2 className="h-3 w-3" />
                          </Button>
                        </div>
                      ) : (
                        <div className="flex gap-1">
                          <Button size="sm" variant="outline" onClick={() => handleLoadSession(session.id)} title="Load Session">
                            <FolderOpen className="h-3 w-3" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => handleExportSession(session.id)} title="Export Session">
                            <Download className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleOpenAlarmModal(session.id, session.name)}
                            title="Configure Alarms"
                          >
                            <Volume2 className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center text-muted-foreground py-8">
              <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No active session</p>
              <p className="text-xs">Start trading to create a session automatically</p>
            </div>
          )}
        </CardContent>
      </Card>



      {/* Past Sessions */}
      <Card className="bg-card-foreground/5 border-border border-2">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Past Sessions ({getInactiveSessions().length})
          </CardTitle>
          <CardDescription>
            Auto-saved: {getInactiveSessions().length} | Manual: 0
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px]">
            {getInactiveSessions().length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No saved sessions yet.</p>
                <p className="text-xs">Save your current session to get started.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Table Header */}
                <div className="grid grid-cols-4 gap-4 pb-2 border-b border-border text-sm font-medium text-muted-foreground">
                  <div>Session Name</div>
                  <div>Active Status</div>
                  <div>Total Runtime</div>
                  <div>Actions</div>
                </div>

                {/* Past Sessions Rows */}
                <div className="space-y-2">
                  {getInactiveSessions().map((session) => (
                    <div key={session.id} className="grid grid-cols-4 gap-4 items-center py-2 border-b border-border/50 last:border-b-0">
                      <div className="flex items-center gap-2">
                        {editingSessionId === session.id ? (
                          <div className="flex gap-2 flex-1">
                            <Input
                              value={editingName}
                              onChange={(e) => setEditingName(e.target.value)}
                              onKeyPress={(e) => e.key === 'Enter' && handleRenameSession(session.id)}
                              className="text-sm"
                            />
                            <Button size="sm" onClick={() => handleRenameSession(session.id)}>
                              <Save className="h-3 w-3" />
                            </Button>
                          </div>
                        ) : (
                          <>
                            <span className="font-medium">{session.name}</span>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => {
                                setEditingSessionId(session.id);
                                setEditingName(session.name);
                              }}
                            >
                              <Edit2 className="h-3 w-3" />
                            </Button>
                          </>
                        )}
                      </div>

                      <div>
                        <Badge variant="secondary">
                          Inactive
                        </Badge>
                      </div>

                      <div className="text-sm">
                        {formatRuntime(sessionManager.getCurrentRuntime(session.id))}
                      </div>

                      <div className="flex gap-1">
                        <Button size="sm" variant="outline" onClick={() => handleLoadSession(session.id)} title="Load Session">
                          <FolderOpen className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleExportSession(session.id)} title="Export Session">
                          <Download className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleOpenAlarmModal(session.id, session.name)}
                          title="Configure Alarms"
                        >
                          <Volume2 className="h-3 w-3" />
                        </Button>
                        <Button size="sm" variant="outline" onClick={() => handleDeleteSession(session.id)} title="Delete Session">
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Session Alarm Modal */}
      {selectedAlarmSession && (
        <SessionAlarmModal
          isOpen={alarmModalOpen}
          onClose={handleCloseAlarmModal}
          sessionId={selectedAlarmSession.id}
          sessionName={selectedAlarmSession.name}
        />
      )}
    </div>
  );
}
