"use client";

import React, { useState, useEffect, useRef } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Volume2, Upload, Play, Square } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface SessionAlarmModalProps {
  isOpen: boolean;
  onClose: () => void;
  sessionId: string;
  sessionName: string;
}

interface SessionAlarmSettings {
  successAlarmsEnabled: boolean;
  errorAlarmsEnabled: boolean;
  successSoundType: string;
  errorSoundType: string;
  customSuccessSoundDataUri?: string;
  customErrorSoundDataUri?: string;
}

const BUILT_IN_SOUNDS = [
  { value: 'notification', label: 'Default Notification' },
  { value: 'success', label: 'Success Chime' },
  { value: 'error', label: 'Error Alert' },
  { value: 'bell', label: 'Bell' },
  { value: 'custom', label: 'Custom Sound' }
];

const CUSTOM_SOUND_SUCCESS_VALUE = "custom_success";
const CUSTOM_SOUND_ERROR_VALUE = "custom_error";

export default function SessionAlarmModal({ isOpen, onClose, sessionId, sessionName }: SessionAlarmModalProps) {
  const { toast } = useToast();
  const [settings, setSettings] = useState<SessionAlarmSettings>({
    successAlarmsEnabled: true,
    errorAlarmsEnabled: true,
    successSoundType: 'notification',
    errorSoundType: 'error'
  });
  const [isPlaying, setIsPlaying] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // Load session-specific alarm settings
  useEffect(() => {
    if (isOpen && sessionId) {
      const savedSettings = localStorage.getItem(`session_alarm_${sessionId}`);
      if (savedSettings) {
        try {
          const parsed = JSON.parse(savedSettings);
          setSettings(parsed);
        } catch (error) {
          console.error('Failed to parse saved alarm settings:', error);
        }
      }
    }
  }, [isOpen, sessionId]);

  const handleSwitchChange = (key: keyof SessionAlarmSettings, checked: boolean) => {
    setSettings(prev => ({ ...prev, [key]: checked }));
  };

  const handleSelectChange = (key: keyof SessionAlarmSettings, value: string) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleFileUpload = (type: 'success' | 'error', event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('audio/')) {
      toast({
        title: "Invalid File",
        description: "Please select an audio file (MP3, WAV, etc.)",
        variant: "destructive"
      });
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast({
        title: "File Too Large",
        description: "Audio file must be smaller than 5MB",
        variant: "destructive"
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const dataUri = e.target?.result as string;
      if (type === 'success') {
        setSettings(prev => ({
          ...prev,
          successSoundType: CUSTOM_SOUND_SUCCESS_VALUE,
          customSuccessSoundDataUri: dataUri
        }));
      } else {
        setSettings(prev => ({
          ...prev,
          errorSoundType: CUSTOM_SOUND_ERROR_VALUE,
          customErrorSoundDataUri: dataUri
        }));
      }
      toast({
        title: "Sound Uploaded",
        description: `Custom ${type} sound has been uploaded successfully`,
      });
    };
    reader.readAsDataURL(file);
  };

  const playTestSound = (soundType: string, isSuccess: boolean) => {
    if (isPlaying) {
      stopTestSound();
      return;
    }

    let soundUrl = '';
    
    if (soundType === CUSTOM_SOUND_SUCCESS_VALUE && settings.customSuccessSoundDataUri) {
      soundUrl = settings.customSuccessSoundDataUri;
    } else if (soundType === CUSTOM_SOUND_ERROR_VALUE && settings.customErrorSoundDataUri) {
      soundUrl = settings.customErrorSoundDataUri;
    } else {
      // Use built-in sounds
      soundUrl = `/sounds/${soundType}.mp3`;
    }

    if (audioRef.current) {
      audioRef.current.src = soundUrl;
      audioRef.current.play().then(() => {
        setIsPlaying(soundType);
      }).catch(error => {
        console.error('Failed to play sound:', error);
        toast({
          title: "Playback Error",
          description: "Failed to play the selected sound",
          variant: "destructive"
        });
      });
    }
  };

  const stopTestSound = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }
    setIsPlaying(null);
  };

  const handleSave = () => {
    // Save settings to localStorage with session-specific key
    localStorage.setItem(`session_alarm_${sessionId}`, JSON.stringify(settings));
    
    toast({
      title: "Alarm Settings Saved",
      description: `Alarm preferences for "${sessionName}" have been updated.`
    });
    
    onClose();
  };

  useEffect(() => {
    if (typeof window !== "undefined") {
      audioRef.current = new Audio();
      audioRef.current.onended = () => setIsPlaying(null);
    }
    
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Volume2 className="h-5 w-5" />
            Session Alarm Settings
          </DialogTitle>
          <DialogDescription>
            Configure success and error alarms for session: <strong>{sessionName}</strong>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Success Alarms */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="success-alarms" className="text-base font-medium">Success Alarms</Label>
                <p className="text-sm text-muted-foreground">Play sound when trades execute successfully</p>
              </div>
              <Switch
                id="success-alarms"
                checked={settings.successAlarmsEnabled}
                onCheckedChange={(checked) => handleSwitchChange('successAlarmsEnabled', checked)}
              />
            </div>

            {settings.successAlarmsEnabled && (
              <div className="space-y-3 pl-4 border-l-2 border-green-500">
                <div>
                  <Label htmlFor="success-sound">Success Sound</Label>
                  <div className="flex gap-2 mt-1">
                    <Select
                      value={settings.successSoundType}
                      onValueChange={(value) => handleSelectChange('successSoundType', value)}
                    >
                      <SelectTrigger className="flex-1">
                        <SelectValue placeholder="Select sound" />
                      </SelectTrigger>
                      <SelectContent>
                        {BUILT_IN_SOUNDS.map(sound => (
                          <SelectItem key={sound.value} value={sound.value}>
                            {sound.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => playTestSound(settings.successSoundType, true)}
                      disabled={isPlaying !== null}
                    >
                      {isPlaying === settings.successSoundType ? <Square className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>

                {settings.successSoundType === 'custom' && (
                  <div>
                    <Label htmlFor="success-upload">Upload Custom Success Sound</Label>
                    <div className="flex gap-2 mt-1">
                      <input
                        id="success-upload"
                        type="file"
                        accept="audio/*"
                        onChange={(e) => handleFileUpload('success', e)}
                        className="hidden"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => document.getElementById('success-upload')?.click()}
                        className="flex-1"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        {settings.customSuccessSoundDataUri ? 'Change Sound' : 'Upload Sound'}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Error Alarms */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="error-alarms" className="text-base font-medium">Error Alarms</Label>
                <p className="text-sm text-muted-foreground">Play sound when errors occur</p>
              </div>
              <Switch
                id="error-alarms"
                checked={settings.errorAlarmsEnabled}
                onCheckedChange={(checked) => handleSwitchChange('errorAlarmsEnabled', checked)}
              />
            </div>

            {settings.errorAlarmsEnabled && (
              <div className="space-y-3 pl-4 border-l-2 border-red-500">
                <div>
                  <Label htmlFor="error-sound">Error Sound</Label>
                  <div className="flex gap-2 mt-1">
                    <Select
                      value={settings.errorSoundType}
                      onValueChange={(value) => handleSelectChange('errorSoundType', value)}
                    >
                      <SelectTrigger className="flex-1">
                        <SelectValue placeholder="Select sound" />
                      </SelectTrigger>
                      <SelectContent>
                        {BUILT_IN_SOUNDS.map(sound => (
                          <SelectItem key={sound.value} value={sound.value}>
                            {sound.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => playTestSound(settings.errorSoundType, false)}
                      disabled={isPlaying !== null}
                    >
                      {isPlaying === settings.errorSoundType ? <Square className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>

                {settings.errorSoundType === 'custom' && (
                  <div>
                    <Label htmlFor="error-upload">Upload Custom Error Sound</Label>
                    <div className="flex gap-2 mt-1">
                      <input
                        id="error-upload"
                        type="file"
                        accept="audio/*"
                        onChange={(e) => handleFileUpload('error', e)}
                        className="hidden"
                      />
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => document.getElementById('error-upload')?.click()}
                        className="flex-1"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        {settings.customErrorSoundDataUri ? 'Change Sound' : 'Upload Sound'}
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">Cancel</Button>
          </DialogClose>
          <Button onClick={handleSave} className="btn-neo">
            Save Alarm Settings
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
