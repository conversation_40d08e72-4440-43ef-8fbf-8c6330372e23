# Pluto 2.0 Trading Bot

A comprehensive trading bot application with frontend and backend components.

## Project Structure

- **Frontend**: Next.js application with Tailwind CSS
- **Backend**: Python Flask API for trading operations

## Setup Instructions

### Backend Setup

1. Navigate to the backend directory:
   ```
   cd backend
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   ```

3. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - Unix/MacOS: `source venv/bin/activate`

4. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

5. Run the backend server:
   ```
   python run.py
   ```

### Frontend Setup

1. Navigate to the frontend directory:
   ```
   cd frontend
   ```

2. Install dependencies:
   ```
   npm install
   ```

3. Run the development server:
   ```
   npm run dev
   ```

## Features

- Real-time trading data visualization
- Automated trading strategies
- User authentication and portfolio management

## Technologies Used

- **Frontend**: Next.js, React, Tailwind CSS
- **Backend**: Python, Flask, SQLAlchemy 